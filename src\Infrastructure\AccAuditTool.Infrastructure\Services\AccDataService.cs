using AccAuditTool.Application.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Implementation of ACC data extraction service using APS APIs
/// </summary>
public class AccDataService : IAccDataService
{
    private readonly IApsApiClient _apiClient;
    private readonly ILogger<AccDataService> _logger;

    public AccDataService(IApsApiClient apiClient, ILogger<AccDataService> logger)
    {
        _apiClient = apiClient;
        _logger = logger;
    }

    public async Task<IEnumerable<AccAccountDto>> GetAccountsAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching accessible accounts from ACC");

        try
        {
            var response = await _apiClient.GetAsync<AccountsResponse>("/hq/v1/accounts", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No accounts data received from API");
                return Enumerable.Empty<AccAccountDto>();
            }

            var accounts = response.Data.Select(account => new AccAccountDto
            {
                Id = account.Id,
                Name = account.Name,
                Region = account.Region,
                SubscriptionType = account.SubscriptionType
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} accounts", accounts.Count);
            return accounts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching accounts from ACC");
            throw new InvalidOperationException("Failed to fetch accounts from ACC", ex);
        }
    }

    public async Task<IEnumerable<AccProjectDto>> GetProjectsAsync(string accountId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching projects for account {AccountId}", accountId);

        try
        {
            var response = await _apiClient.GetAsync<ProjectsResponse>($"/hq/v1/accounts/{accountId}/projects", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No projects data received for account {AccountId}", accountId);
                return Enumerable.Empty<AccProjectDto>();
            }

            var projects = response.Data.Select(project => new AccProjectDto
            {
                Id = project.Id,
                Name = project.Name,
                Description = project.Description,
                AccountId = accountId,
                Status = project.Status,
                ProjectType = project.ProjectType,
                StartDate = project.StartDate,
                EndDate = project.EndDate
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} projects for account {AccountId}", projects.Count, accountId);
            return projects;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching projects for account {AccountId}", accountId);
            throw new InvalidOperationException($"Failed to fetch projects for account {accountId}", ex);
        }
    }

    public async Task<IEnumerable<AccUserDto>> GetProjectUsersAsync(string projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching users for project {ProjectId}", projectId);

        try
        {
            var response = await _apiClient.GetAsync<UsersResponse>($"/hq/v1/accounts/{GetAccountIdFromProject(projectId)}/projects/{projectId}/users", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No users data received for project {ProjectId}", projectId);
                return Enumerable.Empty<AccUserDto>();
            }

            var users = new List<AccUserDto>();
            
            foreach (var user in response.Data)
            {
                var userDto = new AccUserDto
                {
                    Id = user.Id,
                    Email = user.Email,
                    Name = user.Name,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    CompanyId = user.CompanyId,
                    CompanyName = user.CompanyName,
                    Status = user.Status,
                    LastSeenAt = user.LastSeenAt
                };

                // Fetch user roles for this project
                userDto.Roles = await GetUserRolesAsync(projectId, user.Id, cancellationToken);
                users.Add(userDto);
            }

            _logger.LogInformation("Successfully fetched {Count} users for project {ProjectId}", users.Count, projectId);
            return users;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching users for project {ProjectId}", projectId);
            throw new InvalidOperationException($"Failed to fetch users for project {projectId}", ex);
        }
    }

    public async Task<AccUserDto?> GetUserDetailsAsync(string userId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching details for user {UserId}", userId);

        try
        {
            var response = await _apiClient.GetAsync<UserDetailsResponse>($"/hq/v1/accounts/users/{userId}", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No user details received for user {UserId}", userId);
                return null;
            }

            var user = response.Data;
            return new AccUserDto
            {
                Id = user.Id,
                Email = user.Email,
                Name = user.Name,
                FirstName = user.FirstName,
                LastName = user.LastName,
                CompanyId = user.CompanyId,
                CompanyName = user.CompanyName,
                Status = user.Status,
                LastSeenAt = user.LastSeenAt
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching user details for {UserId}", userId);
            throw new InvalidOperationException($"Failed to fetch user details for {userId}", ex);
        }
    }

    public async Task<IEnumerable<AccPermissionDto>> GetFolderPermissionsAsync(string projectId, string folderId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching permissions for folder {FolderId} in project {ProjectId}", folderId, projectId);

        try
        {
            var response = await _apiClient.GetAsync<PermissionsResponse>($"/data/v1/projects/{projectId}/folders/{folderId}/permissions", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No permissions data received for folder {FolderId}", folderId);
                return Enumerable.Empty<AccPermissionDto>();
            }

            var permissions = response.Data.Select(permission => new AccPermissionDto
            {
                SubjectType = permission.SubjectType,
                SubjectId = permission.SubjectId,
                ResourceId = folderId,
                ResourceType = "folder",
                Actions = permission.Actions ?? new List<string>(),
                InheritedFrom = permission.InheritedFrom
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} permissions for folder {FolderId}", permissions.Count, folderId);
            return permissions;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching permissions for folder {FolderId} in project {ProjectId}", folderId, projectId);
            throw new InvalidOperationException($"Failed to fetch permissions for folder {folderId}", ex);
        }
    }

    public async Task<IEnumerable<AccResourceDto>> GetProjectFoldersAsync(string projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching folders for project {ProjectId}", projectId);

        try
        {
            var response = await _apiClient.GetAsync<FoldersResponse>($"/data/v1/projects/{projectId}/folders", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No folders data received for project {ProjectId}", projectId);
                return Enumerable.Empty<AccResourceDto>();
            }

            var folders = response.Data.Select(folder => new AccResourceDto
            {
                Id = folder.Id,
                Name = folder.Name,
                Path = folder.Path ?? $"/{folder.Name}",
                Type = "folder",
                ParentId = folder.ParentId,
                LastModifiedAt = folder.LastModifiedAt,
                LastModifiedBy = folder.LastModifiedBy
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} folders for project {ProjectId}", folders.Count, projectId);
            return folders;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching folders for project {ProjectId}", projectId);
            throw new InvalidOperationException($"Failed to fetch folders for project {projectId}", ex);
        }
    }

    public async Task<IEnumerable<AccCompanyDto>> GetProjectCompaniesAsync(string projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching companies for project {ProjectId}", projectId);

        try
        {
            var response = await _apiClient.GetAsync<CompaniesResponse>($"/hq/v1/accounts/{GetAccountIdFromProject(projectId)}/projects/{projectId}/companies", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No companies data received for project {ProjectId}", projectId);
                return Enumerable.Empty<AccCompanyDto>();
            }

            var companies = response.Data.Select(company => new AccCompanyDto
            {
                Id = company.Id,
                Name = company.Name,
                Trade = company.Trade,
                Address = company.Address,
                Phone = company.Phone
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} companies for project {ProjectId}", companies.Count, projectId);
            return companies;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching companies for project {ProjectId}", projectId);
            throw new InvalidOperationException($"Failed to fetch companies for project {projectId}", ex);
        }
    }

    public async Task<IEnumerable<AccRoleDto>> GetProjectRolesAsync(string projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Fetching roles for project {ProjectId}", projectId);

        try
        {
            var response = await _apiClient.GetAsync<RolesResponse>($"/hq/v1/accounts/{GetAccountIdFromProject(projectId)}/projects/{projectId}/industry_roles", cancellationToken);
            
            if (response?.Data == null)
            {
                _logger.LogWarning("No roles data received for project {ProjectId}", projectId);
                return Enumerable.Empty<AccRoleDto>();
            }

            var roles = response.Data.Select(role => new AccRoleDto
            {
                Id = role.Id,
                Name = role.Name,
                Description = role.Description,
                ServiceKey = role.ServiceKey,
                Permissions = role.Permissions ?? new List<string>()
            }).ToList();

            _logger.LogInformation("Successfully fetched {Count} roles for project {ProjectId}", roles.Count, projectId);
            return roles;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching roles for project {ProjectId}", projectId);
            throw new InvalidOperationException($"Failed to fetch roles for project {projectId}", ex);
        }
    }

    private async Task<List<AccUserRoleDto>> GetUserRolesAsync(string projectId, string userId, CancellationToken cancellationToken)
    {
        try
        {
            var response = await _apiClient.GetAsync<UserRolesResponse>($"/hq/v1/accounts/{GetAccountIdFromProject(projectId)}/projects/{projectId}/users/{userId}/industry_roles", cancellationToken);
            
            if (response?.Data == null)
                return new List<AccUserRoleDto>();

            return response.Data.Select(role => new AccUserRoleDto
            {
                RoleId = role.Id,
                RoleName = role.Name,
                ServiceKey = role.ServiceKey,
                AssignedAt = role.AssignedAt ?? DateTime.UtcNow
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error fetching roles for user {UserId} in project {ProjectId}", userId, projectId);
            return new List<AccUserRoleDto>();
        }
    }

    private string GetAccountIdFromProject(string projectId)
    {
        // In a real implementation, you would need to maintain a mapping or extract from project details
        // For now, this is a placeholder that would need to be implemented based on your data structure
        return "placeholder-account-id";
    }
}
