using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Represents a calculated risk score for an entity
/// </summary>
public class RiskScore
{
    /// <summary>
    /// Type of entity (User, Project, Company, etc.)
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Unique identifier of the entity
    /// </summary>
    public string EntityId { get; set; } = string.Empty;

    /// <summary>
    /// Calculated risk score (0-100)
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// Risk level based on the score
    /// </summary>
    public RiskLevel Level { get; set; }

    /// <summary>
    /// Confidence in the risk assessment (0-1)
    /// </summary>
    public double Confidence { get; set; }

    /// <summary>
    /// When the risk score was calculated
    /// </summary>
    public DateTime CalculatedAt { get; set; }

    /// <summary>
    /// Breakdown of risk scores by component/category
    /// </summary>
    public Dictionary<string, double> ComponentScores { get; set; } = new();

    /// <summary>
    /// Additional metadata about the risk calculation
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();

    /// <summary>
    /// Risk factors that contributed to the score
    /// </summary>
    public List<RiskFactor> RiskFactors { get; set; } = new();
}

/// <summary>
/// Risk levels for categorizing risk scores
/// </summary>
public enum RiskLevel
{
    Minimal = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Represents a specific risk factor
/// </summary>
public class RiskFactor
{
    /// <summary>
    /// Name of the risk factor
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the risk factor
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Impact of this factor on the overall risk score
    /// </summary>
    public double Impact { get; set; }

    /// <summary>
    /// Category of the risk factor
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Evidence supporting this risk factor
    /// </summary>
    public object? Evidence { get; set; }
}

/// <summary>
/// Represents risk trends over time
/// </summary>
public class RiskTrend
{
    /// <summary>
    /// Type of entity being tracked
    /// </summary>
    public string EntityType { get; set; } = string.Empty;

    /// <summary>
    /// Unique identifier of the entity
    /// </summary>
    public string EntityId { get; set; } = string.Empty;

    /// <summary>
    /// Time period covered by the trend
    /// </summary>
    public TimeSpan Period { get; set; }

    /// <summary>
    /// Individual data points in the trend
    /// </summary>
    public List<RiskTrendPoint> TrendPoints { get; set; } = new();

    /// <summary>
    /// Overall direction of the trend
    /// </summary>
    public TrendDirection Direction { get; set; }

    /// <summary>
    /// Percentage change from start to end of period
    /// </summary>
    public double ChangePercentage { get; set; }

    /// <summary>
    /// When the trend was calculated
    /// </summary>
    public DateTime CalculatedAt { get; set; }

    /// <summary>
    /// Statistical information about the trend
    /// </summary>
    public TrendStatistics Statistics { get; set; } = new();
}

/// <summary>
/// Individual point in a risk trend
/// </summary>
public class RiskTrendPoint
{
    /// <summary>
    /// Date of this data point
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Risk score at this point in time
    /// </summary>
    public double Score { get; set; }

    /// <summary>
    /// Risk level at this point in time
    /// </summary>
    public RiskLevel Level { get; set; }

    /// <summary>
    /// Number of findings at this point
    /// </summary>
    public int FindingCount { get; set; }

    /// <summary>
    /// Additional context for this data point
    /// </summary>
    public Dictionary<string, object> Context { get; set; } = new();
}

/// <summary>
/// Direction of a risk trend
/// </summary>
public enum TrendDirection
{
    Insufficient,  // Not enough data points
    Decreasing,    // Risk is decreasing
    Stable,        // Risk is stable
    Increasing     // Risk is increasing
}

/// <summary>
/// Statistical information about a trend
/// </summary>
public class TrendStatistics
{
    /// <summary>
    /// Average risk score over the period
    /// </summary>
    public double Average { get; set; }

    /// <summary>
    /// Minimum risk score in the period
    /// </summary>
    public double Minimum { get; set; }

    /// <summary>
    /// Maximum risk score in the period
    /// </summary>
    public double Maximum { get; set; }

    /// <summary>
    /// Standard deviation of risk scores
    /// </summary>
    public double StandardDeviation { get; set; }

    /// <summary>
    /// Volatility of the risk score (coefficient of variation)
    /// </summary>
    public double Volatility { get; set; }
}

/// <summary>
/// Configuration for risk scoring calculations
/// </summary>
public class RiskScoringConfiguration
{
    /// <summary>
    /// Weights for different severity levels
    /// </summary>
    public Dictionary<AuditSeverity, double> SeverityWeights { get; set; } = new();

    /// <summary>
    /// Weights for different finding categories
    /// </summary>
    public Dictionary<AuditFindingCategory, double> CategoryWeights { get; set; } = new();

    /// <summary>
    /// Multipliers for different entity types
    /// </summary>
    public Dictionary<string, double> EntityTypeMultipliers { get; set; } = new();

    /// <summary>
    /// Temporal decay factors for aging findings
    /// </summary>
    public Dictionary<int, double> TemporalDecayFactors { get; set; } = new();

    /// <summary>
    /// Thresholds for risk level classification
    /// </summary>
    public RiskLevelThresholds Thresholds { get; set; } = new();

    /// <summary>
    /// Maximum age of findings to consider (in days)
    /// </summary>
    public int MaxFindingAgeDays { get; set; } = 365;

    /// <summary>
    /// Whether to apply frequency-based scoring
    /// </summary>
    public bool EnableFrequencyScoring { get; set; } = true;

    /// <summary>
    /// Whether to apply temporal decay
    /// </summary>
    public bool EnableTemporalDecay { get; set; } = true;
}

/// <summary>
/// Thresholds for determining risk levels
/// </summary>
public class RiskLevelThresholds
{
    /// <summary>
    /// Minimum score for Low risk level
    /// </summary>
    public double LowThreshold { get; set; } = 20;

    /// <summary>
    /// Minimum score for Medium risk level
    /// </summary>
    public double MediumThreshold { get; set; } = 40;

    /// <summary>
    /// Minimum score for High risk level
    /// </summary>
    public double HighThreshold { get; set; } = 60;

    /// <summary>
    /// Minimum score for Critical risk level
    /// </summary>
    public double CriticalThreshold { get; set; } = 80;
}

/// <summary>
/// Risk assessment summary for reporting
/// </summary>
public class RiskAssessmentSummary
{
    /// <summary>
    /// Overall risk score
    /// </summary>
    public RiskScore OverallRisk { get; set; } = new();

    /// <summary>
    /// Risk scores by entity type
    /// </summary>
    public Dictionary<string, RiskScore> EntityTypeRisks { get; set; } = new();

    /// <summary>
    /// Top risk factors
    /// </summary>
    public List<RiskFactor> TopRiskFactors { get; set; } = new();

    /// <summary>
    /// Risk distribution by level
    /// </summary>
    public Dictionary<RiskLevel, int> RiskDistribution { get; set; } = new();

    /// <summary>
    /// Trend information
    /// </summary>
    public RiskTrend Trend { get; set; } = new();

    /// <summary>
    /// Recommendations for risk mitigation
    /// </summary>
    public List<RiskMitigationRecommendation> Recommendations { get; set; } = new();

    /// <summary>
    /// When the assessment was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Risk mitigation recommendation
/// </summary>
public class RiskMitigationRecommendation
{
    /// <summary>
    /// Priority of the recommendation
    /// </summary>
    public RecommendationPriority Priority { get; set; }

    /// <summary>
    /// Title of the recommendation
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Expected impact on risk reduction
    /// </summary>
    public double ExpectedImpact { get; set; }

    /// <summary>
    /// Estimated effort to implement
    /// </summary>
    public ImplementationEffort Effort { get; set; }

    /// <summary>
    /// Risk factors this recommendation addresses
    /// </summary>
    public List<string> AddressedRiskFactors { get; set; } = new();
}

/// <summary>
/// Priority levels for recommendations
/// </summary>
public enum RecommendationPriority
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Implementation effort levels
/// </summary>
public enum ImplementationEffort
{
    Low = 1,
    Medium = 2,
    High = 3,
    VeryHigh = 4
}
