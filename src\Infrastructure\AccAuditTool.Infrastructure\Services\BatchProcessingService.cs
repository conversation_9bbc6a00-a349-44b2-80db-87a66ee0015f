using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for efficient batch processing of large datasets
/// </summary>
public interface IBatchProcessingService
{
    /// <summary>
    /// Process items in batches with parallel execution
    /// </summary>
    Task<BatchProcessingResult<TResult>> ProcessBatchAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process items in batches with parallel execution and progress reporting
    /// </summary>
    Task<BatchProcessingResult<TResult>> ProcessBatchAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        IProgress<BatchProgress>? progress,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Process items with retry logic for failed items
    /// </summary>
    Task<BatchProcessingResult<TResult>> ProcessBatchWithRetryAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Configuration options for batch processing
/// </summary>
public class BatchProcessingOptions
{
    /// <summary>
    /// Number of items to process in each batch
    /// </summary>
    public int BatchSize { get; set; } = 100;

    /// <summary>
    /// Maximum degree of parallelism
    /// </summary>
    public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Delay between batches to avoid overwhelming the system
    /// </summary>
    public TimeSpan DelayBetweenBatches { get; set; } = TimeSpan.FromMilliseconds(100);

    /// <summary>
    /// Maximum number of retry attempts for failed items
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Whether to continue processing if some items fail
    /// </summary>
    public bool ContinueOnError { get; set; } = true;

    /// <summary>
    /// Timeout for individual item processing
    /// </summary>
    public TimeSpan ItemTimeout { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Result of batch processing operation
/// </summary>
public class BatchProcessingResult<T>
{
    public List<T> SuccessfulResults { get; set; } = new();
    public List<BatchProcessingError> Errors { get; set; } = new();
    public int TotalItems { get; set; }
    public int SuccessfulItems => SuccessfulResults.Count;
    public int FailedItems => Errors.Count;
    public double SuccessRate => TotalItems > 0 ? (double)SuccessfulItems / TotalItems * 100 : 0;
    public TimeSpan TotalProcessingTime { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
}

/// <summary>
/// Error information for failed batch items
/// </summary>
public class BatchProcessingError
{
    public object? Item { get; set; }
    public Exception Exception { get; set; } = null!;
    public int AttemptCount { get; set; }
    public DateTime LastAttemptAt { get; set; }
}

/// <summary>
/// Progress information for batch processing
/// </summary>
public class BatchProgress
{
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public int SuccessfulItems { get; set; }
    public int FailedItems { get; set; }
    public double ProgressPercentage => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public string CurrentBatch { get; set; } = string.Empty;
}

/// <summary>
/// Implementation of batch processing service
/// </summary>
public class BatchProcessingService : IBatchProcessingService
{
    private readonly ILogger<BatchProcessingService> _logger;

    public BatchProcessingService(ILogger<BatchProcessingService> logger)
    {
        _logger = logger;
    }

    public async Task<BatchProcessingResult<TResult>> ProcessBatchAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        return await ProcessBatchAsync(items, processor, null, options, cancellationToken);
    }

    public async Task<BatchProcessingResult<TResult>> ProcessBatchAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        IProgress<BatchProgress>? progress,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        options ??= new BatchProcessingOptions();
        var itemList = items.ToList();
        var result = new BatchProcessingResult<TResult>
        {
            TotalItems = itemList.Count,
            StartedAt = DateTime.UtcNow
        };

        _logger.LogInformation("Starting batch processing of {TotalItems} items with batch size {BatchSize} and parallelism {Parallelism}",
            result.TotalItems, options.BatchSize, options.MaxDegreeOfParallelism);

        var progressInfo = new BatchProgress { TotalItems = result.TotalItems };
        var startTime = DateTime.UtcNow;

        try
        {
            var batches = itemList.Chunk(options.BatchSize).ToList();
            var batchNumber = 0;

            foreach (var batch in batches)
            {
                batchNumber++;
                progressInfo.CurrentBatch = $"Batch {batchNumber}/{batches.Count}";
                
                _logger.LogDebug("Processing batch {BatchNumber}/{TotalBatches} with {ItemCount} items",
                    batchNumber, batches.Count, batch.Length);

                var batchResults = await ProcessSingleBatchAsync(
                    batch, processor, options, cancellationToken);

                result.SuccessfulResults.AddRange(batchResults.SuccessfulResults);
                result.Errors.AddRange(batchResults.Errors);

                // Update progress
                progressInfo.ProcessedItems += batch.Length;
                progressInfo.SuccessfulItems = result.SuccessfulItems;
                progressInfo.FailedItems = result.FailedItems;
                progressInfo.ElapsedTime = DateTime.UtcNow - startTime;
                
                if (progressInfo.ProcessedItems > 0)
                {
                    var avgTimePerItem = progressInfo.ElapsedTime.TotalMilliseconds / progressInfo.ProcessedItems;
                    var remainingItems = progressInfo.TotalItems - progressInfo.ProcessedItems;
                    progressInfo.EstimatedTimeRemaining = TimeSpan.FromMilliseconds(avgTimePerItem * remainingItems);
                }

                progress?.Report(progressInfo);

                // Delay between batches if configured
                if (options.DelayBetweenBatches > TimeSpan.Zero && batchNumber < batches.Count)
                {
                    await Task.Delay(options.DelayBetweenBatches, cancellationToken);
                }
            }

            result.CompletedAt = DateTime.UtcNow;
            result.TotalProcessingTime = result.CompletedAt - result.StartedAt;

            _logger.LogInformation("Batch processing completed. Success rate: {SuccessRate:F1}% ({Successful}/{Total}), Total time: {TotalTime}ms",
                result.SuccessRate, result.SuccessfulItems, result.TotalItems, result.TotalProcessingTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during batch processing");
            result.CompletedAt = DateTime.UtcNow;
            result.TotalProcessingTime = result.CompletedAt - result.StartedAt;
            throw;
        }

        return result;
    }

    public async Task<BatchProcessingResult<TResult>> ProcessBatchWithRetryAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        BatchProcessingOptions? options = null,
        CancellationToken cancellationToken = default)
    {
        options ??= new BatchProcessingOptions();
        var itemList = items.ToList();
        var result = new BatchProcessingResult<TResult>
        {
            TotalItems = itemList.Count,
            StartedAt = DateTime.UtcNow
        };

        var failedItems = new List<(TItem Item, int AttemptCount)>();
        var currentItems = itemList.Select(item => (Item: item, AttemptCount: 0)).ToList();

        for (int attempt = 1; attempt <= options.MaxRetryAttempts + 1; attempt++)
        {
            if (!currentItems.Any()) break;

            _logger.LogInformation("Processing attempt {Attempt} with {ItemCount} items",
                attempt, currentItems.Count);

            var batchResult = await ProcessBatchAsync(
                currentItems.Select(x => x.Item),
                processor,
                options,
                cancellationToken);

            result.SuccessfulResults.AddRange(batchResult.SuccessfulResults);

            // Prepare failed items for retry
            failedItems.Clear();
            foreach (var error in batchResult.Errors)
            {
                var failedItem = currentItems.FirstOrDefault(x => x.Item?.Equals(error.Item) == true);
                if (failedItem.Item != null)
                {
                    failedItems.Add((failedItem.Item, attempt));
                }
            }

            // If this is the last attempt or no failures, add errors to final result
            if (attempt > options.MaxRetryAttempts || !failedItems.Any())
            {
                result.Errors.AddRange(batchResult.Errors);
                break;
            }

            // Prepare for retry
            currentItems = failedItems.Select(x => (x.Item, x.AttemptCount)).ToList();
            
            _logger.LogWarning("Retrying {FailedCount} failed items after {RetryDelay}ms delay",
                failedItems.Count, options.RetryDelay.TotalMilliseconds);

            await Task.Delay(options.RetryDelay, cancellationToken);
        }

        result.CompletedAt = DateTime.UtcNow;
        result.TotalProcessingTime = result.CompletedAt - result.StartedAt;

        return result;
    }

    private async Task<BatchProcessingResult<TResult>> ProcessSingleBatchAsync<TItem, TResult>(
        IEnumerable<TItem> batch,
        Func<TItem, CancellationToken, Task<TResult>> processor,
        BatchProcessingOptions options,
        CancellationToken cancellationToken)
    {
        var result = new BatchProcessingResult<TResult>();
        var results = new ConcurrentBag<TResult>();
        var errors = new ConcurrentBag<BatchProcessingError>();

        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = options.MaxDegreeOfParallelism,
            CancellationToken = cancellationToken
        };

        await Parallel.ForEachAsync(batch, parallelOptions, async (item, ct) =>
        {
            try
            {
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(ct);
                timeoutCts.CancelAfter(options.ItemTimeout);

                var itemResult = await processor(item, timeoutCts.Token);
                results.Add(itemResult);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error processing batch item: {Item}", item);
                errors.Add(new BatchProcessingError
                {
                    Item = item,
                    Exception = ex,
                    AttemptCount = 1,
                    LastAttemptAt = DateTime.UtcNow
                });

                if (!options.ContinueOnError)
                {
                    throw;
                }
            }
        });

        result.SuccessfulResults.AddRange(results);
        result.Errors.AddRange(errors);

        return result;
    }
}
