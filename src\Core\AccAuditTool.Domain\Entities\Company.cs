namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a company/organization in the ACC system
/// </summary>
public class Company : BaseEntity
{
    /// <summary>
    /// ACC company identifier from Autodesk system
    /// </summary>
    public string AccCompanyId { get; set; } = string.Empty;

    /// <summary>
    /// Company name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Company trade/industry type
    /// </summary>
    public string? Trade { get; set; }

    /// <summary>
    /// Company address
    /// </summary>
    public string? Address { get; set; }

    /// <summary>
    /// Company phone number
    /// </summary>
    public string? Phone { get; set; }

    /// <summary>
    /// Company website URL
    /// </summary>
    public string? Website { get; set; }

    /// <summary>
    /// Company status in the system
    /// </summary>
    public CompanyStatus Status { get; set; } = CompanyStatus.Active;

    /// <summary>
    /// Users belonging to this company
    /// </summary>
    public ICollection<User> Users { get; set; } = new List<User>();

    /// <summary>
    /// Projects this company is involved in
    /// </summary>
    public ICollection<ProjectCompany> ProjectCompanies { get; set; } = new List<ProjectCompany>();

    /// <summary>
    /// Company-level permissions
    /// </summary>
    public ICollection<Permission> CompanyPermissions { get; set; } = new List<Permission>();
}

/// <summary>
/// Company status enumeration
/// </summary>
public enum CompanyStatus
{
    Active,
    Inactive,
    Suspended
}
