using System.Text.Json.Serialization;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Base response model for APS API responses
/// </summary>
public class BaseApiResponse<T>
{
    [JsonPropertyName("data")]
    public T? Data { get; set; }

    [JsonPropertyName("links")]
    public ApiLinks? Links { get; set; }

    [JsonPropertyName("meta")]
    public ApiMeta? Meta { get; set; }
}

/// <summary>
/// API pagination links
/// </summary>
public class ApiLinks
{
    [JsonPropertyName("self")]
    public string? Self { get; set; }

    [JsonPropertyName("first")]
    public string? First { get; set; }

    [JsonPropertyName("prev")]
    public string? Prev { get; set; }

    [JsonPropertyName("next")]
    public string? Next { get; set; }

    [JsonPropertyName("last")]
    public string? Last { get; set; }
}

/// <summary>
/// API response metadata
/// </summary>
public class ApiMeta
{
    [JsonPropertyName("total_results")]
    public int TotalResults { get; set; }

    [Json<PERSON>ropertyName("partial_results")]
    public bool PartialResults { get; set; }
}

// Response type aliases
public class AccountsResponse : BaseApiResponse<List<AccountData>> { }
public class ProjectsResponse : BaseApiResponse<List<ProjectData>> { }
public class UsersResponse : BaseApiResponse<List<UserData>> { }
public class UserDetailsResponse : BaseApiResponse<UserData> { }
public class CompaniesResponse : BaseApiResponse<List<CompanyData>> { }
public class RolesResponse : BaseApiResponse<List<RoleData>> { }
public class UserRolesResponse : BaseApiResponse<List<UserRoleData>> { }
public class FoldersResponse : BaseApiResponse<List<FolderData>> { }
public class PermissionsResponse : BaseApiResponse<List<PermissionData>> { }

/// <summary>
/// Account data from APS API
/// </summary>
public class AccountData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("region")]
    public string? Region { get; set; }

    [JsonPropertyName("subscription_type")]
    public string? SubscriptionType { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Project data from APS API
/// </summary>
public class ProjectData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("project_type")]
    public string? ProjectType { get; set; }

    [JsonPropertyName("start_date")]
    public DateTime? StartDate { get; set; }

    [JsonPropertyName("end_date")]
    public DateTime? EndDate { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// User data from APS API
/// </summary>
public class UserData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("first_name")]
    public string? FirstName { get; set; }

    [JsonPropertyName("last_name")]
    public string? LastName { get; set; }

    [JsonPropertyName("company_id")]
    public string? CompanyId { get; set; }

    [JsonPropertyName("company_name")]
    public string? CompanyName { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("last_seen_at")]
    public DateTime? LastSeenAt { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("updated_at")]
    public DateTime? UpdatedAt { get; set; }
}

/// <summary>
/// Company data from APS API
/// </summary>
public class CompanyData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("trade")]
    public string? Trade { get; set; }

    [JsonPropertyName("address")]
    public string? Address { get; set; }

    [JsonPropertyName("phone")]
    public string? Phone { get; set; }

    [JsonPropertyName("website")]
    public string? Website { get; set; }

    [JsonPropertyName("status")]
    public string? Status { get; set; }
}

/// <summary>
/// Role data from APS API
/// </summary>
public class RoleData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("description")]
    public string? Description { get; set; }

    [JsonPropertyName("service_key")]
    public string? ServiceKey { get; set; }

    [JsonPropertyName("permissions")]
    public List<string>? Permissions { get; set; }

    [JsonPropertyName("created_at")]
    public DateTime? CreatedAt { get; set; }
}

/// <summary>
/// User role assignment data from APS API
/// </summary>
public class UserRoleData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("service_key")]
    public string? ServiceKey { get; set; }

    [JsonPropertyName("assigned_at")]
    public DateTime? AssignedAt { get; set; }

    [JsonPropertyName("assigned_by")]
    public string? AssignedBy { get; set; }
}

/// <summary>
/// Folder data from APS API
/// </summary>
public class FolderData
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("path")]
    public string? Path { get; set; }

    [JsonPropertyName("parent_id")]
    public string? ParentId { get; set; }

    [JsonPropertyName("last_modified_time")]
    public DateTime? LastModifiedAt { get; set; }

    [JsonPropertyName("last_modified_user_name")]
    public string? LastModifiedBy { get; set; }

    [JsonPropertyName("created_time")]
    public DateTime? CreatedAt { get; set; }

    [JsonPropertyName("created_user_name")]
    public string? CreatedBy { get; set; }
}

/// <summary>
/// Permission data from APS API
/// </summary>
public class PermissionData
{
    [JsonPropertyName("subject_type")]
    public string SubjectType { get; set; } = string.Empty;

    [JsonPropertyName("subject_id")]
    public string SubjectId { get; set; } = string.Empty;

    [JsonPropertyName("actions")]
    public List<string>? Actions { get; set; }

    [JsonPropertyName("inherited_from")]
    public string? InheritedFrom { get; set; }

    [JsonPropertyName("granted_at")]
    public DateTime? GrantedAt { get; set; }

    [JsonPropertyName("granted_by")]
    public string? GrantedBy { get; set; }
}
