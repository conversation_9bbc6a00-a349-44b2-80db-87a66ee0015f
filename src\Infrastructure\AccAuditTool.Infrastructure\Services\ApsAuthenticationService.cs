using AccAuditTool.Infrastructure.Configuration;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for managing APS OAuth 2.0 authentication and token lifecycle
/// </summary>
public interface IApsAuthenticationService
{
    /// <summary>
    /// Get a valid access token for API calls
    /// </summary>
    Task<string> GetAccessTokenAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get access token for a specific user (3-legged OAuth)
    /// </summary>
    Task<string> GetUserAccessTokenAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Refresh an expired access token
    /// </summary>
    Task<string> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate if a token is still valid
    /// </summary>
    Task<bool> ValidateTokenAsync(string accessToken, CancellationToken cancellationToken = default);

    /// <summary>
    /// Revoke an access token
    /// </summary>
    Task<bool> RevokeTokenAsync(string accessToken, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of APS authentication service
/// </summary>
public class ApsAuthenticationService : IApsAuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly ApsApiOptions _options;
    private readonly IMemoryCache _cache;
    private readonly ILogger<ApsAuthenticationService> _logger;
    private readonly JwtSecurityTokenHandler _jwtHandler;

    private const string AppTokenCacheKey = "aps_app_token";
    private const string UserTokenCacheKeyPrefix = "aps_user_token_";

    public ApsAuthenticationService(
        HttpClient httpClient,
        IOptions<ApsApiOptions> options,
        IMemoryCache cache,
        ILogger<ApsAuthenticationService> logger)
    {
        _httpClient = httpClient;
        _options = options.Value;
        _cache = cache;
        _logger = logger;
        _jwtHandler = new JwtSecurityTokenHandler();
    }

    public async Task<string> GetAccessTokenAsync(CancellationToken cancellationToken = default)
    {
        // Check cache first
        if (_cache.TryGetValue(AppTokenCacheKey, out string? cachedToken) && !string.IsNullOrEmpty(cachedToken))
        {
            if (await ValidateTokenAsync(cachedToken, cancellationToken))
            {
                _logger.LogDebug("Using cached application access token");
                return cachedToken;
            }
        }

        _logger.LogInformation("Requesting new application access token from APS");

        // Request new token using client credentials flow
        var tokenRequest = new
        {
            grant_type = "client_credentials",
            client_id = _options.ClientId,
            client_secret = _options.ClientSecret,
            scope = string.Join(" ", _options.Scopes)
        };

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("grant_type", tokenRequest.grant_type),
            new KeyValuePair<string, string>("client_id", tokenRequest.client_id),
            new KeyValuePair<string, string>("client_secret", tokenRequest.client_secret),
            new KeyValuePair<string, string>("scope", tokenRequest.scope)
        });

        try
        {
            var response = await _httpClient.PostAsync("/authentication/v2/token", content, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Failed to obtain access token. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                throw new InvalidOperationException($"Failed to obtain access token: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);

            if (tokenResponse?.AccessToken == null)
            {
                _logger.LogError("Invalid token response: {Response}", responseContent);
                throw new InvalidOperationException("Invalid token response from APS");
            }

            // Cache the token with appropriate expiration
            var cacheExpiry = TimeSpan.FromSeconds(tokenResponse.ExpiresIn - 300); // 5 minutes buffer
            _cache.Set(AppTokenCacheKey, tokenResponse.AccessToken, cacheExpiry);

            _logger.LogInformation("Successfully obtained new access token, expires in {ExpiresIn} seconds", 
                tokenResponse.ExpiresIn);

            return tokenResponse.AccessToken;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while requesting access token");
            throw new InvalidOperationException("Network error while obtaining access token", ex);
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError(ex, "Timeout while requesting access token");
            throw new InvalidOperationException("Timeout while obtaining access token", ex);
        }
    }

    public async Task<string> GetUserAccessTokenAsync(string userId, CancellationToken cancellationToken = default)
    {
        var cacheKey = $"{UserTokenCacheKeyPrefix}{userId}";
        
        // Check cache first
        if (_cache.TryGetValue(cacheKey, out string? cachedToken) && !string.IsNullOrEmpty(cachedToken))
        {
            if (await ValidateTokenAsync(cachedToken, cancellationToken))
            {
                _logger.LogDebug("Using cached user access token for user {UserId}", userId);
                return cachedToken;
            }
        }

        // For user tokens, we would typically need to implement the 3-legged OAuth flow
        // This is a placeholder for the actual implementation
        _logger.LogWarning("User access token requested for {UserId}, but 3-legged OAuth not implemented", userId);
        throw new NotImplementedException("3-legged OAuth flow not yet implemented");
    }

    public async Task<string> RefreshTokenAsync(string refreshToken, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Refreshing access token");

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("grant_type", "refresh_token"),
            new KeyValuePair<string, string>("client_id", _options.ClientId),
            new KeyValuePair<string, string>("client_secret", _options.ClientSecret),
            new KeyValuePair<string, string>("refresh_token", refreshToken)
        });

        try
        {
            var response = await _httpClient.PostAsync("/authentication/v2/token", content, cancellationToken);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogError("Failed to refresh token. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                throw new InvalidOperationException($"Failed to refresh token: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);

            if (tokenResponse?.AccessToken == null)
            {
                _logger.LogError("Invalid refresh token response: {Response}", responseContent);
                throw new InvalidOperationException("Invalid refresh token response from APS");
            }

            _logger.LogInformation("Successfully refreshed access token");
            return tokenResponse.AccessToken;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error while refreshing token");
            throw new InvalidOperationException("Network error while refreshing token", ex);
        }
    }

    public async Task<bool> ValidateTokenAsync(string accessToken, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrEmpty(accessToken))
            return false;

        try
        {
            // Parse JWT token to check expiration
            var token = _jwtHandler.ReadJwtToken(accessToken);
            var expiry = token.ValidTo;
            
            // Add 5-minute buffer for clock skew
            var isValid = expiry > DateTime.UtcNow.AddMinutes(5);
            
            _logger.LogDebug("Token validation result: {IsValid}, expires at {Expiry}", isValid, expiry);
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error validating token: {Token}", accessToken[..Math.Min(20, accessToken.Length)]);
            return false;
        }
    }

    public async Task<bool> RevokeTokenAsync(string accessToken, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Revoking access token");

        var content = new FormUrlEncodedContent(new[]
        {
            new KeyValuePair<string, string>("token", accessToken),
            new KeyValuePair<string, string>("client_id", _options.ClientId),
            new KeyValuePair<string, string>("client_secret", _options.ClientSecret)
        });

        try
        {
            var response = await _httpClient.PostAsync("/authentication/v1/revoke", content, cancellationToken);
            var success = response.IsSuccessStatusCode;
            
            if (success)
            {
                // Remove from cache
                _cache.Remove(AppTokenCacheKey);
                _logger.LogInformation("Successfully revoked access token");
            }
            else
            {
                _logger.LogWarning("Failed to revoke token. Status: {StatusCode}", response.StatusCode);
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error revoking token");
            return false;
        }
    }
}

/// <summary>
/// OAuth 2.0 token response model
/// </summary>
internal class TokenResponse
{
    [JsonPropertyName("access_token")]
    public string? AccessToken { get; set; }

    [JsonPropertyName("token_type")]
    public string? TokenType { get; set; }

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("refresh_token")]
    public string? RefreshToken { get; set; }

    [JsonPropertyName("scope")]
    public string? Scope { get; set; }
}
