@page "/analytics"
@using AccAuditTool.Application.Services
@using AccAuditTool.Domain.Entities
@inject IRiskScoringEngine RiskScoringEngine
@inject IUnitOfWork UnitOfWork
@inject IJSRuntime JSRuntime

<PageTitle>Analytics Dashboard - ACC Audit Tool</PageTitle>

<div class="analytics-dashboard">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-chart-line"></i>
                Analytics Dashboard
            </h1>
            <p class="page-description">Advanced data visualization and trend analysis for audit insights.</p>
        </div>
        <div class="header-actions">
            <div class="time-range-selector">
                <select class="form-select" @bind="SelectedTimeRange" @onchange="OnTimeRangeChanged">
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                    <option value="90d">Last 90 Days</option>
                    <option value="6m">Last 6 Months</option>
                    <option value="1y">Last Year</option>
                </select>
            </div>
            <button class="btn btn-outline-secondary" @onclick="RefreshData">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <button class="btn btn-primary" @onclick="ExportAnalytics">
                <i class="fas fa-download"></i>
                Export
            </button>
        </div>
    </div>

    @if (IsLoading)
    {
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading analytics...</span>
            </div>
            <p class="loading-text">Loading analytics data...</p>
        </div>
    }
    else
    {
        <!-- Key Metrics Row -->
        <div class="metrics-row">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <div class="metric-icon bg-primary">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +12%
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">@TotalAudits</h3>
                            <p class="metric-label">Total Audits</p>
                            <small class="metric-subtitle">@SelectedTimeRange.ToUpper()</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8%
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">@TotalFindings</h3>
                            <p class="metric-label">Total Findings</p>
                            <small class="metric-subtitle">@SelectedTimeRange.ToUpper()</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <div class="metric-icon bg-danger">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="metric-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                0%
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">@CriticalFindings</h3>
                            <p class="metric-label">Critical Findings</p>
                            <small class="metric-subtitle">@SelectedTimeRange.ToUpper()</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="metric-card">
                        <div class="metric-header">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5%
                            </div>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">@AverageRiskScore.ToString("F1")</h3>
                            <p class="metric-label">Avg Risk Score</p>
                            <small class="metric-subtitle">@SelectedTimeRange.ToUpper()</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="charts-row">
            <div class="row">
                <div class="col-lg-8 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-area"></i>
                                Audit Trends Over Time
                            </h5>
                            <div class="card-actions">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn @(TrendViewMode == "audits" ? "btn-primary" : "btn-outline-primary")" 
                                            @onclick="() => SetTrendViewMode(\"audits\")">Audits</button>
                                    <button class="btn @(TrendViewMode == "findings" ? "btn-primary" : "btn-outline-primary")" 
                                            @onclick="() => SetTrendViewMode(\"findings\")">Findings</button>
                                    <button class="btn @(TrendViewMode == "risk" ? "btn-primary" : "btn-outline-primary")" 
                                            @onclick="() => SetTrendViewMode(\"risk\")">Risk</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <ChartBase ChartType="line" 
                                       ChartData="@GetTrendChartData()" 
                                       ChartOptions="@GetTrendChartOptions()"
                                       CssClass="chart-lg"
                                       IsLoading="@IsLoadingTrends"
                                       HasData="@TrendData.Any()" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                Findings by Category
                            </h5>
                        </div>
                        <div class="card-body">
                            <ChartBase ChartType="doughnut" 
                                       ChartData="@GetCategoryChartData()" 
                                       ChartOptions="@GetDoughnutChartOptions()"
                                       CssClass="chart-md"
                                       IsLoading="@IsLoadingCategories"
                                       HasData="@CategoryData.Any()" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="charts-row">
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-bar"></i>
                                Findings by Severity
                            </h5>
                        </div>
                        <div class="card-body">
                            <ChartBase ChartType="bar" 
                                       ChartData="@GetSeverityChartData()" 
                                       ChartOptions="@GetBarChartOptions()"
                                       CssClass="chart-md"
                                       IsLoading="@IsLoadingSeverity"
                                       HasData="@SeverityData.Any()" />
                        </div>
                    </div>
                </div>
                <div class="col-lg-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                Risk Score Distribution
                            </h5>
                        </div>
                        <div class="card-body">
                            <ChartBase ChartType="line" 
                                       ChartData="@GetRiskDistributionChartData()" 
                                       ChartOptions="@GetLineChartOptions()"
                                       CssClass="chart-md"
                                       IsLoading="@IsLoadingRisk"
                                       HasData="@RiskDistributionData.Any()" />
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Heatmap Row -->
        <div class="heatmap-row">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-th"></i>
                        Audit Activity Heatmap
                    </h5>
                    <div class="card-actions">
                        <small class="text-muted">Darker colors indicate higher activity</small>
                    </div>
                </div>
                <div class="card-body">
                    <AuditHeatmap Data="@HeatmapData" TimeRange="@SelectedTimeRange" />
                </div>
            </div>
        </div>

        <!-- Top Insights -->
        <div class="insights-row">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">
                        <i class="fas fa-lightbulb"></i>
                        Key Insights
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach (var insight in TopInsights)
                        {
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="insight-card">
                                    <div class="insight-icon">
                                        <i class="@insight.Icon <EMAIL>"></i>
                                    </div>
                                    <div class="insight-content">
                                        <h6 class="insight-title">@insight.Title</h6>
                                        <p class="insight-description">@insight.Description</p>
                                        <div class="insight-value">@insight.Value</div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool IsLoading = true;
    private bool IsLoadingTrends = false;
    private bool IsLoadingCategories = false;
    private bool IsLoadingSeverity = false;
    private bool IsLoadingRisk = false;
    
    private string SelectedTimeRange = "30d";
    private string TrendViewMode = "audits";

    // Sample data - in real implementation, this would come from services
    private int TotalAudits = 45;
    private int TotalFindings = 128;
    private int CriticalFindings = 8;
    private double AverageRiskScore = 65.3;

    private List<TrendDataPoint> TrendData = new();
    private Dictionary<string, int> CategoryData = new();
    private Dictionary<AuditSeverity, int> SeverityData = new();
    private List<RiskDistributionPoint> RiskDistributionData = new();
    private List<HeatmapDataPoint> HeatmapData = new();
    private List<AnalyticsInsight> TopInsights = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadAnalyticsData();
    }

    private async Task LoadAnalyticsData()
    {
        IsLoading = true;
        StateHasChanged();

        try
        {
            // Load all analytics data
            TrendData = GenerateTrendData();
            CategoryData = GenerateCategoryData();
            SeverityData = GenerateSeverityData();
            RiskDistributionData = GenerateRiskDistributionData();
            HeatmapData = GenerateHeatmapData();
            TopInsights = GenerateTopInsights();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading analytics data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadAnalyticsData();
    }

    private async Task OnTimeRangeChanged()
    {
        await LoadAnalyticsData();
    }

    private async Task SetTrendViewMode(string mode)
    {
        TrendViewMode = mode;
        StateHasChanged();
    }

    private async Task ExportAnalytics()
    {
        await JSRuntime.InvokeVoidAsync("downloadFile", $"/api/analytics/export?range={SelectedTimeRange}");
    }

    // Chart data preparation methods
    private object GetTrendChartData()
    {
        var sortedData = TrendData.OrderBy(d => d.Date).ToList();

        return new
        {
            labels = sortedData.Select(d => d.Date.ToString("MMM dd")).ToArray(),
            datasets = TrendViewMode switch
            {
                "audits" => new[]
                {
                    new
                    {
                        label = "Audit Runs",
                        data = sortedData.Select(d => d.AuditCount).ToArray(),
                        borderColor = "rgb(13, 110, 253)",
                        backgroundColor = "rgba(13, 110, 253, 0.1)",
                        borderWidth = 2,
                        fill = true,
                        tension = 0.4
                    }
                },
                "findings" => new[]
                {
                    new
                    {
                        label = "Total Findings",
                        data = sortedData.Select(d => d.FindingsCount).ToArray(),
                        borderColor = "rgb(255, 193, 7)",
                        backgroundColor = "rgba(255, 193, 7, 0.1)",
                        borderWidth = 2,
                        fill = true,
                        tension = 0.4
                    },
                    new
                    {
                        label = "Critical Findings",
                        data = sortedData.Select(d => d.CriticalCount).ToArray(),
                        borderColor = "rgb(220, 53, 69)",
                        backgroundColor = "rgba(220, 53, 69, 0.1)",
                        borderWidth = 2,
                        fill = false,
                        tension = 0.4
                    }
                },
                "risk" => new[]
                {
                    new
                    {
                        label = "Average Risk Score",
                        data = sortedData.Select(d => d.RiskScore).ToArray(),
                        borderColor = "rgb(25, 135, 84)",
                        backgroundColor = "rgba(25, 135, 84, 0.1)",
                        borderWidth = 2,
                        fill = true,
                        tension = 0.4
                    }
                },
                _ => new object[0]
            }
        };
    }

    private object GetCategoryChartData()
    {
        return new
        {
            labels = CategoryData.Keys.ToArray(),
            datasets = new[]
            {
                new
                {
                    data = CategoryData.Values.ToArray(),
                    backgroundColor = new[]
                    {
                        "#dc3545", "#fd7e14", "#ffc107", "#20c997", "#0dcaf0", "#6f42c1"
                    },
                    borderColor = new[]
                    {
                        "#b02a37", "#e8681a", "#e6ac00", "#1aa179", "#0bb5d6", "#59359a"
                    },
                    borderWidth = 2
                }
            }
        };
    }

    private object GetSeverityChartData()
    {
        return new
        {
            labels = SeverityData.Keys.Select(k => k.ToString()).ToArray(),
            datasets = new[]
            {
                new
                {
                    label = "Findings Count",
                    data = SeverityData.Values.ToArray(),
                    backgroundColor = SeverityData.Keys.Select(GetSeverityColor).ToArray(),
                    borderColor = SeverityData.Keys.Select(GetSeverityBorderColor).ToArray(),
                    borderWidth = 1
                }
            }
        };
    }

    private object GetRiskDistributionChartData()
    {
        var sortedData = RiskDistributionData.OrderBy(d => d.RiskRange).ToList();

        return new
        {
            labels = sortedData.Select(d => d.RiskRange).ToArray(),
            datasets = new[]
            {
                new
                {
                    label = "Count",
                    data = sortedData.Select(d => d.Count).ToArray(),
                    borderColor = "rgb(25, 135, 84)",
                    backgroundColor = "rgba(25, 135, 84, 0.2)",
                    borderWidth = 2,
                    fill = true,
                    tension = 0.4
                }
            }
        };
    }

    private object GetTrendChartOptions()
    {
        return new
        {
            responsive = true,
            maintainAspectRatio = false,
            plugins = new
            {
                legend = new { position = "top" },
                tooltip = new { mode = "index", intersect = false }
            },
            scales = new
            {
                x = new { display = true, grid = new { display = false } },
                y = new { display = true, beginAtZero = true, grid = new { color = "rgba(0, 0, 0, 0.1)" } }
            }
        };
    }

    private object GetDoughnutChartOptions()
    {
        return new
        {
            responsive = true,
            maintainAspectRatio = false,
            plugins = new
            {
                legend = new { position = "bottom" },
                tooltip = new
                {
                    callbacks = new
                    {
                        label = "function(context) { const total = context.dataset.data.reduce((a, b) => a + b, 0); const percentage = ((context.parsed / total) * 100).toFixed(1); return context.label + ': ' + context.parsed + ' (' + percentage + '%)'; }"
                    }
                }
            },
            cutout = "60%"
        };
    }

    private object GetBarChartOptions()
    {
        return new
        {
            responsive = true,
            maintainAspectRatio = false,
            plugins = new
            {
                legend = new { display = false },
                tooltip = new { mode = "index", intersect = false }
            },
            scales = new
            {
                x = new { display = true, grid = new { display = false } },
                y = new { display = true, beginAtZero = true, grid = new { color = "rgba(0, 0, 0, 0.1)" } }
            }
        };
    }

    private object GetLineChartOptions()
    {
        return new
        {
            responsive = true,
            maintainAspectRatio = false,
            plugins = new
            {
                legend = new { display = false },
                tooltip = new { mode = "index", intersect = false }
            },
            scales = new
            {
                x = new { display = true, grid = new { display = false } },
                y = new { display = true, beginAtZero = true, grid = new { color = "rgba(0, 0, 0, 0.1)" } }
            }
        };
    }

    // Helper methods
    private string GetSeverityColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#dc3545",
            AuditSeverity.High => "#fd7e14",
            AuditSeverity.Medium => "#ffc107",
            AuditSeverity.Low => "#20c997",
            AuditSeverity.Info => "#0dcaf0",
            _ => "#6c757d"
        };
    }

    private string GetSeverityBorderColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#b02a37",
            AuditSeverity.High => "#e8681a",
            AuditSeverity.Medium => "#e6ac00",
            AuditSeverity.Low => "#1aa179",
            AuditSeverity.Info => "#0bb5d6",
            _ => "#5a6268"
        };
    }

    // Data generation methods (sample data)
    private List<TrendDataPoint> GenerateTrendData()
    {
        var data = new List<TrendDataPoint>();
        var random = new Random();
        var days = SelectedTimeRange switch
        {
            "7d" => 7,
            "30d" => 30,
            "90d" => 90,
            "6m" => 180,
            "1y" => 365,
            _ => 30
        };

        for (int i = days - 1; i >= 0; i--)
        {
            var date = DateTime.Today.AddDays(-i);
            data.Add(new TrendDataPoint
            {
                Date = date,
                AuditCount = random.Next(0, 8),
                FindingsCount = random.Next(0, 25),
                CriticalCount = random.Next(0, 5),
                RiskScore = random.NextDouble() * 40 + 40 // 40-80 range
            });
        }

        return data;
    }

    private Dictionary<string, int> GenerateCategoryData()
    {
        return new Dictionary<string, int>
        {
            ["Security"] = 45,
            ["Compliance"] = 32,
            ["Access Control"] = 28,
            ["Data Integrity"] = 18,
            ["Performance"] = 12,
            ["Configuration"] = 8
        };
    }

    private Dictionary<AuditSeverity, int> GenerateSeverityData()
    {
        return new Dictionary<AuditSeverity, int>
        {
            [AuditSeverity.Critical] = 8,
            [AuditSeverity.High] = 23,
            [AuditSeverity.Medium] = 45,
            [AuditSeverity.Low] = 67,
            [AuditSeverity.Info] = 32
        };
    }

    private List<RiskDistributionPoint> GenerateRiskDistributionData()
    {
        return new List<RiskDistributionPoint>
        {
            new() { RiskRange = "0-20", Count = 5 },
            new() { RiskRange = "21-40", Count = 12 },
            new() { RiskRange = "41-60", Count = 28 },
            new() { RiskRange = "61-80", Count = 35 },
            new() { RiskRange = "81-100", Count = 15 }
        };
    }

    private List<HeatmapDataPoint> GenerateHeatmapData()
    {
        var data = new List<HeatmapDataPoint>();
        var random = new Random();

        for (int week = 0; week < 12; week++)
        {
            for (int day = 0; day < 7; day++)
            {
                data.Add(new HeatmapDataPoint
                {
                    Week = week,
                    Day = day,
                    Value = random.Next(0, 10)
                });
            }
        }

        return data;
    }

    private List<AnalyticsInsight> GenerateTopInsights()
    {
        return new List<AnalyticsInsight>
        {
            new()
            {
                Title = "Peak Activity Day",
                Description = "Tuesdays show highest audit activity",
                Value = "32% higher",
                Icon = "fas fa-calendar-day",
                Color = "primary"
            },
            new()
            {
                Title = "Most Common Finding",
                Description = "Access control violations are most frequent",
                Value = "28% of total",
                Icon = "fas fa-key",
                Color = "warning"
            },
            new()
            {
                Title = "Risk Trend",
                Description = "Overall risk score trending downward",
                Value = "-12% this month",
                Icon = "fas fa-arrow-down",
                Color = "success"
            },
            new()
            {
                Title = "Compliance Rate",
                Description = "ISO 27001 compliance improving",
                Value = "85% compliant",
                Icon = "fas fa-clipboard-check",
                Color = "info"
            },
            new()
            {
                Title = "Critical Findings",
                Description = "Critical findings remain stable",
                Value = "No change",
                Icon = "fas fa-exclamation-triangle",
                Color = "danger"
            },
            new()
            {
                Title = "Audit Efficiency",
                Description = "Average audit completion time",
                Value = "45 minutes",
                Icon = "fas fa-clock",
                Color = "secondary"
            }
        };
    }

    // Data classes
    public class TrendDataPoint
    {
        public DateTime Date { get; set; }
        public int AuditCount { get; set; }
        public int FindingsCount { get; set; }
        public int CriticalCount { get; set; }
        public double RiskScore { get; set; }
    }

    public class RiskDistributionPoint
    {
        public string RiskRange { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class HeatmapDataPoint
    {
        public int Week { get; set; }
        public int Day { get; set; }
        public int Value { get; set; }
    }

    public class AnalyticsInsight
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string Color { get; set; } = string.Empty;
    }
}

<style>
    .analytics-dashboard {
        padding: 0;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .header-content {
        flex: 1;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        margin-right: 0.75rem;
        color: #007bff;
    }

    .page-description {
        color: #6c757d;
        margin: 0;
        font-size: 0.95rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        align-items: center;
        flex-shrink: 0;
    }

    .time-range-selector .form-select {
        min-width: 150px;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 4rem;
        gap: 1rem;
    }

    .loading-text {
        color: #6c757d;
        margin: 0;
    }

    /* Metrics Row */
    .metrics-row {
        margin-bottom: 2rem;
    }

    .metric-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        height: 100%;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
    }

    .metric-icon i {
        font-size: 1.25rem;
    }

    .metric-trend {
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
    }

    .trend-up {
        color: #198754;
        background-color: rgba(25, 135, 84, 0.1);
    }

    .trend-down {
        color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
    }

    .trend-stable {
        color: #6c757d;
        background-color: rgba(108, 117, 125, 0.1);
    }

    .metric-content {
        text-align: left;
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #495057;
        margin: 0 0 0.25rem 0;
        line-height: 1;
    }

    .metric-label {
        font-size: 1rem;
        color: #6c757d;
        margin: 0 0 0.25rem 0;
        font-weight: 500;
    }

    .metric-subtitle {
        font-size: 0.8rem;
        color: #adb5bd;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Charts */
    .charts-row {
        margin-bottom: 2rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    /* Heatmap */
    .heatmap-row {
        margin-bottom: 2rem;
    }

    /* Insights */
    .insights-row {
        margin-bottom: 2rem;
    }

    .insight-card {
        background: #fff;
        border: 1px solid #f1f3f4;
        border-radius: 0.375rem;
        padding: 1rem;
        height: 100%;
        display: flex;
        gap: 1rem;
        transition: background-color 0.2s ease;
    }

    .insight-card:hover {
        background-color: #f8f9fa;
    }

    .insight-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .insight-icon i {
        font-size: 1.2rem;
    }

    .insight-content {
        flex: 1;
        min-width: 0;
    }

    .insight-title {
        font-weight: 600;
        color: #495057;
        margin: 0 0 0.25rem 0;
        font-size: 0.9rem;
    }

    .insight-description {
        color: #6c757d;
        font-size: 0.8rem;
        margin: 0 0 0.5rem 0;
        line-height: 1.3;
    }

    .insight-value {
        font-weight: 600;
        color: #495057;
        font-size: 0.9rem;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .header-actions {
            justify-content: stretch;
            flex-wrap: wrap;
        }

        .header-actions > * {
            flex: 1;
        }

        .metric-card {
            margin-bottom: 1rem;
        }

        .metric-value {
            font-size: 2rem;
        }

        .insight-card {
            margin-bottom: 1rem;
        }
    }

    @media (max-width: 576px) {
        .metric-header {
            flex-direction: column;
            gap: 0.75rem;
            align-items: flex-start;
        }

        .metric-trend {
            align-self: flex-end;
        }

        .insight-card {
            flex-direction: column;
            text-align: center;
        }

        .insight-icon {
            align-self: center;
        }
    }
</style>
