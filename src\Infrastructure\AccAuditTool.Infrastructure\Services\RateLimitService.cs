using AccAuditTool.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for managing API rate limiting with token bucket algorithm
/// </summary>
public interface IRateLimitService
{
    /// <summary>
    /// Wait for permission to make an API call
    /// </summary>
    Task WaitForPermissionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Try to acquire permission immediately without waiting
    /// </summary>
    bool TryAcquirePermission();

    /// <summary>
    /// Update rate limits based on API response headers
    /// </summary>
    void UpdateLimitsFromResponse(HttpResponseMessage response);

    /// <summary>
    /// Get current rate limit status
    /// </summary>
    RateLimitStatus GetStatus();
}

/// <summary>
/// Rate limit status information
/// </summary>
public class RateLimitStatus
{
    public int AvailableTokens { get; set; }
    public int MaxTokens { get; set; }
    public DateTime NextRefill { get; set; }
    public TimeSpan RefillInterval { get; set; }
    public bool IsAdaptive { get; set; }
    public int CurrentLimit { get; set; }
}

/// <summary>
/// Token bucket rate limiter implementation
/// </summary>
public class RateLimitService : IRateLimitService
{
    private readonly RateLimitOptions _options;
    private readonly ILogger<RateLimitService> _logger;
    private readonly SemaphoreSlim _semaphore;
    
    private volatile int _availableTokens;
    private volatile int _maxTokens;
    private DateTime _lastRefill;
    private TimeSpan _refillInterval;
    private volatile int _currentRequestsPerMinute;
    
    private readonly object _lockObject = new();
    private readonly Timer _refillTimer;

    public RateLimitService(IOptions<ApsApiOptions> options, ILogger<RateLimitService> logger)
    {
        _options = options.Value.RateLimit;
        _logger = logger;
        
        _maxTokens = _options.BurstCapacity;
        _availableTokens = _maxTokens;
        _currentRequestsPerMinute = _options.RequestsPerMinute;
        _refillInterval = TimeSpan.FromMinutes(1.0 / _currentRequestsPerMinute);
        _lastRefill = DateTime.UtcNow;
        
        _semaphore = new SemaphoreSlim(1, 1);
        
        // Start refill timer
        _refillTimer = new Timer(RefillTokens, null, _refillInterval, _refillInterval);
        
        _logger.LogInformation("Rate limiter initialized: {RequestsPerMinute} requests/minute, {BurstCapacity} burst capacity",
            _currentRequestsPerMinute, _maxTokens);
    }

    public async Task WaitForPermissionAsync(CancellationToken cancellationToken = default)
    {
        while (!TryAcquirePermission())
        {
            var waitTime = CalculateWaitTime();
            _logger.LogDebug("Rate limit reached, waiting {WaitTime}ms", waitTime.TotalMilliseconds);
            
            try
            {
                await Task.Delay(waitTime, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                _logger.LogDebug("Rate limit wait cancelled");
                throw;
            }
        }
    }

    public bool TryAcquirePermission()
    {
        lock (_lockObject)
        {
            if (_availableTokens > 0)
            {
                _availableTokens--;
                _logger.LogTrace("Token acquired, {AvailableTokens} remaining", _availableTokens);
                return true;
            }
            
            _logger.LogTrace("No tokens available for rate limiting");
            return false;
        }
    }

    public void UpdateLimitsFromResponse(HttpResponseMessage response)
    {
        if (!_options.EnableAdaptiveRateLimit)
            return;

        try
        {
            // Check for rate limit headers from APS API
            if (response.Headers.TryGetValues("X-RateLimit-Limit", out var limitValues))
            {
                if (int.TryParse(limitValues.FirstOrDefault(), out var newLimit))
                {
                    UpdateRateLimit(newLimit);
                }
            }

            // Check for remaining requests
            if (response.Headers.TryGetValues("X-RateLimit-Remaining", out var remainingValues))
            {
                if (int.TryParse(remainingValues.FirstOrDefault(), out var remaining))
                {
                    _logger.LogTrace("API reports {Remaining} requests remaining", remaining);
                }
            }

            // Check for reset time
            if (response.Headers.TryGetValues("X-RateLimit-Reset", out var resetValues))
            {
                if (long.TryParse(resetValues.FirstOrDefault(), out var resetTimestamp))
                {
                    var resetTime = DateTimeOffset.FromUnixTimeSeconds(resetTimestamp);
                    _logger.LogTrace("API rate limit resets at {ResetTime}", resetTime);
                }
            }

            // Handle 429 Too Many Requests
            if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
            {
                _logger.LogWarning("Received 429 Too Many Requests, reducing rate limit");
                ReduceRateLimit();
                
                // Check for Retry-After header
                if (response.Headers.RetryAfter != null)
                {
                    var retryAfter = response.Headers.RetryAfter.Delta ?? TimeSpan.FromSeconds(60);
                    _logger.LogInformation("API requested retry after {RetryAfter}", retryAfter);
                    
                    // Temporarily reduce available tokens
                    lock (_lockObject)
                    {
                        _availableTokens = 0;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error updating rate limits from response headers");
        }
    }

    public RateLimitStatus GetStatus()
    {
        lock (_lockObject)
        {
            return new RateLimitStatus
            {
                AvailableTokens = _availableTokens,
                MaxTokens = _maxTokens,
                NextRefill = _lastRefill.Add(_refillInterval),
                RefillInterval = _refillInterval,
                IsAdaptive = _options.EnableAdaptiveRateLimit,
                CurrentLimit = _currentRequestsPerMinute
            };
        }
    }

    private void RefillTokens(object? state)
    {
        lock (_lockObject)
        {
            if (_availableTokens < _maxTokens)
            {
                _availableTokens = Math.Min(_availableTokens + 1, _maxTokens);
                _lastRefill = DateTime.UtcNow;
                _logger.LogTrace("Token refilled, {AvailableTokens}/{MaxTokens} available", _availableTokens, _maxTokens);
            }
        }
    }

    private TimeSpan CalculateWaitTime()
    {
        lock (_lockObject)
        {
            var timeSinceLastRefill = DateTime.UtcNow - _lastRefill;
            var timeUntilNextRefill = _refillInterval - timeSinceLastRefill;
            
            return timeUntilNextRefill > TimeSpan.Zero ? timeUntilNextRefill : TimeSpan.FromMilliseconds(100);
        }
    }

    private void UpdateRateLimit(int newLimit)
    {
        if (newLimit != _currentRequestsPerMinute)
        {
            _logger.LogInformation("Updating rate limit from {OldLimit} to {NewLimit} requests/minute", 
                _currentRequestsPerMinute, newLimit);
            
            lock (_lockObject)
            {
                _currentRequestsPerMinute = newLimit;
                _refillInterval = TimeSpan.FromMinutes(1.0 / newLimit);
                
                // Update timer interval
                _refillTimer.Change(_refillInterval, _refillInterval);
            }
        }
    }

    private void ReduceRateLimit()
    {
        var newLimit = Math.Max(1, (int)(_currentRequestsPerMinute * 0.8)); // Reduce by 20%
        UpdateRateLimit(newLimit);
    }

    public void Dispose()
    {
        _refillTimer?.Dispose();
        _semaphore?.Dispose();
    }
}
