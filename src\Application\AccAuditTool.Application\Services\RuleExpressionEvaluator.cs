using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Text.Json;
using System.Text.RegularExpressions;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for evaluating rule expressions and conditions
/// </summary>
public interface IRuleExpressionEvaluator
{
    /// <summary>
    /// Evaluate a condition against a field value
    /// </summary>
    Task<bool> EvaluateConditionAsync(object? fieldValue, string operatorType, object? expectedValue, CancellationToken cancellationToken = default);

    /// <summary>
    /// Parse and evaluate a complex expression
    /// </summary>
    Task<object?> EvaluateExpressionAsync(string expression, Dictionary<string, object> variables, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate an expression syntax
    /// </summary>
    Task<ExpressionValidationResult> ValidateExpressionAsync(string expression, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of rule expression evaluator
/// </summary>
public class RuleExpressionEvaluator : IRuleExpressionEvaluator
{
    private readonly ILogger<RuleExpressionEvaluator> _logger;

    public RuleExpressionEvaluator(ILogger<RuleExpressionEvaluator> logger)
    {
        _logger = logger;
    }

    public async Task<bool> EvaluateConditionAsync(object? fieldValue, string operatorType, object? expectedValue, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogTrace("Evaluating condition: {FieldValue} {Operator} {ExpectedValue}", 
                fieldValue, operatorType, expectedValue);

            return operatorType.ToLowerInvariant() switch
            {
                "equals" or "eq" => await EvaluateEqualsAsync(fieldValue, expectedValue),
                "not_equals" or "ne" => !await EvaluateEqualsAsync(fieldValue, expectedValue),
                "greater_than" or "gt" => await EvaluateGreaterThanAsync(fieldValue, expectedValue),
                "greater_than_or_equal" or "gte" => await EvaluateGreaterThanOrEqualAsync(fieldValue, expectedValue),
                "less_than" or "lt" => await EvaluateLessThanAsync(fieldValue, expectedValue),
                "less_than_or_equal" or "lte" => await EvaluateLessThanOrEqualAsync(fieldValue, expectedValue),
                "contains" => await EvaluateContainsAsync(fieldValue, expectedValue),
                "not_contains" => !await EvaluateContainsAsync(fieldValue, expectedValue),
                "starts_with" => await EvaluateStartsWithAsync(fieldValue, expectedValue),
                "ends_with" => await EvaluateEndsWithAsync(fieldValue, expectedValue),
                "in" => await EvaluateInAsync(fieldValue, expectedValue),
                "not_in" => !await EvaluateInAsync(fieldValue, expectedValue),
                "regex" => await EvaluateRegexAsync(fieldValue, expectedValue),
                "is_null" => fieldValue == null,
                "is_not_null" => fieldValue != null,
                "is_empty" => await EvaluateIsEmptyAsync(fieldValue),
                "is_not_empty" => !await EvaluateIsEmptyAsync(fieldValue),
                "between" => await EvaluateBetweenAsync(fieldValue, expectedValue),
                "contains_any" => await EvaluateContainsAnyAsync(fieldValue, expectedValue),
                "contains_all" => await EvaluateContainsAllAsync(fieldValue, expectedValue),
                _ => throw new ArgumentException($"Unknown operator: {operatorType}")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating condition: {FieldValue} {Operator} {ExpectedValue}", 
                fieldValue, operatorType, expectedValue);
            return false;
        }
    }

    public async Task<object?> EvaluateExpressionAsync(string expression, Dictionary<string, object> variables, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogTrace("Evaluating expression: {Expression}", expression);

            // Handle special functions
            if (expression.StartsWith("NOW()", StringComparison.OrdinalIgnoreCase))
            {
                return await EvaluateDateTimeExpressionAsync(expression);
            }

            if (expression.StartsWith("COUNT(", StringComparison.OrdinalIgnoreCase))
            {
                return await EvaluateCountExpressionAsync(expression, variables);
            }

            if (expression.StartsWith("SUM(", StringComparison.OrdinalIgnoreCase))
            {
                return await EvaluateSumExpressionAsync(expression, variables);
            }

            // Replace variables in expression
            var processedExpression = ReplaceVariables(expression, variables);

            // Try to parse as different types
            if (bool.TryParse(processedExpression, out var boolValue))
                return boolValue;

            if (int.TryParse(processedExpression, out var intValue))
                return intValue;

            if (double.TryParse(processedExpression, out var doubleValue))
                return doubleValue;

            if (DateTime.TryParse(processedExpression, out var dateValue))
                return dateValue;

            // Return as string if no other type matches
            return processedExpression;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error evaluating expression: {Expression}", expression);
            return null;
        }
    }

    public async Task<ExpressionValidationResult> ValidateExpressionAsync(string expression, CancellationToken cancellationToken = default)
    {
        var result = new ExpressionValidationResult
        {
            IsValid = true,
            Expression = expression
        };

        try
        {
            if (string.IsNullOrWhiteSpace(expression))
            {
                result.IsValid = false;
                result.Errors.Add("Expression cannot be empty");
                return result;
            }

            // Check for balanced parentheses
            if (!HasBalancedParentheses(expression))
            {
                result.IsValid = false;
                result.Errors.Add("Unbalanced parentheses in expression");
            }

            // Check for valid function syntax
            var functionPattern = @"(\w+)\s*\([^)]*\)";
            var functions = Regex.Matches(expression, functionPattern);
            
            foreach (Match function in functions)
            {
                var functionName = function.Groups[1].Value.ToUpperInvariant();
                if (!IsValidFunction(functionName))
                {
                    result.IsValid = false;
                    result.Errors.Add($"Unknown function: {functionName}");
                }
            }

            // Check for valid operators
            var operatorPattern = @"(==|!=|>=|<=|>|<|\+|-|\*|/|%|&&|\|\||!|AND|OR|NOT)";
            var operators = Regex.Matches(expression, operatorPattern, RegexOptions.IgnoreCase);
            
            // Additional syntax validation could be added here

            if (result.IsValid)
            {
                result.Warnings.Add("Expression syntax appears valid, but runtime validation is recommended");
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Error validating expression: {ex.Message}");
        }

        return result;
    }

    private async Task<bool> EvaluateEqualsAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null && expectedValue == null)
            return true;

        if (fieldValue == null || expectedValue == null)
            return false;

        // Handle different types
        if (fieldValue.GetType() != expectedValue.GetType())
        {
            // Try to convert to common type
            if (TryConvertToCommonType(fieldValue, expectedValue, out var convertedField, out var convertedExpected))
            {
                return Equals(convertedField, convertedExpected);
            }
        }

        return Equals(fieldValue, expectedValue);
    }

    private async Task<bool> EvaluateGreaterThanAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (TryConvertToComparable(fieldValue, expectedValue, out var comparableField, out var comparableExpected))
        {
            return comparableField.CompareTo(comparableExpected) > 0;
        }

        return false;
    }

    private async Task<bool> EvaluateGreaterThanOrEqualAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (TryConvertToComparable(fieldValue, expectedValue, out var comparableField, out var comparableExpected))
        {
            return comparableField.CompareTo(comparableExpected) >= 0;
        }

        return false;
    }

    private async Task<bool> EvaluateLessThanAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (TryConvertToComparable(fieldValue, expectedValue, out var comparableField, out var comparableExpected))
        {
            return comparableField.CompareTo(comparableExpected) < 0;
        }

        return false;
    }

    private async Task<bool> EvaluateLessThanOrEqualAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (TryConvertToComparable(fieldValue, expectedValue, out var comparableField, out var comparableExpected))
        {
            return comparableField.CompareTo(comparableExpected) <= 0;
        }

        return false;
    }

    private async Task<bool> EvaluateContainsAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        var fieldString = fieldValue.ToString();
        var expectedString = expectedValue.ToString();

        return fieldString?.Contains(expectedString, StringComparison.OrdinalIgnoreCase) == true;
    }

    private async Task<bool> EvaluateStartsWithAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        var fieldString = fieldValue.ToString();
        var expectedString = expectedValue.ToString();

        return fieldString?.StartsWith(expectedString, StringComparison.OrdinalIgnoreCase) == true;
    }

    private async Task<bool> EvaluateEndsWithAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        var fieldString = fieldValue.ToString();
        var expectedString = expectedValue.ToString();

        return fieldString?.EndsWith(expectedString, StringComparison.OrdinalIgnoreCase) == true;
    }

    private async Task<bool> EvaluateInAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        // Handle array/list of values
        if (expectedValue is IEnumerable<object> enumerable)
        {
            return enumerable.Any(item => Equals(fieldValue, item));
        }

        // Handle comma-separated string
        if (expectedValue is string stringValue)
        {
            var values = stringValue.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                   .Select(v => v.Trim());
            return values.Any(v => Equals(fieldValue.ToString(), v));
        }

        return false;
    }

    private async Task<bool> EvaluateRegexAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        var fieldString = fieldValue.ToString();
        var pattern = expectedValue.ToString();

        try
        {
            return Regex.IsMatch(fieldString ?? string.Empty, pattern ?? string.Empty, RegexOptions.IgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Invalid regex pattern: {Pattern}", pattern);
            return false;
        }
    }

    private async Task<bool> EvaluateIsEmptyAsync(object? fieldValue)
    {
        if (fieldValue == null)
            return true;

        if (fieldValue is string stringValue)
            return string.IsNullOrWhiteSpace(stringValue);

        if (fieldValue is IEnumerable<object> enumerable)
            return !enumerable.Any();

        return false;
    }

    private async Task<bool> EvaluateBetweenAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        // Expected value should be an array with two elements [min, max]
        if (expectedValue is not IEnumerable<object> range)
            return false;

        var rangeArray = range.ToArray();
        if (rangeArray.Length != 2)
            return false;

        var minValue = rangeArray[0];
        var maxValue = rangeArray[1];

        return await EvaluateGreaterThanOrEqualAsync(fieldValue, minValue) &&
               await EvaluateLessThanOrEqualAsync(fieldValue, maxValue);
    }

    private async Task<bool> EvaluateContainsAnyAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (fieldValue is not IEnumerable<object> fieldCollection)
            return false;

        if (expectedValue is not IEnumerable<object> expectedCollection)
            return false;

        return fieldCollection.Any(item => expectedCollection.Contains(item));
    }

    private async Task<bool> EvaluateContainsAllAsync(object? fieldValue, object? expectedValue)
    {
        if (fieldValue == null || expectedValue == null)
            return false;

        if (fieldValue is not IEnumerable<object> fieldCollection)
            return false;

        if (expectedValue is not IEnumerable<object> expectedCollection)
            return false;

        return expectedCollection.All(item => fieldCollection.Contains(item));
    }

    private async Task<DateTime> EvaluateDateTimeExpressionAsync(string expression)
    {
        var now = DateTime.UtcNow;

        // Parse expressions like "NOW() - 30 DAYS", "NOW() + 1 HOUR", etc.
        var pattern = @"NOW\(\)\s*([+-])\s*(\d+)\s*(DAYS?|HOURS?|MINUTES?|SECONDS?)";
        var match = Regex.Match(expression, pattern, RegexOptions.IgnoreCase);

        if (match.Success)
        {
            var operation = match.Groups[1].Value;
            var amount = int.Parse(match.Groups[2].Value);
            var unit = match.Groups[3].Value.ToUpperInvariant();

            var timeSpan = unit switch
            {
                "DAY" or "DAYS" => TimeSpan.FromDays(amount),
                "HOUR" or "HOURS" => TimeSpan.FromHours(amount),
                "MINUTE" or "MINUTES" => TimeSpan.FromMinutes(amount),
                "SECOND" or "SECONDS" => TimeSpan.FromSeconds(amount),
                _ => TimeSpan.Zero
            };

            return operation == "+" ? now.Add(timeSpan) : now.Subtract(timeSpan);
        }

        return now;
    }

    private async Task<int> EvaluateCountExpressionAsync(string expression, Dictionary<string, object> variables)
    {
        // Placeholder for COUNT() function implementation
        // Would need to parse the expression and count items in collections
        return 0;
    }

    private async Task<double> EvaluateSumExpressionAsync(string expression, Dictionary<string, object> variables)
    {
        // Placeholder for SUM() function implementation
        // Would need to parse the expression and sum numeric values
        return 0.0;
    }

    private string ReplaceVariables(string expression, Dictionary<string, object> variables)
    {
        var result = expression;
        
        foreach (var variable in variables)
        {
            var placeholder = $"{{{variable.Key}}}";
            var value = variable.Value?.ToString() ?? string.Empty;
            result = result.Replace(placeholder, value);
        }

        return result;
    }

    private bool HasBalancedParentheses(string expression)
    {
        var count = 0;
        foreach (var ch in expression)
        {
            if (ch == '(') count++;
            else if (ch == ')') count--;
            
            if (count < 0) return false;
        }
        return count == 0;
    }

    private bool IsValidFunction(string functionName)
    {
        var validFunctions = new[] { "NOW", "COUNT", "SUM", "AVG", "MIN", "MAX", "UPPER", "LOWER", "TRIM" };
        return validFunctions.Contains(functionName.ToUpperInvariant());
    }

    private bool TryConvertToCommonType(object value1, object value2, out object? converted1, out object? converted2)
    {
        converted1 = null;
        converted2 = null;

        try
        {
            // Try numeric conversion
            if (double.TryParse(value1.ToString(), out var double1) && 
                double.TryParse(value2.ToString(), out var double2))
            {
                converted1 = double1;
                converted2 = double2;
                return true;
            }

            // Try date conversion
            if (DateTime.TryParse(value1.ToString(), out var date1) && 
                DateTime.TryParse(value2.ToString(), out var date2))
            {
                converted1 = date1;
                converted2 = date2;
                return true;
            }

            // Fall back to string comparison
            converted1 = value1.ToString();
            converted2 = value2.ToString();
            return true;
        }
        catch
        {
            return false;
        }
    }

    private bool TryConvertToComparable(object value1, object value2, out IComparable? comparable1, out IComparable? comparable2)
    {
        comparable1 = null;
        comparable2 = null;

        if (TryConvertToCommonType(value1, value2, out var converted1, out var converted2))
        {
            comparable1 = converted1 as IComparable;
            comparable2 = converted2 as IComparable;
            return comparable1 != null && comparable2 != null;
        }

        return false;
    }
}

/// <summary>
/// Result of expression validation
/// </summary>
public class ExpressionValidationResult
{
    public bool IsValid { get; set; }
    public string Expression { get; set; } = string.Empty;
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}
