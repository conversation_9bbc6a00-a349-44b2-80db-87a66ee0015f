@page "/rules"
@using AccAuditTool.Application.Services
@using AccAuditTool.Domain.Interfaces
@inject IRuleRegistry RuleRegistry
@inject ICustomRuleBuilder CustomRuleBuilder
@inject IRuleTestingService RuleTestingService
@inject IJSRuntime JSRuntime

<PageTitle>Rule Management - ACC Audit Tool</PageTitle>

<div class="rule-management">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-cogs"></i>
                Rule Management
            </h1>
            <p class="page-description">Manage audit rules, create custom rules, and configure rule parameters.</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-outline-secondary" @onclick="RefreshRules">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <button class="btn btn-outline-info" @onclick="TestAllRules">
                <i class="fas fa-vial"></i>
                Test All
            </button>
            <button class="btn btn-primary" @onclick="CreateNewRule">
                <i class="fas fa-plus"></i>
                Create Rule
            </button>
        </div>
    </div>

    <!-- Rule Statistics -->
    <div class="rule-stats">
        <div class="row">
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-primary">@TotalRules</div>
                    <div class="stat-label">Total Rules</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-success">@EnabledRules</div>
                    <div class="stat-label">Enabled</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-warning">@DisabledRules</div>
                    <div class="stat-label">Disabled</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-info">@CustomRules</div>
                    <div class="stat-label">Custom</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-secondary">@BuiltInRules</div>
                    <div class="stat-label">Built-in</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-danger">@FailedValidation</div>
                    <div class="stat-label">Invalid</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="rule-filters">
        <div class="row">
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Category</label>
                    <select class="form-select" @bind="SelectedCategory" @onchange="ApplyFilters">
                        <option value="">All Categories</option>
                        <option value="@AuditRuleCategory.Security">Security</option>
                        <option value="@AuditRuleCategory.Compliance">Compliance</option>
                        <option value="@AuditRuleCategory.AccessControl">Access Control</option>
                        <option value="@AuditRuleCategory.DataIntegrity">Data Integrity</option>
                        <option value="@AuditRuleCategory.Performance">Performance</option>
                        <option value="@AuditRuleCategory.Configuration">Configuration</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Severity</label>
                    <select class="form-select" @bind="SelectedSeverity" @onchange="ApplyFilters">
                        <option value="">All Severities</option>
                        <option value="@AuditSeverity.Critical">Critical</option>
                        <option value="@AuditSeverity.High">High</option>
                        <option value="@AuditSeverity.Medium">Medium</option>
                        <option value="@AuditSeverity.Low">Low</option>
                        <option value="@AuditSeverity.Info">Info</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" @bind="SelectedStatus" @onchange="ApplyFilters">
                        <option value="">All Statuses</option>
                        <option value="enabled">Enabled</option>
                        <option value="disabled">Disabled</option>
                        <option value="custom">Custom</option>
                        <option value="builtin">Built-in</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" 
                               class="form-control" 
                               placeholder="Search rules..." 
                               @bind="SearchQuery"
                               @onkeypress="HandleSearchKeyPress" />
                        <button class="btn btn-outline-secondary" type="button" @onclick="ApplyFilters">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rule List -->
    <div class="rule-list-container">
        @if (IsLoading)
        {
            <div class="loading-container">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading rules...</span>
                </div>
                <p class="loading-text">Loading rules...</p>
            </div>
        }
        else if (FilteredRules.Any())
        {
            <div class="rule-list">
                <div class="list-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5>Audit Rules (@FilteredRules.Count)</h5>
                        </div>
                        <div class="col-auto">
                            <div class="bulk-actions">
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" 
                                            type="button" 
                                            data-bs-toggle="dropdown" 
                                            aria-expanded="false">
                                        <i class="fas fa-tasks"></i>
                                        Bulk Actions
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><button class="dropdown-item" @onclick="EnableSelectedRules">
                                            <i class="fas fa-check"></i> Enable Selected
                                        </button></li>
                                        <li><button class="dropdown-item" @onclick="DisableSelectedRules">
                                            <i class="fas fa-times"></i> Disable Selected
                                        </button></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><button class="dropdown-item" @onclick="TestSelectedRules">
                                            <i class="fas fa-vial"></i> Test Selected
                                        </button></li>
                                        <li><button class="dropdown-item text-danger" @onclick="DeleteSelectedRules">
                                            <i class="fas fa-trash"></i> Delete Selected
                                        </button></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <RuleListView Rules="@FilteredRules" 
                              SelectedRules="@SelectedRules"
                              OnRuleSelected="HandleRuleSelection"
                              OnViewRule="ViewRuleDetails"
                              OnEditRule="EditRule"
                              OnTestRule="TestRule"
                              OnToggleRule="ToggleRuleEnabled"
                              OnDeleteRule="DeleteRule"
                              OnDuplicateRule="DuplicateRule" />
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <nav aria-label="Rule pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item @(CurrentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(CurrentPage - 1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        </li>
                        @for (int i = Math.Max(1, CurrentPage - 2); i <= Math.Min(TotalPages, CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == CurrentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => GoToPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(CurrentPage == TotalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(CurrentPage + 1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
                <div class="pagination-info">
                    Showing @((CurrentPage - 1) * PageSize + 1) to @Math.Min(CurrentPage * PageSize, FilteredRules.Count) of @FilteredRules.Count rules
                </div>
            </div>
        }
        else
        {
            <div class="no-rules-message">
                <i class="fas fa-cogs text-muted"></i>
                <h5>No Rules Found</h5>
                <p class="text-muted">
                    @if (HasActiveFilters)
                    {
                        <span>No rules match your current filters. Try adjusting your search criteria.</span>
                    }
                    else
                    {
                        <span>No audit rules are configured. Get started by creating your first custom rule.</span>
                    }
                </p>
                @if (!HasActiveFilters)
                {
                    <button class="btn btn-primary" @onclick="CreateNewRule">
                        <i class="fas fa-plus"></i>
                        Create First Rule
                    </button>
                }
                else
                {
                    <button class="btn btn-outline-secondary" @onclick="ClearFilters">
                        <i class="fas fa-times"></i>
                        Clear Filters
                    </button>
                }
            </div>
        }
    </div>
</div>

@code {
    private bool IsLoading = true;
    private string SelectedCategory = string.Empty;
    private string SelectedSeverity = string.Empty;
    private string SelectedStatus = string.Empty;
    private string SearchQuery = string.Empty;
    private int CurrentPage = 1;
    private int PageSize = 10;
    private HashSet<string> SelectedRules = new();

    // Sample data - in real implementation, this would come from services
    private List<IAuditRule> AllRules = new();
    private List<IAuditRule> FilteredRules = new();

    // Statistics
    private int TotalRules => AllRules.Count;
    private int EnabledRules => AllRules.Count(r => r.IsEnabled);
    private int DisabledRules => AllRules.Count(r => !r.IsEnabled);
    private int CustomRules => AllRules.Count(r => r.RuleId.StartsWith("CUSTOM-"));
    private int BuiltInRules => AllRules.Count(r => !r.RuleId.StartsWith("CUSTOM-"));
    private int FailedValidation = 2; // Sample data

    private int TotalPages => (int)Math.Ceiling((double)FilteredRules.Count / PageSize);
    private bool HasActiveFilters => !string.IsNullOrEmpty(SelectedCategory) || 
                                   !string.IsNullOrEmpty(SelectedSeverity) || 
                                   !string.IsNullOrEmpty(SelectedStatus) || 
                                   !string.IsNullOrEmpty(SearchQuery);

    protected override async Task OnInitializedAsync()
    {
        await LoadRules();
    }

    private async Task LoadRules()
    {
        IsLoading = true;
        StateHasChanged();

        try
        {
            AllRules = (await RuleRegistry.GetAllRulesAsync()).ToList();
            await ApplyFilters();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading rules: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task ApplyFilters()
    {
        var filtered = AllRules.AsEnumerable();

        // Category filter
        if (!string.IsNullOrEmpty(SelectedCategory) && Enum.TryParse<AuditRuleCategory>(SelectedCategory, out var category))
        {
            filtered = filtered.Where(r => r.Category == category);
        }

        // Severity filter
        if (!string.IsNullOrEmpty(SelectedSeverity) && Enum.TryParse<AuditSeverity>(SelectedSeverity, out var severity))
        {
            filtered = filtered.Where(r => r.Severity == severity);
        }

        // Status filter
        if (!string.IsNullOrEmpty(SelectedStatus))
        {
            filtered = SelectedStatus switch
            {
                "enabled" => filtered.Where(r => r.IsEnabled),
                "disabled" => filtered.Where(r => !r.IsEnabled),
                "custom" => filtered.Where(r => r.RuleId.StartsWith("CUSTOM-")),
                "builtin" => filtered.Where(r => !r.RuleId.StartsWith("CUSTOM-")),
                _ => filtered
            };
        }

        // Search filter
        if (!string.IsNullOrEmpty(SearchQuery))
        {
            var query = SearchQuery.ToLowerInvariant();
            filtered = filtered.Where(r =>
                r.Name.ToLowerInvariant().Contains(query) ||
                r.Description.ToLowerInvariant().Contains(query) ||
                r.RuleId.ToLowerInvariant().Contains(query));
        }

        FilteredRules = filtered.OrderBy(r => r.Name).ToList();
        CurrentPage = 1;
        StateHasChanged();
    }

    private async Task RefreshRules()
    {
        await LoadRules();
    }

    private async Task CreateNewRule()
    {
        // Navigate to rule creation page
        // Navigation.NavigateTo("/rules/create");
    }

    private async Task TestAllRules()
    {
        // Run tests for all rules
        var options = new RuleTestOptions
        {
            IncludeUnitTests = true,
            IncludeIntegrationTests = false,
            IncludePerformanceTests = false
        };

        // await RuleTestingService.RunComprehensiveTestSuiteAsync(options);
    }

    private async Task HandleSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyFilters();
        }
    }

    private async Task GoToPage(int page)
    {
        if (page >= 1 && page <= TotalPages)
        {
            CurrentPage = page;
            StateHasChanged();
        }
    }

    private async Task ClearFilters()
    {
        SelectedCategory = string.Empty;
        SelectedSeverity = string.Empty;
        SelectedStatus = string.Empty;
        SearchQuery = string.Empty;
        await ApplyFilters();
    }

    private void HandleRuleSelection(string ruleId, bool isSelected)
    {
        if (isSelected)
        {
            SelectedRules.Add(ruleId);
        }
        else
        {
            SelectedRules.Remove(ruleId);
        }
        StateHasChanged();
    }

    private async Task EnableSelectedRules()
    {
        foreach (var ruleId in SelectedRules)
        {
            await RuleRegistry.SetRuleEnabledAsync(ruleId, true);
        }
        SelectedRules.Clear();
        await LoadRules();
    }

    private async Task DisableSelectedRules()
    {
        foreach (var ruleId in SelectedRules)
        {
            await RuleRegistry.SetRuleEnabledAsync(ruleId, false);
        }
        SelectedRules.Clear();
        await LoadRules();
    }

    private async Task TestSelectedRules()
    {
        // Test selected rules
        foreach (var ruleId in SelectedRules)
        {
            await RuleTestingService.RunRuleUnitTestsAsync(ruleId);
        }
    }

    private async Task DeleteSelectedRules()
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Are you sure you want to delete {SelectedRules.Count} selected rules?");

        if (confirmed)
        {
            foreach (var ruleId in SelectedRules)
            {
                await RuleRegistry.UnregisterRuleAsync(ruleId);
            }
            SelectedRules.Clear();
            await LoadRules();
        }
    }

    private async Task ViewRuleDetails(string ruleId)
    {
        // Navigate to rule details page
        // Navigation.NavigateTo($"/rules/{ruleId}");
    }

    private async Task EditRule(string ruleId)
    {
        // Navigate to rule edit page
        // Navigation.NavigateTo($"/rules/{ruleId}/edit");
    }

    private async Task TestRule(string ruleId)
    {
        // Run tests for specific rule
        await RuleTestingService.RunRuleUnitTestsAsync(ruleId);
    }

    private async Task ToggleRuleEnabled(string ruleId)
    {
        var rule = AllRules.FirstOrDefault(r => r.RuleId == ruleId);
        if (rule != null)
        {
            await RuleRegistry.SetRuleEnabledAsync(ruleId, !rule.IsEnabled);
            await LoadRules();
        }
    }

    private async Task DeleteRule(string ruleId)
    {
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            "Are you sure you want to delete this rule?");

        if (confirmed)
        {
            await RuleRegistry.UnregisterRuleAsync(ruleId);
            await LoadRules();
        }
    }

    private async Task DuplicateRule(string ruleId)
    {
        // Navigate to rule duplication page
        // Navigation.NavigateTo($"/rules/{ruleId}/duplicate");
    }
}

<style>
    .rule-management {
        padding: 0;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .header-content {
        flex: 1;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        margin-right: 0.75rem;
        color: #007bff;
    }

    .page-description {
        color: #6c757d;
        margin: 0;
        font-size: 0.95rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .rule-stats {
        margin-bottom: 2rem;
    }

    .stat-card {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    .rule-filters {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-group {
        margin-bottom: 0;
    }

    .filter-group .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .rule-list-container {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .list-header {
        background-color: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .list-header h5 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .bulk-actions .dropdown-toggle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        gap: 1rem;
    }

    .loading-text {
        color: #6c757d;
        margin: 0;
    }

    .no-rules-message {
        text-align: center;
        padding: 3rem 2rem;
    }

    .no-rules-message i {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .no-rules-message h5 {
        color: #495057;
        margin-bottom: 0.75rem;
    }

    .no-rules-message p {
        margin-bottom: 1.5rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .pagination-container {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .pagination-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border: none;
        color: #6c757d;
        padding: 0.5rem 0.75rem;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: #fff;
    }

    .pagination .page-link:hover {
        background-color: #e9ecef;
        color: #495057;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .header-actions {
            justify-content: stretch;
        }

        .header-actions .btn {
            flex: 1;
        }

        .rule-filters {
            padding: 1rem;
        }

        .stat-card {
            margin-bottom: 1rem;
        }

        .pagination-container {
            flex-direction: column;
            text-align: center;
        }
    }
</style>
