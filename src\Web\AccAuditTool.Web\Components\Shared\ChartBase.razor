@inject IJSRuntime JSRuntime

<div class="chart-container @CssClass" style="@Style">
    <canvas id="@CanvasId" width="@Width" height="@Height"></canvas>
    
    @if (ShowLoading && IsLoading)
    {
        <div class="chart-loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading chart...</span>
            </div>
        </div>
    }
    
    @if (ShowNoData && !HasData)
    {
        <div class="chart-no-data">
            <i class="fas fa-chart-bar text-muted"></i>
            <p class="text-muted">@NoDataMessage</p>
        </div>
    }
</div>

@code {
    [Parameter] public string ChartType { get; set; } = "line";
    [Parameter] public object? ChartData { get; set; }
    [Parameter] public object? ChartOptions { get; set; }
    [Parameter] public string CssClass { get; set; } = string.Empty;
    [Parameter] public string Style { get; set; } = string.Empty;
    [Parameter] public int Width { get; set; } = 400;
    [Parameter] public int Height { get; set; } = 200;
    [Parameter] public bool ShowLoading { get; set; } = true;
    [Parameter] public bool ShowNoData { get; set; } = true;
    [Parameter] public string NoDataMessage { get; set; } = "No data available";
    [Parameter] public bool IsLoading { get; set; } = false;
    [Parameter] public bool HasData { get; set; } = true;
    [Parameter] public EventCallback<object> OnChartClick { get; set; }
    [Parameter] public EventCallback<object> OnChartHover { get; set; }

    private string CanvasId = Guid.NewGuid().ToString("N")[..8];
    private bool ChartInitialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && ChartData != null)
        {
            await InitializeChart();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ChartInitialized && ChartData != null)
        {
            await UpdateChart();
        }
    }

    private async Task InitializeChart()
    {
        if (ChartData == null) return;

        var options = GetDefaultOptions();
        if (ChartOptions != null)
        {
            // Merge custom options with defaults
            options = ChartOptions;
        }

        await JSRuntime.InvokeVoidAsync("initializeChart", CanvasId, ChartType, ChartData, options);
        ChartInitialized = true;
    }

    private async Task UpdateChart()
    {
        if (ChartData == null) return;

        await JSRuntime.InvokeVoidAsync("updateChart", CanvasId, ChartData);
    }

    public async Task RefreshChart()
    {
        if (ChartInitialized)
        {
            await UpdateChart();
        }
        else
        {
            await InitializeChart();
        }
    }

    public async Task DestroyChart()
    {
        if (ChartInitialized)
        {
            await JSRuntime.InvokeVoidAsync("destroyChart", CanvasId);
            ChartInitialized = false;
        }
    }

    private object GetDefaultOptions()
    {
        return new
        {
            responsive = true,
            maintainAspectRatio = false,
            plugins = new
            {
                legend = new
                {
                    position = "top"
                },
                tooltip = new
                {
                    enabled = true,
                    mode = "index",
                    intersect = false
                }
            },
            scales = GetDefaultScales(),
            onClick = OnChartClick.HasDelegate ? "chartClickHandler" : null,
            onHover = OnChartHover.HasDelegate ? "chartHoverHandler" : null
        };
    }

    private object GetDefaultScales()
    {
        return ChartType switch
        {
            "line" or "bar" => new
            {
                x = new
                {
                    display = true,
                    grid = new { display = false }
                },
                y = new
                {
                    display = true,
                    beginAtZero = true,
                    grid = new { color = "rgba(0, 0, 0, 0.1)" }
                }
            },
            "pie" or "doughnut" => new { },
            _ => new
            {
                x = new { display = true },
                y = new { display = true, beginAtZero = true }
            }
        };
    }
}

<script>
    window.chartInstances = window.chartInstances || {};

    window.initializeChart = function (canvasId, type, data, options) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        // Destroy existing chart if it exists
        if (window.chartInstances[canvasId]) {
            window.chartInstances[canvasId].destroy();
        }

        const chart = new Chart(ctx, {
            type: type,
            data: data,
            options: options
        });

        window.chartInstances[canvasId] = chart;
    };

    window.updateChart = function (canvasId, data) {
        const chart = window.chartInstances[canvasId];
        if (chart) {
            chart.data = data;
            chart.update('active');
        }
    };

    window.destroyChart = function (canvasId) {
        const chart = window.chartInstances[canvasId];
        if (chart) {
            chart.destroy();
            delete window.chartInstances[canvasId];
        }
    };

    // Global chart event handlers
    window.chartClickHandler = function (event, elements, chart) {
        if (elements.length > 0) {
            const element = elements[0];
            const datasetIndex = element.datasetIndex;
            const index = element.index;
            const value = chart.data.datasets[datasetIndex].data[index];
            const label = chart.data.labels[index];
            
            // Trigger Blazor event
            DotNet.invokeMethodAsync('AccAuditTool.Web', 'OnChartClick', {
                datasetIndex: datasetIndex,
                index: index,
                value: value,
                label: label
            });
        }
    };

    window.chartHoverHandler = function (event, elements, chart) {
        if (elements.length > 0) {
            const element = elements[0];
            const datasetIndex = element.datasetIndex;
            const index = element.index;
            
            // Trigger Blazor event
            DotNet.invokeMethodAsync('AccAuditTool.Web', 'OnChartHover', {
                datasetIndex: datasetIndex,
                index: index
            });
        }
    };

    // Cleanup on page unload
    window.addEventListener('beforeunload', function () {
        Object.keys(window.chartInstances).forEach(function (canvasId) {
            window.destroyChart(canvasId);
        });
    });
</script>

<style>
    .chart-container {
        position: relative;
        width: 100%;
        height: 300px;
    }

    .chart-container canvas {
        max-width: 100%;
        max-height: 100%;
    }

    .chart-loading {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.8);
        z-index: 10;
    }

    .chart-no-data {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 10;
    }

    .chart-no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .chart-no-data p {
        margin: 0;
        font-size: 0.875rem;
    }

    /* Responsive chart containers */
    .chart-container.chart-sm {
        height: 200px;
    }

    .chart-container.chart-md {
        height: 300px;
    }

    .chart-container.chart-lg {
        height: 400px;
    }

    .chart-container.chart-xl {
        height: 500px;
    }

    /* Chart themes */
    .chart-container.chart-dark {
        background-color: #2d3748;
        border-radius: 0.375rem;
    }

    .chart-container.chart-light {
        background-color: #f7fafc;
        border-radius: 0.375rem;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }

        .chart-container.chart-lg,
        .chart-container.chart-xl {
            height: 300px;
        }
    }

    @media (max-width: 576px) {
        .chart-container {
            height: 200px;
        }

        .chart-no-data i {
            font-size: 2rem;
        }

        .chart-no-data p {
            font-size: 0.8rem;
        }
    }
</style>
