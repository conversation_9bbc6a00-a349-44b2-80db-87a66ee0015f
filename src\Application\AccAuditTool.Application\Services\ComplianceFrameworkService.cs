using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for managing compliance frameworks and their associated rules
/// </summary>
public interface IComplianceFrameworkService
{
    /// <summary>
    /// Get all available compliance frameworks
    /// </summary>
    Task<IEnumerable<ComplianceFramework>> GetAvailableFrameworksAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get a specific compliance framework
    /// </summary>
    Task<ComplianceFramework?> GetFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rules for a specific compliance framework
    /// </summary>
    Task<IEnumerable<IAuditRule>> GetFrameworkRulesAsync(string frameworkId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Enable a compliance framework (enables all its rules)
    /// </summary>
    Task EnableFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Disable a compliance framework (disables all its rules)
    /// </summary>
    Task DisableFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get compliance assessment for a framework
    /// </summary>
    Task<ComplianceAssessment> GetComplianceAssessmentAsync(string frameworkId, Guid auditRunId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate compliance report
    /// </summary>
    Task<ComplianceReport> GenerateComplianceReportAsync(string frameworkId, Guid auditRunId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Map audit findings to compliance controls
    /// </summary>
    Task<Dictionary<string, List<AuditFinding>>> MapFindingsToControlsAsync(string frameworkId, IEnumerable<AuditFinding> findings, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of compliance framework service
/// </summary>
public class ComplianceFrameworkService : IComplianceFrameworkService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IRuleRegistry _ruleRegistry;
    private readonly ILogger<ComplianceFrameworkService> _logger;
    private readonly Dictionary<string, ComplianceFramework> _frameworks;

    public ComplianceFrameworkService(
        IUnitOfWork unitOfWork,
        IRuleRegistry ruleRegistry,
        ILogger<ComplianceFrameworkService> logger)
    {
        _unitOfWork = unitOfWork;
        _ruleRegistry = ruleRegistry;
        _logger = logger;
        _frameworks = InitializeFrameworks();
    }

    public async Task<IEnumerable<ComplianceFramework>> GetAvailableFrameworksAsync(CancellationToken cancellationToken = default)
    {
        return _frameworks.Values;
    }

    public async Task<ComplianceFramework?> GetFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default)
    {
        _frameworks.TryGetValue(frameworkId, out var framework);
        return framework;
    }

    public async Task<IEnumerable<IAuditRule>> GetFrameworkRulesAsync(string frameworkId, CancellationToken cancellationToken = default)
    {
        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        if (framework == null)
        {
            return Enumerable.Empty<IAuditRule>();
        }

        var allRules = await _ruleRegistry.GetAllRulesAsync(cancellationToken);
        var frameworkRules = allRules.Where(rule => framework.RuleIds.Contains(rule.RuleId));

        return frameworkRules;
    }

    public async Task EnableFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Enabling compliance framework {FrameworkId}", frameworkId);

        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        if (framework == null)
        {
            throw new ArgumentException($"Framework {frameworkId} not found");
        }

        foreach (var ruleId in framework.RuleIds)
        {
            try
            {
                await _ruleRegistry.SetRuleEnabledAsync(ruleId, true, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to enable rule {RuleId} for framework {FrameworkId}", ruleId, frameworkId);
            }
        }

        _logger.LogInformation("Successfully enabled compliance framework {FrameworkId}", frameworkId);
    }

    public async Task DisableFrameworkAsync(string frameworkId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Disabling compliance framework {FrameworkId}", frameworkId);

        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        if (framework == null)
        {
            throw new ArgumentException($"Framework {frameworkId} not found");
        }

        foreach (var ruleId in framework.RuleIds)
        {
            try
            {
                await _ruleRegistry.SetRuleEnabledAsync(ruleId, false, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to disable rule {RuleId} for framework {FrameworkId}", ruleId, frameworkId);
            }
        }

        _logger.LogInformation("Successfully disabled compliance framework {FrameworkId}", frameworkId);
    }

    public async Task<ComplianceAssessment> GetComplianceAssessmentAsync(string frameworkId, Guid auditRunId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Generating compliance assessment for framework {FrameworkId} and audit run {AuditRunId}", frameworkId, auditRunId);

        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        if (framework == null)
        {
            throw new ArgumentException($"Framework {frameworkId} not found");
        }

        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun == null)
        {
            throw new ArgumentException($"Audit run {auditRunId} not found");
        }

        // Get findings for framework rules
        var findings = await _unitOfWork.AuditFindings.FindAsync(
            f => f.AuditRunId == auditRunId && framework.RuleIds.Contains(f.RuleId), cancellationToken);

        var assessment = new ComplianceAssessment
        {
            FrameworkId = frameworkId,
            FrameworkName = framework.Name,
            AuditRunId = auditRunId,
            AssessmentDate = DateTime.UtcNow,
            TotalControls = framework.Controls.Count,
            TotalFindings = findings.Count()
        };

        // Assess each control
        foreach (var control in framework.Controls)
        {
            var controlAssessment = await AssessControlAsync(control, findings, cancellationToken);
            assessment.ControlAssessments[control.Id] = controlAssessment;

            // Update overall compliance status
            if (controlAssessment.Status == ComplianceStatus.NonCompliant)
            {
                assessment.NonCompliantControls++;
            }
            else if (controlAssessment.Status == ComplianceStatus.PartiallyCompliant)
            {
                assessment.PartiallyCompliantControls++;
            }
            else
            {
                assessment.CompliantControls++;
            }
        }

        // Calculate overall compliance percentage
        assessment.CompliancePercentage = assessment.TotalControls > 0 
            ? (double)(assessment.CompliantControls + (assessment.PartiallyCompliantControls * 0.5)) / assessment.TotalControls * 100
            : 100;

        // Determine overall status
        assessment.OverallStatus = assessment.CompliancePercentage switch
        {
            >= 95 => ComplianceStatus.Compliant,
            >= 70 => ComplianceStatus.PartiallyCompliant,
            _ => ComplianceStatus.NonCompliant
        };

        return assessment;
    }

    public async Task<ComplianceReport> GenerateComplianceReportAsync(string frameworkId, Guid auditRunId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Generating compliance report for framework {FrameworkId} and audit run {AuditRunId}", frameworkId, auditRunId);

        var assessment = await GetComplianceAssessmentAsync(frameworkId, auditRunId, cancellationToken);
        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);

        var report = new ComplianceReport
        {
            FrameworkId = frameworkId,
            FrameworkName = framework!.Name,
            AuditRunId = auditRunId,
            GeneratedAt = DateTime.UtcNow,
            Assessment = assessment,
            ExecutiveSummary = GenerateExecutiveSummary(assessment),
            Recommendations = GenerateRecommendations(assessment, framework),
            AuditScope = auditRun!.Scope,
            AuditPeriod = new AuditPeriod
            {
                StartDate = auditRun.StartedAt,
                EndDate = auditRun.CompletedAt ?? DateTime.UtcNow
            }
        };

        // Add detailed findings by control
        var findings = await _unitOfWork.AuditFindings.FindAsync(
            f => f.AuditRunId == auditRunId && framework.RuleIds.Contains(f.RuleId), cancellationToken);

        report.FindingsByControl = await MapFindingsToControlsAsync(frameworkId, findings, cancellationToken);

        return report;
    }

    public async Task<Dictionary<string, List<AuditFinding>>> MapFindingsToControlsAsync(string frameworkId, IEnumerable<AuditFinding> findings, CancellationToken cancellationToken = default)
    {
        var framework = await GetFrameworkAsync(frameworkId, cancellationToken);
        if (framework == null)
        {
            return new Dictionary<string, List<AuditFinding>>();
        }

        var result = new Dictionary<string, List<AuditFinding>>();

        foreach (var control in framework.Controls)
        {
            var controlFindings = findings.Where(f => control.RuleIds.Contains(f.RuleId)).ToList();
            result[control.Id] = controlFindings;
        }

        return result;
    }

    private async Task<ControlAssessment> AssessControlAsync(ComplianceControl control, IEnumerable<AuditFinding> allFindings, CancellationToken cancellationToken)
    {
        var controlFindings = allFindings.Where(f => control.RuleIds.Contains(f.RuleId)).ToList();

        var assessment = new ControlAssessment
        {
            ControlId = control.Id,
            ControlName = control.Name,
            Description = control.Description,
            TotalFindings = controlFindings.Count,
            CriticalFindings = controlFindings.Count(f => f.Severity == AuditSeverity.Critical),
            HighFindings = controlFindings.Count(f => f.Severity == AuditSeverity.High),
            MediumFindings = controlFindings.Count(f => f.Severity == AuditSeverity.Medium),
            LowFindings = controlFindings.Count(f => f.Severity == AuditSeverity.Low)
        };

        // Determine compliance status based on findings
        assessment.Status = DetermineControlComplianceStatus(assessment);

        // Calculate risk score for the control
        assessment.RiskScore = CalculateControlRiskScore(assessment);

        return assessment;
    }

    private ComplianceStatus DetermineControlComplianceStatus(ControlAssessment assessment)
    {
        if (assessment.CriticalFindings > 0)
        {
            return ComplianceStatus.NonCompliant;
        }

        if (assessment.HighFindings > 2)
        {
            return ComplianceStatus.NonCompliant;
        }

        if (assessment.HighFindings > 0 || assessment.MediumFindings > 5)
        {
            return ComplianceStatus.PartiallyCompliant;
        }

        if (assessment.MediumFindings > 0 || assessment.LowFindings > 10)
        {
            return ComplianceStatus.PartiallyCompliant;
        }

        return ComplianceStatus.Compliant;
    }

    private double CalculateControlRiskScore(ControlAssessment assessment)
    {
        var score = 0.0;
        score += assessment.CriticalFindings * 10.0;
        score += assessment.HighFindings * 7.0;
        score += assessment.MediumFindings * 4.0;
        score += assessment.LowFindings * 1.0;

        return Math.Min(100, score);
    }

    private string GenerateExecutiveSummary(ComplianceAssessment assessment)
    {
        return $"Compliance assessment for {assessment.FrameworkName} shows {assessment.CompliancePercentage:F1}% overall compliance. " +
               $"Out of {assessment.TotalControls} controls, {assessment.CompliantControls} are compliant, " +
               $"{assessment.PartiallyCompliantControls} are partially compliant, and {assessment.NonCompliantControls} are non-compliant. " +
               $"Total of {assessment.TotalFindings} findings were identified across all controls.";
    }

    private List<ComplianceRecommendation> GenerateRecommendations(ComplianceAssessment assessment, ComplianceFramework framework)
    {
        var recommendations = new List<ComplianceRecommendation>();

        // High-priority recommendations for non-compliant controls
        var nonCompliantControls = assessment.ControlAssessments.Values
            .Where(ca => ca.Status == ComplianceStatus.NonCompliant)
            .OrderByDescending(ca => ca.RiskScore)
            .Take(5);

        foreach (var control in nonCompliantControls)
        {
            recommendations.Add(new ComplianceRecommendation
            {
                Priority = RecommendationPriority.High,
                ControlId = control.ControlId,
                Title = $"Address non-compliance in {control.ControlName}",
                Description = $"Control {control.ControlName} has {control.CriticalFindings} critical and {control.HighFindings} high severity findings that must be addressed immediately.",
                ExpectedImpact = "High",
                EstimatedEffort = "Medium to High"
            });
        }

        // Medium-priority recommendations for partially compliant controls
        var partiallyCompliantControls = assessment.ControlAssessments.Values
            .Where(ca => ca.Status == ComplianceStatus.PartiallyCompliant)
            .OrderByDescending(ca => ca.RiskScore)
            .Take(3);

        foreach (var control in partiallyCompliantControls)
        {
            recommendations.Add(new ComplianceRecommendation
            {
                Priority = RecommendationPriority.Medium,
                ControlId = control.ControlId,
                Title = $"Improve compliance for {control.ControlName}",
                Description = $"Control {control.ControlName} has {control.HighFindings} high and {control.MediumFindings} medium severity findings that should be addressed.",
                ExpectedImpact = "Medium",
                EstimatedEffort = "Medium"
            });
        }

        // General recommendations
        if (assessment.CompliancePercentage < 80)
        {
            recommendations.Add(new ComplianceRecommendation
            {
                Priority = RecommendationPriority.High,
                Title = "Implement comprehensive compliance program",
                Description = "Overall compliance is below acceptable levels. Consider implementing a formal compliance management program.",
                ExpectedImpact = "High",
                EstimatedEffort = "High"
            });
        }

        return recommendations;
    }

    private Dictionary<string, ComplianceFramework> InitializeFrameworks()
    {
        var frameworks = new Dictionary<string, ComplianceFramework>();

        // ISO 27001 Framework
        frameworks["ISO27001"] = new ComplianceFramework
        {
            Id = "ISO27001",
            Name = "ISO 27001:2013 Information Security Management",
            Description = "International standard for information security management systems",
            Version = "2013",
            Controls = GetISO27001Controls(),
            RuleIds = new List<string> { "SEC-001", "SEC-002", "COMP-001" }
        };

        // SOC 2 Framework
        frameworks["SOC2"] = new ComplianceFramework
        {
            Id = "SOC2",
            Name = "SOC 2 Type II",
            Description = "Service Organization Control 2 for service organizations",
            Version = "2017",
            Controls = GetSOC2Controls(),
            RuleIds = new List<string> { "SEC-001", "SEC-002", "COMP-001" }
        };

        // NIST Cybersecurity Framework
        frameworks["NIST-CSF"] = new ComplianceFramework
        {
            Id = "NIST-CSF",
            Name = "NIST Cybersecurity Framework",
            Description = "Framework for improving critical infrastructure cybersecurity",
            Version = "1.1",
            Controls = GetNISTCSFControls(),
            RuleIds = new List<string> { "SEC-001", "SEC-002", "COMP-001" }
        };

        return frameworks;
    }

    private List<ComplianceControl> GetISO27001Controls()
    {
        return new List<ComplianceControl>
        {
            new ComplianceControl
            {
                Id = "A.9.1.1",
                Name = "Access control policy",
                Description = "An access control policy shall be established, documented and reviewed based on business and information security requirements.",
                RuleIds = new List<string> { "SEC-001", "COMP-001" }
            },
            new ComplianceControl
            {
                Id = "A.9.2.1",
                Name = "User registration and de-registration",
                Description = "A formal user registration and de-registration process shall be implemented to enable assignment of access rights.",
                RuleIds = new List<string> { "SEC-002" }
            },
            new ComplianceControl
            {
                Id = "A.9.2.5",
                Name = "Review of user access rights",
                Description = "Asset owners shall review users' access rights at regular intervals.",
                RuleIds = new List<string> { "SEC-001", "SEC-002" }
            }
        };
    }

    private List<ComplianceControl> GetSOC2Controls()
    {
        return new List<ComplianceControl>
        {
            new ComplianceControl
            {
                Id = "CC6.1",
                Name = "Logical and Physical Access Controls",
                Description = "The entity implements logical and physical access controls to protect against threats from sources outside its system boundaries.",
                RuleIds = new List<string> { "SEC-001", "COMP-001" }
            },
            new ComplianceControl
            {
                Id = "CC6.2",
                Name = "User Access Management",
                Description = "Prior to issuing system credentials and granting system access, the entity registers and authorizes new internal and external users.",
                RuleIds = new List<string> { "SEC-002" }
            },
            new ComplianceControl
            {
                Id = "CC6.3",
                Name = "User Access Reviews",
                Description = "The entity authorizes, modifies, or removes access to data, software, functions, and other protected information assets.",
                RuleIds = new List<string> { "SEC-001", "SEC-002" }
            }
        };
    }

    private List<ComplianceControl> GetNISTCSFControls()
    {
        return new List<ComplianceControl>
        {
            new ComplianceControl
            {
                Id = "PR.AC-1",
                Name = "Identity and Access Management",
                Description = "Identities and credentials are issued, managed, verified, revoked, and audited for authorized devices, users and processes.",
                RuleIds = new List<string> { "SEC-001", "SEC-002", "COMP-001" }
            },
            new ComplianceControl
            {
                Id = "PR.AC-4",
                Name = "Access Permissions and Authorizations",
                Description = "Access permissions and authorizations are managed, incorporating the principles of least privilege and separation of duties.",
                RuleIds = new List<string> { "SEC-001", "COMP-001" }
            },
            new ComplianceControl
            {
                Id = "PR.AC-6",
                Name = "Identity Authentication",
                Description = "Identities are proofed and bound to credentials and asserted in interactions.",
                RuleIds = new List<string> { "SEC-002" }
            }
        };
    }
}
