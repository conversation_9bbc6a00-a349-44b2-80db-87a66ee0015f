Write-Host "Starting ACC Audit Tool Web Application..." -ForegroundColor Green

# Change to the web project directory
Set-Location "src\Web\AccAuditTool.Web"
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Yellow

# Build the application
Write-Host "Building application..." -ForegroundColor Yellow
dotnet build

if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Build successful! Starting application..." -ForegroundColor Green
Write-Host "The application will be available at:" -ForegroundColor Cyan
Write-Host "  - HTTP:  http://localhost:5000" -ForegroundColor Cyan
Write-Host "  - HTTPS: https://localhost:5001" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow

# Run the application
dotnet run --urls "http://localhost:5000;https://localhost:5001"
