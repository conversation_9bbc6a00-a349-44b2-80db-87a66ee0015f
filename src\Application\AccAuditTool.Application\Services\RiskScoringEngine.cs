using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for calculating risk scores based on audit findings
/// </summary>
public interface IRiskScoringEngine
{
    /// <summary>
    /// Calculate risk score for a specific entity (user, project, etc.)
    /// </summary>
    Task<RiskScore> CalculateEntityRiskScoreAsync(string entityType, string entityId, IEnumerable<AuditFinding> findings, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate overall risk score for an audit run
    /// </summary>
    Task<RiskScore> CalculateOverallRiskScoreAsync(Guid auditRunId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Calculate risk score for a project
    /// </summary>
    Task<RiskScore> CalculateProjectRiskScoreAsync(Guid projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get risk trends over time
    /// </summary>
    Task<RiskTrend> GetRiskTrendAsync(string entityType, string entityId, TimeSpan period, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update risk scoring configuration
    /// </summary>
    Task UpdateRiskScoringConfigurationAsync(RiskScoringConfiguration configuration, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of risk scoring engine
/// </summary>
public class RiskScoringEngine : IRiskScoringEngine
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RiskScoringEngine> _logger;
    private RiskScoringConfiguration _configuration;

    public RiskScoringEngine(IUnitOfWork unitOfWork, ILogger<RiskScoringEngine> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _configuration = GetDefaultConfiguration();
    }

    public async Task<RiskScore> CalculateEntityRiskScoreAsync(string entityType, string entityId, IEnumerable<AuditFinding> findings, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Calculating risk score for {EntityType} {EntityId} with {FindingCount} findings", 
            entityType, entityId, findings.Count());

        var riskScore = new RiskScore
        {
            EntityType = entityType,
            EntityId = entityId,
            CalculatedAt = DateTime.UtcNow
        };

        if (!findings.Any())
        {
            riskScore.Score = 0;
            riskScore.Level = RiskLevel.Low;
            riskScore.Confidence = 1.0;
            return riskScore;
        }

        // Calculate base score from findings
        var baseScore = CalculateBaseScore(findings);
        
        // Apply entity-specific multipliers
        var entityMultiplier = GetEntityTypeMultiplier(entityType);
        
        // Apply temporal factors (recent findings are more critical)
        var temporalFactor = CalculateTemporalFactor(findings);
        
        // Apply frequency factor (repeated issues increase risk)
        var frequencyFactor = CalculateFrequencyFactor(findings);
        
        // Calculate final score
        var finalScore = baseScore * entityMultiplier * temporalFactor * frequencyFactor;
        
        // Normalize to 0-100 scale
        riskScore.Score = Math.Min(100, Math.Max(0, finalScore));
        riskScore.Level = DetermineRiskLevel(riskScore.Score);
        riskScore.Confidence = CalculateConfidence(findings);
        
        // Calculate component scores
        riskScore.ComponentScores = CalculateComponentScores(findings);
        
        // Add metadata
        riskScore.Metadata = new Dictionary<string, object>
        {
            ["BaseScore"] = baseScore,
            ["EntityMultiplier"] = entityMultiplier,
            ["TemporalFactor"] = temporalFactor,
            ["FrequencyFactor"] = frequencyFactor,
            ["FindingCount"] = findings.Count(),
            ["SeverityDistribution"] = GetSeverityDistribution(findings)
        };

        _logger.LogDebug("Calculated risk score {Score} ({Level}) for {EntityType} {EntityId}", 
            riskScore.Score, riskScore.Level, entityType, entityId);

        return riskScore;
    }

    public async Task<RiskScore> CalculateOverallRiskScoreAsync(Guid auditRunId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Calculating overall risk score for audit run {AuditRunId}", auditRunId);

        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun == null)
        {
            throw new ArgumentException($"Audit run {auditRunId} not found");
        }

        var findings = await _unitOfWork.AuditFindings.FindAsync(f => f.AuditRunId == auditRunId, cancellationToken);
        
        var riskScore = new RiskScore
        {
            EntityType = "AuditRun",
            EntityId = auditRunId.ToString(),
            CalculatedAt = DateTime.UtcNow
        };

        if (!findings.Any())
        {
            riskScore.Score = 0;
            riskScore.Level = RiskLevel.Low;
            riskScore.Confidence = 1.0;
            return riskScore;
        }

        // Calculate weighted average based on finding severity and count
        var totalWeight = 0.0;
        var weightedScore = 0.0;

        var findingsByCategory = findings.GroupBy(f => f.Category);
        foreach (var categoryGroup in findingsByCategory)
        {
            var categoryWeight = GetCategoryWeight(categoryGroup.Key);
            var categoryScore = CalculateBaseScore(categoryGroup);
            
            totalWeight += categoryWeight;
            weightedScore += categoryScore * categoryWeight;
        }

        riskScore.Score = totalWeight > 0 ? Math.Min(100, weightedScore / totalWeight) : 0;
        riskScore.Level = DetermineRiskLevel(riskScore.Score);
        riskScore.Confidence = CalculateConfidence(findings);
        riskScore.ComponentScores = CalculateComponentScores(findings);

        // Add comprehensive metadata
        riskScore.Metadata = new Dictionary<string, object>
        {
            ["AuditRunId"] = auditRunId,
            ["TotalFindings"] = findings.Count(),
            ["CriticalFindings"] = findings.Count(f => f.Severity == AuditSeverity.Critical),
            ["HighFindings"] = findings.Count(f => f.Severity == AuditSeverity.High),
            ["MediumFindings"] = findings.Count(f => f.Severity == AuditSeverity.Medium),
            ["LowFindings"] = findings.Count(f => f.Severity == AuditSeverity.Low),
            ["CategoryBreakdown"] = findingsByCategory.ToDictionary(g => g.Key.ToString(), g => g.Count()),
            ["AuditScope"] = auditRun.Scope,
            ["AuditDate"] = auditRun.StartedAt
        };

        _logger.LogInformation("Calculated overall risk score {Score} ({Level}) for audit run {AuditRunId} with {FindingCount} findings", 
            riskScore.Score, riskScore.Level, auditRunId, findings.Count());

        return riskScore;
    }

    public async Task<RiskScore> CalculateProjectRiskScoreAsync(Guid projectId, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Calculating project risk score for project {ProjectId}", projectId);

        var project = await _unitOfWork.Projects.GetByIdAsync(projectId, cancellationToken);
        if (project == null)
        {
            throw new ArgumentException($"Project {projectId} not found");
        }

        // Get recent findings for the project (last 90 days)
        var cutoffDate = DateTime.UtcNow.AddDays(-90);
        var findings = await _unitOfWork.AuditFindings.FindAsync(
            f => f.ProjectId == projectId && f.DetectedAt >= cutoffDate, cancellationToken);

        var riskScore = await CalculateEntityRiskScoreAsync("Project", projectId.ToString(), findings, cancellationToken);
        
        // Add project-specific factors
        var userCount = await _unitOfWork.UserRoles.CountAsync(ur => ur.ProjectId == projectId, cancellationToken);
        var companyCount = await _unitOfWork.ProjectCompanies.CountAsync(pc => pc.ProjectId == projectId, cancellationToken);
        
        // Adjust score based on project complexity
        var complexityMultiplier = CalculateProjectComplexityMultiplier(userCount, companyCount);
        riskScore.Score = Math.Min(100, riskScore.Score * complexityMultiplier);
        riskScore.Level = DetermineRiskLevel(riskScore.Score);

        riskScore.Metadata["ProjectId"] = projectId;
        riskScore.Metadata["ProjectName"] = project.Name;
        riskScore.Metadata["UserCount"] = userCount;
        riskScore.Metadata["CompanyCount"] = companyCount;
        riskScore.Metadata["ComplexityMultiplier"] = complexityMultiplier;

        return riskScore;
    }

    public async Task<RiskTrend> GetRiskTrendAsync(string entityType, string entityId, TimeSpan period, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Getting risk trend for {EntityType} {EntityId} over {Period}", entityType, entityId, period);

        var endDate = DateTime.UtcNow;
        var startDate = endDate - period;
        
        // Get historical audit runs within the period
        var auditRuns = await _unitOfWork.AuditRuns.FindAsync(
            ar => ar.StartedAt >= startDate && ar.StartedAt <= endDate && ar.Status == AuditRunStatus.Completed,
            cancellationToken);

        var trendPoints = new List<RiskTrendPoint>();

        foreach (var auditRun in auditRuns.OrderBy(ar => ar.StartedAt))
        {
            var findings = await _unitOfWork.AuditFindings.FindAsync(
                f => f.AuditRunId == auditRun.Id && 
                     (entityType == "Overall" || (f.AffectedEntity == entityType && f.AffectedEntityId == entityId)),
                cancellationToken);

            var riskScore = await CalculateEntityRiskScoreAsync(entityType, entityId, findings, cancellationToken);
            
            trendPoints.Add(new RiskTrendPoint
            {
                Date = auditRun.StartedAt,
                Score = riskScore.Score,
                Level = riskScore.Level,
                FindingCount = findings.Count()
            });
        }

        var trend = new RiskTrend
        {
            EntityType = entityType,
            EntityId = entityId,
            Period = period,
            TrendPoints = trendPoints,
            CalculatedAt = DateTime.UtcNow
        };

        // Calculate trend direction
        if (trendPoints.Count >= 2)
        {
            var firstScore = trendPoints.First().Score;
            var lastScore = trendPoints.Last().Score;
            var scoreDifference = lastScore - firstScore;

            trend.Direction = scoreDifference switch
            {
                > 5 => TrendDirection.Increasing,
                < -5 => TrendDirection.Decreasing,
                _ => TrendDirection.Stable
            };

            trend.ChangePercentage = firstScore > 0 ? (scoreDifference / firstScore) * 100 : 0;
        }
        else
        {
            trend.Direction = TrendDirection.Insufficient;
        }

        return trend;
    }

    public async Task UpdateRiskScoringConfigurationAsync(RiskScoringConfiguration configuration, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating risk scoring configuration");
        
        // Validate configuration
        ValidateConfiguration(configuration);
        
        _configuration = configuration;
        
        // Optionally persist configuration to database
        // await _unitOfWork.RiskScoringConfigurations.UpdateAsync(configuration, cancellationToken);
        
        _logger.LogInformation("Risk scoring configuration updated successfully");
    }

    private double CalculateBaseScore(IEnumerable<AuditFinding> findings)
    {
        var score = 0.0;
        
        foreach (var finding in findings)
        {
            var severityWeight = _configuration.SeverityWeights.GetValueOrDefault(finding.Severity, 1.0);
            var categoryWeight = _configuration.CategoryWeights.GetValueOrDefault(finding.Category, 1.0);
            
            score += severityWeight * categoryWeight;
        }
        
        return score;
    }

    private double GetEntityTypeMultiplier(string entityType)
    {
        return _configuration.EntityTypeMultipliers.GetValueOrDefault(entityType, 1.0);
    }

    private double CalculateTemporalFactor(IEnumerable<AuditFinding> findings)
    {
        if (!findings.Any()) return 1.0;

        var now = DateTime.UtcNow;
        var avgAge = findings.Average(f => (now - f.DetectedAt).TotalDays);
        
        // Recent findings (< 30 days) get higher weight
        return avgAge switch
        {
            < 7 => 1.5,   // Very recent
            < 30 => 1.2,  // Recent
            < 90 => 1.0,  // Normal
            < 180 => 0.8, // Older
            _ => 0.6      // Very old
        };
    }

    private double CalculateFrequencyFactor(IEnumerable<AuditFinding> findings)
    {
        var ruleFrequency = findings.GroupBy(f => f.RuleId).ToDictionary(g => g.Key, g => g.Count());
        var maxFrequency = ruleFrequency.Values.DefaultIfEmpty(1).Max();
        
        // Higher frequency of same rule violations increases risk
        return 1.0 + (maxFrequency - 1) * 0.1; // 10% increase per additional occurrence
    }

    private RiskLevel DetermineRiskLevel(double score)
    {
        return score switch
        {
            >= 80 => RiskLevel.Critical,
            >= 60 => RiskLevel.High,
            >= 40 => RiskLevel.Medium,
            >= 20 => RiskLevel.Low,
            _ => RiskLevel.Minimal
        };
    }

    private double CalculateConfidence(IEnumerable<AuditFinding> findings)
    {
        var findingCount = findings.Count();
        
        // Confidence increases with more findings, but plateaus
        return findingCount switch
        {
            0 => 0.5,
            1 => 0.6,
            < 5 => 0.7,
            < 10 => 0.8,
            < 20 => 0.9,
            _ => 0.95
        };
    }

    private Dictionary<string, double> CalculateComponentScores(IEnumerable<AuditFinding> findings)
    {
        var componentScores = new Dictionary<string, double>();
        
        var findingsByCategory = findings.GroupBy(f => f.Category);
        foreach (var categoryGroup in findingsByCategory)
        {
            var categoryScore = CalculateBaseScore(categoryGroup);
            componentScores[categoryGroup.Key.ToString()] = Math.Min(100, categoryScore);
        }
        
        return componentScores;
    }

    private Dictionary<string, int> GetSeverityDistribution(IEnumerable<AuditFinding> findings)
    {
        return findings.GroupBy(f => f.Severity)
                      .ToDictionary(g => g.Key.ToString(), g => g.Count());
    }

    private double GetCategoryWeight(AuditFindingCategory category)
    {
        return _configuration.CategoryWeights.GetValueOrDefault(category, 1.0);
    }

    private double CalculateProjectComplexityMultiplier(int userCount, int companyCount)
    {
        // More users and companies = higher complexity = higher risk
        var userFactor = Math.Min(2.0, 1.0 + (userCount / 100.0));
        var companyFactor = Math.Min(1.5, 1.0 + (companyCount / 20.0));
        
        return (userFactor + companyFactor) / 2.0;
    }

    private void ValidateConfiguration(RiskScoringConfiguration configuration)
    {
        if (configuration == null)
            throw new ArgumentNullException(nameof(configuration));
            
        if (configuration.SeverityWeights == null || !configuration.SeverityWeights.Any())
            throw new ArgumentException("Severity weights must be provided");
            
        if (configuration.CategoryWeights == null || !configuration.CategoryWeights.Any())
            throw new ArgumentException("Category weights must be provided");
    }

    private RiskScoringConfiguration GetDefaultConfiguration()
    {
        return new RiskScoringConfiguration
        {
            SeverityWeights = new Dictionary<AuditSeverity, double>
            {
                [AuditSeverity.Critical] = 10.0,
                [AuditSeverity.High] = 7.0,
                [AuditSeverity.Medium] = 4.0,
                [AuditSeverity.Low] = 2.0,
                [AuditSeverity.Info] = 1.0
            },
            CategoryWeights = new Dictionary<AuditFindingCategory, double>
            {
                [AuditFindingCategory.Security] = 1.5,
                [AuditFindingCategory.Compliance] = 1.3,
                [AuditFindingCategory.AccessControl] = 1.4,
                [AuditFindingCategory.DataIntegrity] = 1.2,
                [AuditFindingCategory.Performance] = 0.8,
                [AuditFindingCategory.Configuration] = 1.0
            },
            EntityTypeMultipliers = new Dictionary<string, double>
            {
                ["User"] = 1.0,
                ["Project"] = 1.2,
                ["Company"] = 0.9,
                ["Role"] = 1.1,
                ["Permission"] = 1.3
            }
        };
    }
}
