@using AccAuditTool.Application.Services
@inject IJSRuntime JSRuntime

<div class="risk-trend-chart">
    <canvas id="riskTrendChart-@ChartId" width="400" height="200"></canvas>
</div>

@code {
    [Parameter] public List<RiskTrendPoint> Data { get; set; } = new();
    
    private string ChartId = Guid.NewGuid().ToString("N")[..8];
    private bool ChartInitialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender || !ChartInitialized)
        {
            await InitializeChart();
            ChartInitialized = true;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ChartInitialized)
        {
            await UpdateChart();
        }
    }

    private async Task InitializeChart()
    {
        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("initializeRiskTrendChart", $"riskTrendChart-{ChartId}", chartData);
    }

    private async Task UpdateChart()
    {
        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("updateRiskTrendChart", $"riskTrendChart-{ChartId}", chartData);
    }

    private object PrepareChartData()
    {
        var sortedData = Data.OrderBy(d => d.Date).ToList();
        
        return new
        {
            labels = sortedData.Select(d => d.Date.ToString("MMM dd")).ToArray(),
            datasets = new[]
            {
                new
                {
                    label = "Risk Score",
                    data = sortedData.Select(d => d.Score).ToArray(),
                    borderColor = "rgb(220, 53, 69)",
                    backgroundColor = "rgba(220, 53, 69, 0.1)",
                    borderWidth = 2,
                    fill = true,
                    tension = 0.4,
                    pointBackgroundColor = sortedData.Select(d => GetRiskLevelColor(d.Level)).ToArray(),
                    pointBorderColor = sortedData.Select(d => GetRiskLevelColor(d.Level)).ToArray(),
                    pointRadius = 4,
                    pointHoverRadius = 6
                },
                new
                {
                    label = "Finding Count",
                    data = sortedData.Select(d => d.FindingCount * 5).ToArray(), // Scale for visibility
                    borderColor = "rgb(13, 110, 253)",
                    backgroundColor = "rgba(13, 110, 253, 0.1)",
                    borderWidth = 2,
                    fill = false,
                    tension = 0.4,
                    yAxisID = "y1"
                }
            }
        };
    }

    private string GetRiskLevelColor(RiskLevel level)
    {
        return level switch
        {
            RiskLevel.Critical => "#dc3545",
            RiskLevel.High => "#fd7e14",
            RiskLevel.Medium => "#ffc107",
            RiskLevel.Low => "#20c997",
            RiskLevel.Minimal => "#198754",
            _ => "#6c757d"
        };
    }
}

<script>
    window.initializeRiskTrendChart = function (canvasId, data) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            afterLabel: function(context) {
                                if (context.datasetIndex === 0) {
                                    const riskLevel = getRiskLevel(context.parsed.y);
                                    return `Risk Level: ${riskLevel}`;
                                }
                                if (context.datasetIndex === 1) {
                                    const actualCount = Math.round(context.parsed.y / 5);
                                    return `Actual Count: ${actualCount}`;
                                }
                                return '';
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Risk Score'
                        },
                        min: 0,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Findings'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return Math.round(value / 5);
                            }
                        }
                    }
                }
            }
        });

        // Store chart instance for updates
        window[`chart_${canvasId}`] = chart;
    };

    window.updateRiskTrendChart = function (canvasId, data) {
        const chart = window[`chart_${canvasId}`];
        if (chart) {
            chart.data = data;
            chart.update();
        }
    };

    function getRiskLevel(score) {
        if (score >= 80) return 'Critical';
        if (score >= 60) return 'High';
        if (score >= 40) return 'Medium';
        if (score >= 20) return 'Low';
        return 'Minimal';
    }
</script>

<style>
    .risk-trend-chart {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .risk-trend-chart canvas {
        max-height: 300px;
    }
</style>
