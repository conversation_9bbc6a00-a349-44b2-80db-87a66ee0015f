namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a user in the ACC system
/// </summary>
public class User : BaseEntity
{
    /// <summary>
    /// ACC user identifier from Autodesk system
    /// </summary>
    public string AccUserId { get; set; } = string.Empty;

    /// <summary>
    /// User's email address (primary identifier)
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's display name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    public string? FirstName { get; set; }

    /// <summary>
    /// User's last name
    /// </summary>
    public string? LastName { get; set; }

    /// <summary>
    /// Company the user belongs to
    /// </summary>
    public Guid? CompanyId { get; set; }
    public Company? Company { get; set; }

    /// <summary>
    /// User's status in the system
    /// </summary>
    public UserStatus Status { get; set; } = UserStatus.Active;

    /// <summary>
    /// Last time the user was seen active in ACC
    /// </summary>
    public DateTime? LastSeenAt { get; set; }

    /// <summary>
    /// User's role assignments across projects
    /// </summary>
    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// Direct permissions assigned to the user
    /// </summary>
    public ICollection<Permission> DirectPermissions { get; set; } = new List<Permission>();

    /// <summary>
    /// Audit findings related to this user
    /// </summary>
    public ICollection<AuditFinding> AuditFindings { get; set; } = new List<AuditFinding>();
}

/// <summary>
/// User status enumeration
/// </summary>
public enum UserStatus
{
    Active,
    Inactive,
    Suspended,
    PendingActivation
}
