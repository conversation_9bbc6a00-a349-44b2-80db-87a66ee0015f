using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Infrastructure.Tests.Helpers;

public static class TestDataHelper
{
    public static Account CreateTestAccount(string? accAccountId = null, string? name = null)
    {
        return new Account
        {
            AccAccountId = accAccountId ?? $"test-account-{Guid.NewGuid()}",
            Name = name ?? "Test Account",
            Region = "US",
            SubscriptionType = "Premium",
            Status = AccountStatus.Active
        };
    }

    public static Project CreateTestProject(Account? account = null, string? accProjectId = null, string? name = null)
    {
        account ??= CreateTestAccount();
        
        return new Project
        {
            AccProjectId = accProjectId ?? $"test-project-{Guid.NewGuid()}",
            Name = name ?? "Test Project",
            Description = "A test project for unit testing",
            Account = account,
            AccountId = account.Id,
            Status = ProjectStatus.Active,
            ProjectType = "Building",
            StartDate = DateTime.UtcNow.AddDays(-30),
            Location = "Test City, Test State"
        };
    }

    public static Company CreateTestCompany(string? accCompanyId = null, string? name = null)
    {
        return new Company
        {
            AccCompanyId = accCompanyId ?? $"test-company-{Guid.NewGuid()}",
            Name = name ?? "Test Company",
            Trade = "General Contractor",
            Address = "123 Test Street, Test City, TS 12345",
            Phone = "******-123-4567",
            Website = "https://testcompany.com",
            Status = CompanyStatus.Active
        };
    }

    public static User CreateTestUser(Company? company = null, string? accUserId = null, string? email = null)
    {
        var userId = accUserId ?? $"test-user-{Guid.NewGuid()}";
        
        return new User
        {
            AccUserId = userId,
            Email = email ?? $"{userId}@testcompany.com",
            Name = "Test User",
            FirstName = "Test",
            LastName = "User",
            Company = company,
            CompanyId = company?.Id,
            Status = UserStatus.Active,
            LastSeenAt = DateTime.UtcNow.AddHours(-2)
        };
    }

    public static Role CreateTestRole(string? accRoleId = null, string? name = null)
    {
        return new Role
        {
            AccRoleId = accRoleId ?? $"test-role-{Guid.NewGuid()}",
            Name = name ?? "Test Role",
            Description = "A test role for unit testing",
            Type = RoleType.Custom,
            Level = RoleLevel.Project,
            ServiceKey = "docs",
            Version = "1.0",
            Tags = "test,automation",
            ComplianceFrameworks = "ISO27001,SOC2"
        };
    }

    public static Permission CreateTestPermission(
        Project? project = null,
        User? user = null,
        Role? role = null,
        Resource? resource = null)
    {
        project ??= CreateTestProject();
        
        return new Permission
        {
            Project = project,
            ProjectId = project.Id,
            User = user,
            UserId = user?.Id,
            Role = role,
            RoleId = role?.Id,
            Resource = resource,
            ResourceId = resource?.Id,
            AccResourceId = resource?.AccResourceId ?? $"test-resource-{Guid.NewGuid()}",
            ResourceType = ResourceType.Folder,
            Actions = "[\"view\", \"download\", \"upload\"]",
            Source = PermissionSource.Direct,
            GrantedAt = DateTime.UtcNow.AddDays(-7),
            GrantedBy = "<EMAIL>"
        };
    }

    public static Resource CreateTestResource(Project? project = null, Resource? parent = null)
    {
        project ??= CreateTestProject();
        
        return new Resource
        {
            AccResourceId = $"test-resource-{Guid.NewGuid()}",
            Name = "Test Folder",
            Path = parent != null ? $"{parent.Path}/Test Folder" : "/Test Folder",
            Type = ResourceType.Folder,
            Project = project,
            ProjectId = project.Id,
            ParentResource = parent,
            ParentResourceId = parent?.Id,
            LastModifiedAt = DateTime.UtcNow.AddDays(-1),
            LastModifiedBy = "<EMAIL>"
        };
    }

    public static AuditConfiguration CreateTestAuditConfiguration(Account? account = null)
    {
        account ??= CreateTestAccount();
        
        return new AuditConfiguration
        {
            Name = "Test Audit Configuration",
            Description = "A test audit configuration for unit testing",
            Account = account,
            AccountId = account.Id,
            IsDefault = true,
            Settings = "{\"enabledRules\": [\"all\"]}",
            Schedule = "0 2 * * 1", // Weekly on Monday at 2 AM
            IsScheduleEnabled = false,
            NotificationSettings = "{\"emailEnabled\": true, \"recipients\": [\"<EMAIL>\"]}"
        };
    }

    public static AuditRule CreateTestAuditRule(string? ruleId = null)
    {
        return new AuditRule
        {
            RuleId = ruleId ?? $"TEST_RULE_{Guid.NewGuid():N}",
            Name = "Test Audit Rule",
            Description = "A test audit rule for unit testing",
            Category = "Security",
            DefaultSeverity = FindingSeverity.Medium,
            Type = RuleType.Custom,
            IsEnabled = true,
            Configuration = "{\"threshold\": 5}",
            ImplementationClass = "AccAuditTool.Rules.TestRule",
            Version = "1.0",
            Tags = "test,security",
            ComplianceFrameworks = "ISO27001,SOC2",
            Documentation = "This is a test rule for unit testing purposes."
        };
    }

    public static AuditRun CreateTestAuditRun(
        Project? project = null,
        Account? account = null,
        AuditConfiguration? configuration = null)
    {
        account ??= CreateTestAccount();
        project ??= CreateTestProject(account);
        configuration ??= CreateTestAuditConfiguration(account);
        
        return new AuditRun
        {
            Project = project,
            ProjectId = project.Id,
            Account = account,
            AccountId = account.Id,
            AuditConfiguration = configuration,
            AuditConfigurationId = configuration.Id,
            Status = AuditRunStatus.Completed,
            Type = AuditRunType.Manual,
            StartedAt = DateTime.UtcNow.AddHours(-1),
            CompletedAt = DateTime.UtcNow,
            InitiatedBy = "<EMAIL>",
            UsersAnalyzed = 25,
            PermissionsAnalyzed = 150,
            FindingsCount = 8,
            OverallRiskScore = 65
        };
    }

    public static AuditFinding CreateTestAuditFinding(
        AuditRun? auditRun = null,
        AuditRule? auditRule = null,
        User? affectedUser = null)
    {
        auditRun ??= CreateTestAuditRun();
        auditRule ??= CreateTestAuditRule();
        
        return new AuditFinding
        {
            AuditRun = auditRun,
            AuditRunId = auditRun.Id,
            AuditRule = auditRule,
            AuditRuleId = auditRule.Id,
            Severity = FindingSeverity.Medium,
            RiskScore = 60,
            Title = "Test Security Finding",
            Description = "This is a test security finding for unit testing",
            Recommendation = "Review and update the affected permissions",
            AffectedUser = affectedUser,
            AffectedUserId = affectedUser?.Id,
            AccResourceId = "test-resource-123",
            Status = FindingStatus.Open,
            Metadata = "{\"additionalInfo\": \"test data\"}"
        };
    }

    public static UserRole CreateTestUserRole(User? user = null, Role? role = null, Project? project = null)
    {
        user ??= CreateTestUser();
        role ??= CreateTestRole();
        project ??= CreateTestProject();
        
        return new UserRole
        {
            User = user,
            UserId = user.Id,
            Role = role,
            RoleId = role.Id,
            Project = project,
            ProjectId = project.Id,
            AssignedAt = DateTime.UtcNow.AddDays(-14),
            AssignedBy = "<EMAIL>",
            AssignmentReason = "Project team member",
            IsTemporary = false
        };
    }

    public static ProjectCompany CreateTestProjectCompany(Project? project = null, Company? company = null)
    {
        project ??= CreateTestProject();
        company ??= CreateTestCompany();
        
        return new ProjectCompany
        {
            Project = project,
            ProjectId = project.Id,
            Company = company,
            CompanyId = company.Id,
            ProjectRole = "General Contractor",
            JoinedAt = DateTime.UtcNow.AddDays(-30),
            Status = ProjectCompanyStatus.Active
        };
    }
}
