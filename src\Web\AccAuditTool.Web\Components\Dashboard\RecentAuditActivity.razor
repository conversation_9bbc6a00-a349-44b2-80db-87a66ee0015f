@using AccAuditTool.Application.Services

<div class="recent-activity">
    @if (Activities.Any())
    {
        <div class="activity-list">
            @foreach (var activity in Activities.Take(MaxItems))
            {
                <div class="activity-item">
                    <div class="activity-icon">
                        <i class="@GetActivityIcon(activity.Type) text-@GetActivityColor(activity.Type)"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-header">
                            <span class="activity-type">@activity.Type</span>
                            <span class="activity-time">@GetRelativeTime(activity.Timestamp)</span>
                        </div>
                        <p class="activity-description">@activity.Description</p>
                        @if (!string.IsNullOrEmpty(activity.User) && activity.User != "System")
                        {
                            <small class="activity-user text-muted">
                                <i class="fas fa-user"></i>
                                @activity.User
                            </small>
                        }
                    </div>
                </div>
            }
        </div>
        
        @if (Activities.Count > MaxItems)
        {
            <div class="activity-footer">
                <button class="btn btn-link btn-sm" @onclick="ShowAllActivities">
                    <i class="fas fa-ellipsis-h"></i>
                    View all @Activities.Count activities
                </button>
            </div>
        }
    }
    else
    {
        <div class="no-activity-message">
            <i class="fas fa-clock text-muted"></i>
            <p class="text-muted">No recent activity</p>
        </div>
    }
</div>

@code {
    [Parameter] public List<AuditActivity> Activities { get; set; } = new();
    [Parameter] public int MaxItems { get; set; } = 5;
    [Parameter] public EventCallback OnShowAll { get; set; }

    private string GetActivityIcon(string activityType)
    {
        return activityType.ToLowerInvariant() switch
        {
            "audit completed" => "fas fa-check-circle",
            "audit started" => "fas fa-play-circle",
            "audit failed" => "fas fa-times-circle",
            "critical finding" => "fas fa-exclamation-triangle",
            "rule updated" => "fas fa-cog",
            "rule created" => "fas fa-plus-circle",
            "rule deleted" => "fas fa-trash",
            "compliance check" => "fas fa-clipboard-check",
            "user login" => "fas fa-sign-in-alt",
            "user logout" => "fas fa-sign-out-alt",
            "configuration changed" => "fas fa-wrench",
            "report generated" => "fas fa-file-alt",
            "export completed" => "fas fa-download",
            "notification sent" => "fas fa-bell",
            _ => "fas fa-info-circle"
        };
    }

    private string GetActivityColor(string activityType)
    {
        return activityType.ToLowerInvariant() switch
        {
            "audit completed" => "success",
            "audit started" => "primary",
            "audit failed" => "danger",
            "critical finding" => "danger",
            "rule updated" => "warning",
            "rule created" => "success",
            "rule deleted" => "danger",
            "compliance check" => "info",
            "user login" => "success",
            "user logout" => "secondary",
            "configuration changed" => "warning",
            "report generated" => "info",
            "export completed" => "success",
            "notification sent" => "primary",
            _ => "secondary"
        };
    }

    private string GetRelativeTime(DateTime timestamp)
    {
        var timeSpan = DateTime.UtcNow - timestamp;
        
        return timeSpan switch
        {
            { TotalMinutes: < 1 } => "Just now",
            { TotalMinutes: < 60 } => $"{(int)timeSpan.TotalMinutes}m ago",
            { TotalHours: < 24 } => $"{(int)timeSpan.TotalHours}h ago",
            { TotalDays: < 7 } => $"{(int)timeSpan.TotalDays}d ago",
            { TotalDays: < 30 } => $"{(int)(timeSpan.TotalDays / 7)}w ago",
            _ => timestamp.ToString("MMM dd, yyyy")
        };
    }

    private async Task ShowAllActivities()
    {
        if (OnShowAll.HasDelegate)
        {
            await OnShowAll.InvokeAsync();
        }
    }
}

<style>
    .recent-activity {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .activity-list {
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    }

    .activity-item {
        display: flex;
        gap: 12px;
        padding: 12px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .activity-icon i {
        font-size: 14px;
    }

    .activity-content {
        flex: 1;
        min-width: 0;
    }

    .activity-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 4px;
        gap: 8px;
    }

    .activity-type {
        font-weight: 600;
        font-size: 0.875rem;
        color: #495057;
    }

    .activity-time {
        font-size: 0.75rem;
        color: #6c757d;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .activity-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin: 0 0 4px 0;
        line-height: 1.4;
        word-wrap: break-word;
    }

    .activity-user {
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .activity-user i {
        font-size: 0.7rem;
    }

    .activity-footer {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        text-align: center;
    }

    .activity-footer .btn-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .activity-footer .btn-link:hover {
        color: #495057;
        text-decoration: underline;
    }

    .no-activity-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        gap: 10px;
    }

    .no-activity-message i {
        font-size: 2rem;
    }

    .no-activity-message p {
        margin: 0;
        font-size: 0.875rem;
    }

    /* Custom scrollbar for activity list */
    .activity-list::-webkit-scrollbar {
        width: 4px;
    }

    .activity-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .activity-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .activity-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
