using AccAuditTool.Application.Interfaces;
using AccAuditTool.Application.Services;
using AccAuditTool.Domain.Interfaces;
using AccAuditTool.Infrastructure;
using AccAuditTool.Infrastructure.Data;
using AccAuditTool.Infrastructure.Repositories;
using Microsoft.EntityFrameworkCore;
using Serilog;
using Blazorise;
using Blazorise.Bootstrap5;
using Blazorise.Icons.FontAwesome;

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/accaudit-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

var builder = WebApplication.CreateBuilder(args);

// Add Serilog
builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();

// Add infrastructure services (includes database, repositories, and all Phase 2 services)
builder.Services.AddInfrastructure(builder.Configuration);

// Add authentication and authorization
builder.Services.AddAuthentication()
    .AddOpenIdConnect("oidc", options =>
    {
        // Configure OAuth 2.0 for ACC integration
        options.Authority = builder.Configuration["Authentication:Authority"];
        options.ClientId = builder.Configuration["Authentication:ClientId"];
        options.ClientSecret = builder.Configuration["Authentication:ClientSecret"];
        options.ResponseType = "code";
        options.SaveTokens = true;
        options.GetClaimsFromUserInfoEndpoint = true;

        // Add required scopes for ACC API access
        options.Scope.Add("data:read");
        options.Scope.Add("account:read");
    });

builder.Services.AddAuthorization();

// Add Blazorise UI components
builder.Services
    .AddBlazorise(options =>
    {
        options.Immediate = true;
    })
    .AddBootstrap5Providers()
    .AddFontAwesomeIcons();

var app = builder.Build();

// Configure the HTTP request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();

app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapRazorPages();

// Add health check endpoints
app.MapHealthChecks("/health");
app.MapHealthChecks("/health/ready");
app.MapHealthChecks("/health/live");

// Ensure database is created
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<AccAuditDbContext>();
    context.Database.EnsureCreated();
}

app.Run();
