using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Domain.Interfaces;

/// <summary>
/// Interface for ACC data extraction service
/// </summary>
public interface IAccDataService
{
    /// <summary>
    /// Get all accessible accounts
    /// </summary>
    Task<IEnumerable<AccAccountDto>> GetAccountsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get projects for an account
    /// </summary>
    Task<IEnumerable<AccProjectDto>> GetProjectsAsync(string accountId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users for a project
    /// </summary>
    Task<IEnumerable<AccUserDto>> GetProjectUsersAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user details
    /// </summary>
    Task<AccUserDto?> GetUserDetailsAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folder permissions for a project
    /// </summary>
    Task<IEnumerable<AccPermissionDto>> GetFolderPermissionsAsync(
        string projectId,
        string folderId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all folders in a project
    /// </summary>
    Task<IEnumerable<AccResourceDto>> GetProjectFoldersAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get companies for a project
    /// </summary>
    Task<IEnumerable<AccCompanyDto>> GetProjectCompaniesAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get roles for a project
    /// </summary>
    Task<IEnumerable<AccRoleDto>> GetProjectRolesAsync(string projectId, CancellationToken cancellationToken = default);
}

/// <summary>
/// DTO for ACC account data
/// </summary>
public class AccAccountDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Region { get; set; }
    public string? SubscriptionType { get; set; }
}

/// <summary>
/// DTO for ACC project data
/// </summary>
public class AccProjectDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AccountId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? ProjectType { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// DTO for ACC user data
/// </summary>
public class AccUserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? CompanyId { get; set; }
    public string? CompanyName { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LastSeenAt { get; set; }
    public List<AccUserRoleDto> Roles { get; set; } = new();
}

/// <summary>
/// DTO for ACC user role data
/// </summary>
public class AccUserRoleDto
{
    public string RoleId { get; set; } = string.Empty;
    public string RoleName { get; set; } = string.Empty;
    public string? ServiceKey { get; set; }
    public DateTime AssignedAt { get; set; }
}

/// <summary>
/// DTO for ACC permission data
/// </summary>
public class AccPermissionDto
{
    public string SubjectType { get; set; } = string.Empty; // USER, ROLE, COMPANY
    public string SubjectId { get; set; } = string.Empty;
    public string ResourceId { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public string? InheritedFrom { get; set; }
}

/// <summary>
/// DTO for ACC resource data
/// </summary>
public class AccResourceDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? ParentId { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string? LastModifiedBy { get; set; }
}

/// <summary>
/// DTO for ACC company data
/// </summary>
public class AccCompanyDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Trade { get; set; }
    public string? Address { get; set; }
    public string? Phone { get; set; }
}

/// <summary>
/// DTO for ACC role data
/// </summary>
public class AccRoleDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ServiceKey { get; set; }
    public List<string> Permissions { get; set; } = new();
}
