{"format": 1, "restore": {"C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Infrastructure\\AccAuditTool.Infrastructure\\AccAuditTool.Infrastructure.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\AccAuditTool.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\AccAuditTool.Domain.csproj", "projectName": "AccAuditTool.Domain", "projectPath": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\AccAuditTool.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Infrastructure\\AccAuditTool.Infrastructure\\AccAuditTool.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Infrastructure\\AccAuditTool.Infrastructure\\AccAuditTool.Infrastructure.csproj", "projectName": "AccAuditTool.Infrastructure", "projectPath": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Infrastructure\\AccAuditTool.Infrastructure\\AccAuditTool.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Infrastructure\\AccAuditTool.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\AccAuditTool.Domain.csproj": {"projectPath": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Core\\AccAuditTool.Domain\\AccAuditTool.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[8.0.1, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Http.Polly": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[8.0.0, )"}, "Polly": {"target": "Package", "version": "[8.2.0, )"}, "Polly.Extensions.Http": {"target": "Package", "version": "[3.0.0, )"}, "Serilog": {"target": "Package", "version": "[3.1.1, )"}, "Serilog.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "System.IdentityModel.Tokens.Jwt": {"target": "Package", "version": "[8.0.2, )"}, "System.Text.Json": {"target": "Package", "version": "[8.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}}}