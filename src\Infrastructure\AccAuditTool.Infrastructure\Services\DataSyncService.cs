using AccAuditTool.Application.Interfaces;
using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Implementation of data synchronization service with incremental updates and conflict resolution
/// </summary>
public class DataSyncService : IDataSyncService
{
    private readonly IAccDataService _accDataService;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DataSyncService> _logger;

    public DataSyncService(
        IAccDataService accDataService,
        IUnitOfWork unitOfWork,
        ILogger<DataSyncService> logger)
    {
        _accDataService = accDataService;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<DataSyncResult> SyncAccountDataAsync(
        string accAccountId,
        bool fullSync = false,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting {SyncType} sync for account {AccountId}",
            fullSync ? "full" : "incremental", accAccountId);

        var result = new DataSyncResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            // 1. Sync account information
            await SyncAccountAsync(accAccountId, result, cancellationToken);

            // 2. Sync projects for the account
            await SyncProjectsAsync(accAccountId, result, fullSync, cancellationToken);

            // 3. For each project, sync detailed data
            var account = await _unitOfWork.Accounts.FirstOrDefaultAsync(
                a => a.AccAccountId == accAccountId, cancellationToken);

            if (account != null)
            {
                var projects = await _unitOfWork.Projects.FindAsync(
                    p => p.AccountId == account.Id, cancellationToken);

                foreach (var project in projects)
                {
                    await SyncProjectDataAsync(project.AccProjectId, result, fullSync, cancellationToken);
                }
            }

            await _unitOfWork.CommitTransactionAsync(cancellationToken);
            result.Success = true;
            result.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Successfully completed sync for account {AccountId} in {Duration}ms",
                accAccountId, result.Duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            result.Errors.Add($"Account sync failed: {ex.Message}");

            _logger.LogError(ex, "Error during account sync for {AccountId}", accAccountId);
        }

        return result;
    }

    public async Task<DataSyncResult> SyncProjectDataAsync(
        string accProjectId,
        bool fullSync = false,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting {SyncType} sync for project {ProjectId}",
            fullSync ? "full" : "incremental", accProjectId);

        var result = new DataSyncResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            await SyncProjectDataAsync(accProjectId, result, fullSync, cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);
            result.Success = true;
            result.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Successfully completed project sync for {ProjectId} in {Duration}ms",
                accProjectId, result.Duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            result.Errors.Add($"Project sync failed: {ex.Message}");

            _logger.LogError(ex, "Error during project sync for {ProjectId}", accProjectId);
        }

        return result;
    }

    public async Task<DataSyncResult> SyncUserDataAsync(
        string? accUserId = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting user data sync for user {UserId}", accUserId ?? "all users");

        var result = new DataSyncResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            if (!string.IsNullOrEmpty(accUserId))
            {
                // Sync specific user
                await SyncSpecificUserAsync(accUserId, result, cancellationToken);
            }
            else
            {
                // Sync all users across all projects
                var projects = await _unitOfWork.Projects.GetAllAsync(cancellationToken);
                foreach (var project in projects)
                {
                    await SyncProjectUsersAsync(project.AccProjectId, result, cancellationToken);
                }
            }

            await _unitOfWork.CommitTransactionAsync(cancellationToken);
            result.Success = true;
            result.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Successfully completed user sync in {Duration}ms",
                result.Duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            result.Errors.Add($"User sync failed: {ex.Message}");

            _logger.LogError(ex, "Error during user sync");
        }

        return result;
    }

    public async Task<DataSyncResult> SyncPermissionDataAsync(
        string accProjectId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting permission sync for project {ProjectId}", accProjectId);

        var result = new DataSyncResult
        {
            StartedAt = DateTime.UtcNow
        };

        try
        {
            await _unitOfWork.BeginTransactionAsync(cancellationToken);

            await SyncProjectPermissionsAsync(accProjectId, result, cancellationToken);

            await _unitOfWork.CommitTransactionAsync(cancellationToken);
            result.Success = true;
            result.CompletedAt = DateTime.UtcNow;

            _logger.LogInformation("Successfully completed permission sync for {ProjectId} in {Duration}ms",
                accProjectId, result.Duration.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            await _unitOfWork.RollbackTransactionAsync(cancellationToken);
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            result.Errors.Add($"Permission sync failed: {ex.Message}");

            _logger.LogError(ex, "Error during permission sync for {ProjectId}", accProjectId);
        }

        return result;
    }

    public async Task<DataSyncStatus?> GetLastSyncStatusAsync(
        string accAccountId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // This would typically be stored in a separate sync status table
            // For now, we'll return a placeholder implementation
            var account = await _unitOfWork.Accounts.FirstOrDefaultAsync(
                a => a.AccAccountId == accAccountId, cancellationToken);

            if (account == null)
                return null;

            return new DataSyncStatus
            {
                AccAccountId = accAccountId,
                LastFullSync = account.UpdatedAt.AddDays(-1), // Placeholder
                LastIncrementalSync = account.UpdatedAt,
                IsScheduled = false,
                SyncInterval = TimeSpan.FromHours(24),
                NextScheduledSync = DateTime.UtcNow.AddHours(24)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting sync status for account {AccountId}", accAccountId);
            return null;
        }
    }

    public async Task<bool> ScheduleDataSyncAsync(
        string accAccountId,
        TimeSpan interval,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Scheduling data sync for account {AccountId} with interval {Interval}",
            accAccountId, interval);

        try
        {
            // This would typically integrate with a job scheduler like Hangfire or Quartz
            // For now, this is a placeholder implementation
            _logger.LogInformation("Data sync scheduled for account {AccountId}", accAccountId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling data sync for account {AccountId}", accAccountId);
            return false;
        }
    }

    // Placeholder implementations for remaining sync methods
    private async Task SyncSpecificUserAsync(string accUserId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            var userDetails = await _accDataService.GetUserDetailsAsync(accUserId, cancellationToken);
            if (userDetails != null)
            {
                // Implementation would sync specific user data
                result.UsersProcessed++;
                _logger.LogDebug("Synced user: {UserEmail}", userDetails.Email);
            }
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing user {accUserId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing user {UserId}", accUserId);
        }
    }

    private async Task SyncProjectUsersAsync(string accProjectId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            var accUsers = await _accDataService.GetProjectUsersAsync(accProjectId, cancellationToken);
            // Implementation would sync all users for the project
            result.UsersProcessed += accUsers.Count();
            _logger.LogDebug("Synced {Count} users for project {ProjectId}", accUsers.Count(), accProjectId);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing users for project {accProjectId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing users for project {ProjectId}", accProjectId);
        }
    }

    private async Task SyncProjectRolesAsync(string accProjectId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            var accRoles = await _accDataService.GetProjectRolesAsync(accProjectId, cancellationToken);
            // Implementation would sync all roles for the project
            result.RolesProcessed += accRoles.Count();
            _logger.LogDebug("Synced {Count} roles for project {ProjectId}", accRoles.Count(), accProjectId);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing roles for project {accProjectId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing roles for project {ProjectId}", accProjectId);
        }
    }

    private async Task SyncProjectResourcesAsync(string accProjectId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            var accResources = await _accDataService.GetProjectFoldersAsync(accProjectId, cancellationToken);
            // Implementation would sync all resources for the project
            result.ResourcesProcessed += accResources.Count();
            _logger.LogDebug("Synced {Count} resources for project {ProjectId}", accResources.Count(), accProjectId);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing resources for project {accProjectId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing resources for project {ProjectId}", accProjectId);
        }
    }

    private async Task SyncProjectPermissionsAsync(string accProjectId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            // Get all folders for the project first
            var accResources = await _accDataService.GetProjectFoldersAsync(accProjectId, cancellationToken);

            foreach (var resource in accResources)
            {
                var permissions = await _accDataService.GetFolderPermissionsAsync(accProjectId, resource.Id, cancellationToken);
                result.PermissionsProcessed += permissions.Count();
            }

            _logger.LogDebug("Synced permissions for project {ProjectId}", accProjectId);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing permissions for project {accProjectId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing permissions for project {ProjectId}", accProjectId);
        }
    }

    private async Task SyncProjectCompaniesAsync(string accProjectId, DataSyncResult result, CancellationToken cancellationToken)
    {
        try
        {
            var accCompanies = await _accDataService.GetProjectCompaniesAsync(accProjectId, cancellationToken);
            // Implementation would sync all companies for the project
            result.CompaniesProcessed += accCompanies.Count();
            _logger.LogDebug("Synced {Count} companies for project {ProjectId}", accCompanies.Count(), accProjectId);
        }
        catch (Exception ex)
        {
            result.Errors.Add($"Error syncing companies for project {accProjectId}: {ex.Message}");
            _logger.LogError(ex, "Error syncing companies for project {ProjectId}", accProjectId);
        }
    }
}