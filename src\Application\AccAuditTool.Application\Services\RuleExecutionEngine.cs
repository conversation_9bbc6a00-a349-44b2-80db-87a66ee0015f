using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for executing audit rules with parallel processing and dependency management
/// </summary>
public interface IRuleExecutionEngine
{
    /// <summary>
    /// Execute all enabled audit rules for an audit run
    /// </summary>
    Task<RuleExecutionResult> ExecuteRulesAsync(Guid auditRunId, RuleExecutionOptions? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute specific audit rules
    /// </summary>
    Task<RuleExecutionResult> ExecuteRulesAsync(IEnumerable<string> ruleIds, AuditContext context, RuleExecutionOptions? options = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute a single audit rule
    /// </summary>
    Task<AuditRuleResult> ExecuteRuleAsync(string ruleId, AuditContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rule execution statistics
    /// </summary>
    Task<RuleExecutionStatistics> GetExecutionStatisticsAsync(Guid auditRunId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Cancel rule execution
    /// </summary>
    Task CancelExecutionAsync(Guid auditRunId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of rule execution engine
/// </summary>
public class RuleExecutionEngine : IRuleExecutionEngine
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IRuleRegistry _ruleRegistry;
    private readonly IRiskScoringEngine _riskScoringEngine;
    private readonly ILogger<RuleExecutionEngine> _logger;
    private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _executionCancellationTokens;

    public RuleExecutionEngine(
        IUnitOfWork unitOfWork,
        IRuleRegistry ruleRegistry,
        IRiskScoringEngine riskScoringEngine,
        ILogger<RuleExecutionEngine> logger)
    {
        _unitOfWork = unitOfWork;
        _ruleRegistry = ruleRegistry;
        _riskScoringEngine = riskScoringEngine;
        _logger = logger;
        _executionCancellationTokens = new ConcurrentDictionary<Guid, CancellationTokenSource>();
    }

    public async Task<RuleExecutionResult> ExecuteRulesAsync(Guid auditRunId, RuleExecutionOptions? options = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting rule execution for audit run {AuditRunId}", auditRunId);

        options ??= new RuleExecutionOptions();
        var executionCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        _executionCancellationTokens[auditRunId] = executionCts;

        var result = new RuleExecutionResult
        {
            AuditRunId = auditRunId,
            StartedAt = DateTime.UtcNow,
            Options = options
        };

        try
        {
            // Get audit run details
            var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
            if (auditRun == null)
            {
                throw new ArgumentException($"Audit run {auditRunId} not found");
            }

            // Update audit run status
            auditRun.Status = AuditRunStatus.Running;
            await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Create audit context
            var context = new AuditContext
            {
                AuditRunId = auditRunId,
                AccountId = auditRun.AccountId,
                ProjectIds = auditRun.ProjectIds,
                AuditDate = auditRun.StartedAt,
                UnitOfWork = _unitOfWork
            };

            // Get enabled rules
            var enabledRules = await GetEnabledRulesAsync(options, cancellationToken);
            result.TotalRules = enabledRules.Count;

            _logger.LogInformation("Executing {RuleCount} rules for audit run {AuditRunId}", enabledRules.Count, auditRunId);

            // Build rule dependency graph
            var dependencyGraph = BuildRuleDependencyGraph(enabledRules);

            // Execute rules in dependency order
            await ExecuteRulesWithDependenciesAsync(dependencyGraph, context, result, options, executionCts.Token);

            // Calculate risk scores
            if (options.CalculateRiskScores)
            {
                await CalculateRiskScoresAsync(result, context, cancellationToken);
            }

            // Update audit run status
            auditRun.Status = result.Success ? AuditRunStatus.Completed : AuditRunStatus.Failed;
            auditRun.CompletedAt = DateTime.UtcNow;
            auditRun.TotalFindings = result.TotalFindings;
            auditRun.ErrorMessage = result.ErrorMessage;

            await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            result.CompletedAt = DateTime.UtcNow;
            result.ExecutionTime = result.CompletedAt.Value - result.StartedAt;

            _logger.LogInformation("Completed rule execution for audit run {AuditRunId}. Success: {Success}, Findings: {FindingCount}, Duration: {Duration}ms",
                auditRunId, result.Success, result.TotalFindings, result.ExecutionTime?.TotalMilliseconds);
        }
        catch (OperationCanceledException)
        {
            result.Success = false;
            result.ErrorMessage = "Rule execution was cancelled";
            result.CompletedAt = DateTime.UtcNow;
            
            _logger.LogWarning("Rule execution cancelled for audit run {AuditRunId}", auditRunId);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            
            _logger.LogError(ex, "Error executing rules for audit run {AuditRunId}", auditRunId);
        }
        finally
        {
            _executionCancellationTokens.TryRemove(auditRunId, out _);
            executionCts.Dispose();
        }

        return result;
    }

    public async Task<RuleExecutionResult> ExecuteRulesAsync(IEnumerable<string> ruleIds, AuditContext context, RuleExecutionOptions? options = null, CancellationToken cancellationToken = default)
    {
        options ??= new RuleExecutionOptions();
        
        var result = new RuleExecutionResult
        {
            AuditRunId = context.AuditRunId,
            StartedAt = DateTime.UtcNow,
            Options = options
        };

        try
        {
            var rules = new List<IAuditRule>();
            foreach (var ruleId in ruleIds)
            {
                var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
                if (rule != null && rule.IsEnabled)
                {
                    rules.Add(rule);
                }
            }

            result.TotalRules = rules.Count;

            // Build dependency graph for selected rules
            var dependencyGraph = BuildRuleDependencyGraph(rules);

            // Execute rules
            await ExecuteRulesWithDependenciesAsync(dependencyGraph, context, result, options, cancellationToken);

            result.CompletedAt = DateTime.UtcNow;
            result.ExecutionTime = result.CompletedAt.Value - result.StartedAt;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.CompletedAt = DateTime.UtcNow;
            
            _logger.LogError(ex, "Error executing specific rules for audit run {AuditRunId}", context.AuditRunId);
        }

        return result;
    }

    public async Task<AuditRuleResult> ExecuteRuleAsync(string ruleId, AuditContext context, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Executing single rule {RuleId} for audit run {AuditRunId}", ruleId, context.AuditRunId);

        var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
        if (rule == null)
        {
            return new AuditRuleResult
            {
                RuleId = ruleId,
                Success = false,
                ErrorMessage = $"Rule {ruleId} not found"
            };
        }

        if (!rule.IsEnabled)
        {
            return new AuditRuleResult
            {
                RuleId = ruleId,
                Success = false,
                ErrorMessage = $"Rule {ruleId} is disabled"
            };
        }

        try
        {
            var result = await rule.ExecuteAsync(context, cancellationToken);
            
            // Save findings to database
            if (result.Success && result.Findings.Any())
            {
                await SaveFindingsAsync(result.Findings, context, cancellationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing rule {RuleId}", ruleId);
            return new AuditRuleResult
            {
                RuleId = ruleId,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<RuleExecutionStatistics> GetExecutionStatisticsAsync(Guid auditRunId, CancellationToken cancellationToken = default)
    {
        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun == null)
        {
            throw new ArgumentException($"Audit run {auditRunId} not found");
        }

        var findings = await _unitOfWork.AuditFindings.FindAsync(f => f.AuditRunId == auditRunId, cancellationToken);
        
        var statistics = new RuleExecutionStatistics
        {
            AuditRunId = auditRunId,
            Status = auditRun.Status,
            StartedAt = auditRun.StartedAt,
            CompletedAt = auditRun.CompletedAt,
            TotalFindings = findings.Count(),
            FindingsBySeverity = findings.GroupBy(f => f.Severity).ToDictionary(g => g.Key, g => g.Count()),
            FindingsByCategory = findings.GroupBy(f => f.Category).ToDictionary(g => g.Key, g => g.Count()),
            FindingsByRule = findings.GroupBy(f => f.RuleId).ToDictionary(g => g.Key, g => g.Count())
        };

        if (auditRun.CompletedAt.HasValue)
        {
            statistics.ExecutionTime = auditRun.CompletedAt.Value - auditRun.StartedAt;
        }

        return statistics;
    }

    public async Task CancelExecutionAsync(Guid auditRunId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Cancelling rule execution for audit run {AuditRunId}", auditRunId);

        if (_executionCancellationTokens.TryGetValue(auditRunId, out var cts))
        {
            cts.Cancel();
        }

        // Update audit run status
        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun != null && auditRun.Status == AuditRunStatus.Running)
        {
            auditRun.Status = AuditRunStatus.Cancelled;
            auditRun.CompletedAt = DateTime.UtcNow;
            await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
        }
    }

    private async Task<List<IAuditRule>> GetEnabledRulesAsync(RuleExecutionOptions options, CancellationToken cancellationToken)
    {
        var allRules = await _ruleRegistry.GetAllRulesAsync(cancellationToken);
        var enabledRules = allRules.Where(r => r.IsEnabled).ToList();

        // Apply rule filters
        if (options.IncludeRuleIds?.Any() == true)
        {
            enabledRules = enabledRules.Where(r => options.IncludeRuleIds.Contains(r.RuleId)).ToList();
        }

        if (options.ExcludeRuleIds?.Any() == true)
        {
            enabledRules = enabledRules.Where(r => !options.ExcludeRuleIds.Contains(r.RuleId)).ToList();
        }

        if (options.Categories?.Any() == true)
        {
            enabledRules = enabledRules.Where(r => options.Categories.Contains(r.Category)).ToList();
        }

        if (options.MinSeverity.HasValue)
        {
            enabledRules = enabledRules.Where(r => r.Severity >= options.MinSeverity.Value).ToList();
        }

        return enabledRules;
    }

    private RuleDependencyGraph BuildRuleDependencyGraph(List<IAuditRule> rules)
    {
        var graph = new RuleDependencyGraph();

        // Add all rules as nodes
        foreach (var rule in rules)
        {
            graph.AddNode(rule);
        }

        // Add dependencies
        foreach (var rule in rules)
        {
            var dependencies = rule.GetDependencies();
            foreach (var dependency in dependencies)
            {
                var dependentRule = rules.FirstOrDefault(r => r.RuleId == dependency);
                if (dependentRule != null)
                {
                    graph.AddDependency(rule, dependentRule);
                }
            }
        }

        return graph;
    }

    private async Task ExecuteRulesWithDependenciesAsync(
        RuleDependencyGraph dependencyGraph, 
        AuditContext context, 
        RuleExecutionResult result, 
        RuleExecutionOptions options,
        CancellationToken cancellationToken)
    {
        var executedRules = new HashSet<string>();
        var rulesToExecute = dependencyGraph.GetTopologicalOrder();

        // Group rules by dependency level for parallel execution
        var dependencyLevels = GroupRulesByDependencyLevel(rulesToExecute, dependencyGraph);

        foreach (var level in dependencyLevels)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // Execute rules in this level in parallel
            var tasks = level.Select(rule => ExecuteRuleWithResultHandlingAsync(rule, context, result, options, cancellationToken));
            
            if (options.MaxParallelism > 1)
            {
                var semaphore = new SemaphoreSlim(options.MaxParallelism);
                var throttledTasks = tasks.Select(async task =>
                {
                    await semaphore.WaitAsync(cancellationToken);
                    try
                    {
                        return await task;
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                });

                await Task.WhenAll(throttledTasks);
            }
            else
            {
                // Sequential execution
                foreach (var task in tasks)
                {
                    await task;
                }
            }

            // Mark rules as executed
            foreach (var rule in level)
            {
                executedRules.Add(rule.RuleId);
            }

            // Check if we should stop on first failure
            if (options.StopOnFirstFailure && result.FailedRules > 0)
            {
                break;
            }
        }
    }

    private async Task<AuditRuleResult> ExecuteRuleWithResultHandlingAsync(
        IAuditRule rule, 
        AuditContext context, 
        RuleExecutionResult executionResult, 
        RuleExecutionOptions options,
        CancellationToken cancellationToken)
    {
        var ruleResult = await ExecuteRuleAsync(rule.RuleId, context, cancellationToken);
        
        // Update execution result
        lock (executionResult)
        {
            executionResult.RuleResults[rule.RuleId] = ruleResult;
            executionResult.ExecutedRules++;
            
            if (ruleResult.Success)
            {
                executionResult.SuccessfulRules++;
                executionResult.TotalFindings += ruleResult.Findings.Count;
            }
            else
            {
                executionResult.FailedRules++;
                if (string.IsNullOrEmpty(executionResult.ErrorMessage))
                {
                    executionResult.ErrorMessage = ruleResult.ErrorMessage;
                }
            }
        }

        return ruleResult;
    }

    private List<List<IAuditRule>> GroupRulesByDependencyLevel(List<IAuditRule> rules, RuleDependencyGraph dependencyGraph)
    {
        var levels = new List<List<IAuditRule>>();
        var processed = new HashSet<string>();
        var remaining = new Queue<IAuditRule>(rules);

        while (remaining.Any())
        {
            var currentLevel = new List<IAuditRule>();
            var rulesToRemove = new List<IAuditRule>();

            foreach (var rule in remaining)
            {
                var dependencies = rule.GetDependencies();
                if (dependencies.All(dep => processed.Contains(dep)))
                {
                    currentLevel.Add(rule);
                    rulesToRemove.Add(rule);
                    processed.Add(rule.RuleId);
                }
            }

            if (!currentLevel.Any())
            {
                // Circular dependency or unresolvable dependencies
                _logger.LogWarning("Circular dependency detected or unresolvable dependencies in rules");
                currentLevel.AddRange(remaining);
                rulesToRemove.AddRange(remaining);
            }

            levels.Add(currentLevel);
            
            foreach (var rule in rulesToRemove)
            {
                remaining = new Queue<IAuditRule>(remaining.Where(r => r != rule));
            }
        }

        return levels;
    }

    private async Task SaveFindingsAsync(List<AuditFindingData> findingData, AuditContext context, CancellationToken cancellationToken)
    {
        var findings = findingData.Select(fd => new AuditFinding
        {
            AuditRunId = context.AuditRunId,
            RuleId = fd.RuleId,
            Title = fd.Title,
            Description = fd.Description,
            Severity = fd.Severity,
            Category = DetermineFindingCategory(fd.RuleId),
            Evidence = fd.Evidence,
            Recommendation = fd.Recommendation,
            DetectedAt = fd.DetectedAt,
            AffectedEntity = fd.AffectedEntity,
            AffectedEntityId = fd.AffectedEntityId,
            Status = AuditFindingStatus.New
        }).ToList();

        await _unitOfWork.AuditFindings.AddRangeAsync(findings, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }

    private AuditFindingCategory DetermineFindingCategory(string ruleId)
    {
        // Simple category determination based on rule ID prefix
        return ruleId.ToUpperInvariant() switch
        {
            var id when id.StartsWith("SEC-") => AuditFindingCategory.Security,
            var id when id.StartsWith("COMP-") => AuditFindingCategory.Compliance,
            var id when id.StartsWith("PERF-") => AuditFindingCategory.Performance,
            var id when id.StartsWith("DATA-") => AuditFindingCategory.DataIntegrity,
            var id when id.StartsWith("ACCESS-") => AuditFindingCategory.AccessControl,
            _ => AuditFindingCategory.Configuration
        };
    }

    private async Task CalculateRiskScoresAsync(RuleExecutionResult result, AuditContext context, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Calculating risk scores for audit run {AuditRunId}", context.AuditRunId);

            // Get all findings for this audit run
            var findings = await _unitOfWork.AuditFindings.FindAsync(f => f.AuditRunId == context.AuditRunId, cancellationToken);

            // Calculate overall risk score
            var overallRiskScore = await _riskScoringEngine.CalculateOverallRiskScoreAsync(context.AuditRunId, cancellationToken);
            result.OverallRiskScore = overallRiskScore;

            _logger.LogDebug("Calculated overall risk score {Score} ({Level}) for audit run {AuditRunId}", 
                overallRiskScore.Score, overallRiskScore.Level, context.AuditRunId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating risk scores for audit run {AuditRunId}", context.AuditRunId);
        }
    }
}

/// <summary>
/// Options for rule execution
/// </summary>
public class RuleExecutionOptions
{
    /// <summary>
    /// Maximum number of rules to execute in parallel
    /// </summary>
    public int MaxParallelism { get; set; } = Environment.ProcessorCount;

    /// <summary>
    /// Whether to stop execution on first rule failure
    /// </summary>
    public bool StopOnFirstFailure { get; set; } = false;

    /// <summary>
    /// Whether to calculate risk scores after execution
    /// </summary>
    public bool CalculateRiskScores { get; set; } = true;

    /// <summary>
    /// Specific rule IDs to include (null means all enabled rules)
    /// </summary>
    public List<string>? IncludeRuleIds { get; set; }

    /// <summary>
    /// Rule IDs to exclude from execution
    /// </summary>
    public List<string>? ExcludeRuleIds { get; set; }

    /// <summary>
    /// Rule categories to include
    /// </summary>
    public List<AuditRuleCategory>? Categories { get; set; }

    /// <summary>
    /// Minimum severity level to execute
    /// </summary>
    public AuditSeverity? MinSeverity { get; set; }

    /// <summary>
    /// Timeout for individual rule execution
    /// </summary>
    public TimeSpan RuleTimeout { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Timeout for overall execution
    /// </summary>
    public TimeSpan OverallTimeout { get; set; } = TimeSpan.FromHours(2);
}

/// <summary>
/// Result of rule execution
/// </summary>
public class RuleExecutionResult
{
    /// <summary>
    /// Audit run ID
    /// </summary>
    public Guid AuditRunId { get; set; }

    /// <summary>
    /// Whether the execution was successful
    /// </summary>
    public bool Success { get; set; } = true;

    /// <summary>
    /// Error message if execution failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// When execution started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// When execution completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Total execution time
    /// </summary>
    public TimeSpan? ExecutionTime { get; set; }

    /// <summary>
    /// Total number of rules to execute
    /// </summary>
    public int TotalRules { get; set; }

    /// <summary>
    /// Number of rules executed
    /// </summary>
    public int ExecutedRules { get; set; }

    /// <summary>
    /// Number of successful rule executions
    /// </summary>
    public int SuccessfulRules { get; set; }

    /// <summary>
    /// Number of failed rule executions
    /// </summary>
    public int FailedRules { get; set; }

    /// <summary>
    /// Total number of findings generated
    /// </summary>
    public int TotalFindings { get; set; }

    /// <summary>
    /// Results for individual rules
    /// </summary>
    public Dictionary<string, AuditRuleResult> RuleResults { get; set; } = new();

    /// <summary>
    /// Overall risk score calculated from findings
    /// </summary>
    public RiskScore? OverallRiskScore { get; set; }

    /// <summary>
    /// Execution options used
    /// </summary>
    public RuleExecutionOptions Options { get; set; } = new();

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Statistics for rule execution
/// </summary>
public class RuleExecutionStatistics
{
    /// <summary>
    /// Audit run ID
    /// </summary>
    public Guid AuditRunId { get; set; }

    /// <summary>
    /// Current status of the audit run
    /// </summary>
    public AuditRunStatus Status { get; set; }

    /// <summary>
    /// When execution started
    /// </summary>
    public DateTime StartedAt { get; set; }

    /// <summary>
    /// When execution completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Total execution time
    /// </summary>
    public TimeSpan? ExecutionTime { get; set; }

    /// <summary>
    /// Total number of findings
    /// </summary>
    public int TotalFindings { get; set; }

    /// <summary>
    /// Findings by severity level
    /// </summary>
    public Dictionary<AuditSeverity, int> FindingsBySeverity { get; set; } = new();

    /// <summary>
    /// Findings by category
    /// </summary>
    public Dictionary<AuditFindingCategory, int> FindingsByCategory { get; set; } = new();

    /// <summary>
    /// Findings by rule
    /// </summary>
    public Dictionary<string, int> FindingsByRule { get; set; } = new();
}

/// <summary>
/// Rule dependency graph for execution ordering
/// </summary>
public class RuleDependencyGraph
{
    private readonly Dictionary<string, IAuditRule> _nodes = new();
    private readonly Dictionary<string, List<string>> _dependencies = new();

    /// <summary>
    /// Add a rule node to the graph
    /// </summary>
    public void AddNode(IAuditRule rule)
    {
        _nodes[rule.RuleId] = rule;
        if (!_dependencies.ContainsKey(rule.RuleId))
        {
            _dependencies[rule.RuleId] = new List<string>();
        }
    }

    /// <summary>
    /// Add a dependency relationship
    /// </summary>
    public void AddDependency(IAuditRule rule, IAuditRule dependsOn)
    {
        if (!_dependencies.ContainsKey(rule.RuleId))
        {
            _dependencies[rule.RuleId] = new List<string>();
        }

        _dependencies[rule.RuleId].Add(dependsOn.RuleId);
    }

    /// <summary>
    /// Get rules in topological order (dependencies first)
    /// </summary>
    public List<IAuditRule> GetTopologicalOrder()
    {
        var result = new List<IAuditRule>();
        var visited = new HashSet<string>();
        var visiting = new HashSet<string>();

        foreach (var ruleId in _nodes.Keys)
        {
            if (!visited.Contains(ruleId))
            {
                TopologicalSort(ruleId, visited, visiting, result);
            }
        }

        return result;
    }

    private void TopologicalSort(string ruleId, HashSet<string> visited, HashSet<string> visiting, List<IAuditRule> result)
    {
        if (visiting.Contains(ruleId))
        {
            throw new InvalidOperationException($"Circular dependency detected involving rule {ruleId}");
        }

        if (visited.Contains(ruleId))
        {
            return;
        }

        visiting.Add(ruleId);

        foreach (var dependency in _dependencies[ruleId])
        {
            TopologicalSort(dependency, visited, visiting, result);
        }

        visiting.Remove(ruleId);
        visited.Add(ruleId);
        result.Add(_nodes[ruleId]);
    }
}
