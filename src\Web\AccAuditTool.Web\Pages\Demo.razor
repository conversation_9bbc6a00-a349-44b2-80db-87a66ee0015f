@page "/demo"

<PageTitle>ACC Audit Tool - Demo</PageTitle>

<div class="demo-container">
    <div class="demo-sidebar">
        <div class="demo-nav-header">
            <div class="demo-nav-brand">
                <i class="fas fa-shield-alt brand-icon"></i>
                <span class="brand-text">ACC Audit Tool</span>
            </div>
        </div>
        
        <nav class="demo-nav-content">
            <ul class="demo-nav-list">
                <li class="demo-nav-item">
                    <a class="demo-nav-link active" href="#dashboard">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
                <li class="demo-nav-item">
                    <a class="demo-nav-link" href="#audits">
                        <i class="nav-icon fas fa-clipboard-list"></i>
                        <span class="nav-text">Audits</span>
                        <span class="nav-badge">24</span>
                    </a>
                </li>
                <li class="demo-nav-item">
                    <a class="demo-nav-link" href="#rules">
                        <i class="nav-icon fas fa-cogs"></i>
                        <span class="nav-text">Rules</span>
                        <span class="nav-badge">156</span>
                    </a>
                </li>
                <li class="demo-nav-item">
                    <a class="demo-nav-link" href="#reports">
                        <i class="nav-icon fas fa-chart-bar"></i>
                        <span class="nav-text">Reports</span>
                    </a>
                </li>
                <li class="demo-nav-item">
                    <a class="demo-nav-link" href="#analytics">
                        <i class="nav-icon fas fa-chart-line"></i>
                        <span class="nav-text">Analytics</span>
                    </a>
                </li>
            </ul>
        </nav>
    </div>

    <div class="demo-main-content">
        <div class="demo-top-bar">
            <div class="top-bar-left">
                <h1 class="demo-page-title">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </h1>
            </div>
            <div class="top-bar-right">
                <div class="top-bar-actions">
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        New Audit
                    </button>
                </div>
            </div>
        </div>

        <div class="demo-dashboard-content">
            <!-- Key Metrics -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="demo-metric-card">
                        <div class="metric-icon bg-primary">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">24</h3>
                            <p class="metric-label">Total Audits</p>
                            <div class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +12%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="demo-metric-card">
                        <div class="metric-icon bg-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">8</h3>
                            <p class="metric-label">Critical Findings</p>
                            <div class="metric-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -3%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="demo-metric-card">
                        <div class="metric-icon bg-success">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">92%</h3>
                            <p class="metric-label">Compliance Score</p>
                            <div class="metric-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-3">
                    <div class="demo-metric-card">
                        <div class="metric-icon bg-info">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="metric-content">
                            <h3 class="metric-value">156</h3>
                            <p class="metric-label">Active Users</p>
                            <div class="metric-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                0%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row mb-4">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-area"></i>
                                Audit Activity (Last 30 Days)
                            </h5>
                            <div class="card-actions">
                                <div class="btn-group btn-group-sm" role="group">
                                    <button class="btn btn-primary">7D</button>
                                    <button class="btn btn-outline-primary">30D</button>
                                    <button class="btn btn-outline-primary">90D</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <canvas id="demoAuditChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                Findings by Severity
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="demoSeverityChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Tables -->
            <div class="row">
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-clock"></i>
                                Recent Audits
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="demo-audit-list">
                                <div class="audit-item">
                                    <div class="audit-status">
                                        <span class="badge bg-success">Completed</span>
                                    </div>
                                    <div class="audit-details">
                                        <h6>Monthly Security Audit</h6>
                                        <p class="text-muted">Project Alpha - 2 hours ago</p>
                                    </div>
                                    <div class="audit-findings">
                                        <span class="findings-count">3 findings</span>
                                    </div>
                                </div>
                                <div class="audit-item">
                                    <div class="audit-status">
                                        <span class="badge bg-primary">Running</span>
                                    </div>
                                    <div class="audit-details">
                                        <h6>Compliance Check</h6>
                                        <p class="text-muted">Project Beta - Started 30 min ago</p>
                                    </div>
                                    <div class="audit-findings">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 65%"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="audit-item">
                                    <div class="audit-status">
                                        <span class="badge bg-danger">Failed</span>
                                    </div>
                                    <div class="audit-details">
                                        <h6>Access Control Review</h6>
                                        <p class="text-muted">Project Gamma - 1 day ago</p>
                                    </div>
                                    <div class="audit-findings">
                                        <span class="text-danger">Error</span>
                                    </div>
                                </div>
                                <div class="audit-item">
                                    <div class="audit-status">
                                        <span class="badge bg-warning">Pending</span>
                                    </div>
                                    <div class="audit-details">
                                        <h6>User Permission Audit</h6>
                                        <p class="text-muted">Project Delta - Scheduled for tomorrow</p>
                                    </div>
                                    <div class="audit-findings">
                                        <span class="text-muted">Scheduled</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                Critical Findings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="demo-findings-list">
                                <div class="finding-item">
                                    <div class="finding-severity">
                                        <span class="badge bg-danger">Critical</span>
                                    </div>
                                    <div class="finding-details">
                                        <h6>Excessive Admin Permissions</h6>
                                        <p class="text-muted">User <EMAIL> has admin access to 15 projects</p>
                                    </div>
                                    <div class="finding-actions">
                                        <button class="btn btn-sm btn-outline-primary">Review</button>
                                    </div>
                                </div>
                                <div class="finding-item">
                                    <div class="finding-severity">
                                        <span class="badge bg-danger">Critical</span>
                                    </div>
                                    <div class="finding-details">
                                        <h6>Inactive User with Active Permissions</h6>
                                        <p class="text-muted">User <EMAIL> inactive for 90+ days</p>
                                    </div>
                                    <div class="finding-actions">
                                        <button class="btn btn-sm btn-outline-primary">Review</button>
                                    </div>
                                </div>
                                <div class="finding-item">
                                    <div class="finding-severity">
                                        <span class="badge bg-warning">High</span>
                                    </div>
                                    <div class="finding-details">
                                        <h6>Missing Role Segregation</h6>
                                        <p class="text-muted">Project Delta lacks proper role separation</p>
                                    </div>
                                    <div class="finding-actions">
                                        <button class="btn btn-sm btn-outline-primary">Review</button>
                                    </div>
                                </div>
                                <div class="finding-item">
                                    <div class="finding-severity">
                                        <span class="badge bg-warning">High</span>
                                    </div>
                                    <div class="finding-details">
                                        <h6>Shared Account Usage</h6>
                                        <p class="text-muted">Multiple users sharing service account credentials</p>
                                    </div>
                                    <div class="finding-actions">
                                        <button class="btn btn-sm btn-outline-primary">Review</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Audit Activity Chart
        const auditCtx = document.getElementById('demoAuditChart');
        if (auditCtx) {
            new Chart(auditCtx.getContext('2d'), {
                type: 'line',
                data: {
                    labels: ['Jan 1', 'Jan 8', 'Jan 15', 'Jan 22', 'Jan 29', 'Feb 5', 'Feb 12'],
                    datasets: [{
                        label: 'Audits',
                        data: [2, 4, 3, 5, 2, 6, 4],
                        borderColor: 'rgb(13, 110, 253)',
                        backgroundColor: 'rgba(13, 110, 253, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false }
                    },
                    scales: {
                        y: { beginAtZero: true }
                    }
                }
            });
        }

        // Initialize Severity Chart
        const severityCtx = document.getElementById('demoSeverityChart');
        if (severityCtx) {
            new Chart(severityCtx.getContext('2d'), {
                type: 'doughnut',
                data: {
                    labels: ['Critical', 'High', 'Medium', 'Low'],
                    datasets: [{
                        data: [8, 15, 23, 12],
                        backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#20c997'],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }
    });
</script>
