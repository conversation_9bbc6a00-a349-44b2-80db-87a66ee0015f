namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a user's role assignment in a project
/// </summary>
public class UserRole : BaseEntity
{
    /// <summary>
    /// User assigned to the role
    /// </summary>
    public Guid UserId { get; set; }
    public User User { get; set; } = null!;

    /// <summary>
    /// Role assigned to the user
    /// </summary>
    public Guid RoleId { get; set; }
    public Role Role { get; set; } = null!;

    /// <summary>
    /// Project where this role assignment applies
    /// </summary>
    public Guid ProjectId { get; set; }
    public Project Project { get; set; } = null!;

    /// <summary>
    /// Date when the role was assigned
    /// </summary>
    public DateTime AssignedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date when the role assignment expires (if applicable)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// User who assigned this role
    /// </summary>
    public string? AssignedBy { get; set; }

    /// <summary>
    /// Reason for role assignment
    /// </summary>
    public string? AssignmentReason { get; set; }

    /// <summary>
    /// Whether this is a temporary assignment
    /// </summary>
    public bool IsTemporary { get; set; } = false;
}
