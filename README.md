# ACC User Management Audit Tool

A comprehensive .NET 8.0 application for auditing user management, role assignments, and permission allocations across Autodesk Construction Cloud (ACC) projects and workspaces.

## 🏗️ Architecture Overview

This solution follows a clean architecture pattern with clear separation of concerns:

```
├── src/
│   ├── Core/
│   │   └── AccAuditTool.Domain/          # Domain entities, interfaces, and business logic
│   ├── Application/
│   │   └── AccAuditTool.Application/     # Application services and use cases
│   ├── Infrastructure/
│   │   └── AccAuditTool.Infrastructure/  # Data access, external APIs, and infrastructure
│   └── Web/
│       └── AccAuditTool.Web/             # Blazor Server web application
├── tests/
│   ├── AccAuditTool.Domain.Tests/        # Domain layer unit tests
│   ├── AccAuditTool.Application.Tests/   # Application layer unit tests
│   └── AccAuditTool.Infrastructure.Tests/ # Infrastructure integration tests
├── docker-compose.yml                    # Multi-container Docker setup
├── Dockerfile                           # Application containerization
└── .github/workflows/ci-cd.yml          # CI/CD pipeline configuration
```

## 🚀 Features Implemented

### Phase 1 Foundation (✅ Complete)

- **✅ .NET 8.0 Solution Structure** - Clean architecture with proper separation of concerns
- **✅ Domain Models** - Complete entity framework with 15+ core entities
- **✅ Entity Framework Core 8.0** - Database context with comprehensive configurations
- **✅ Repository Pattern** - Generic repository with Unit of Work implementation
- **✅ Application Services** - Service interfaces for audit, reporting, and configuration
- **✅ Blazor Server Web App** - Modern web interface with authentication scaffolding
- **✅ Logging with Serilog** - Structured logging to console and files
- **✅ Docker Support** - Multi-container setup with SQL Server and Redis
- **✅ CI/CD Pipeline** - GitHub Actions workflow for build, test, and deployment
- **✅ Comprehensive Testing** - Unit tests with xUnit, Moq, FluentAssertions, and Testcontainers

### Core Domain Entities

- **Users & Companies** - User management with company relationships
- **Projects & Accounts** - ACC project and account hierarchy
- **Roles & Permissions** - RBAC implementation with granular permissions
- **Resources** - File and folder resource management
- **Audit Framework** - Audit runs, findings, rules, and configurations
- **Relationships** - User roles, project companies, and permission assignments

### Application Services

- **IAuditService** - Execute audits, manage findings, and track audit runs
- **IDataSyncService** - Synchronize data from ACC APIs
- **IReportService** - Generate reports in multiple formats (PDF, Excel, JSON)
- **IConfigurationService** - Manage audit configurations and rules

## 🛠️ Technology Stack

- **.NET 8.0** - Latest LTS framework
- **ASP.NET Core 8.0** - Web framework with Blazor Server
- **Entity Framework Core 8.0** - ORM with SQL Server
- **Serilog** - Structured logging
- **xUnit** - Unit testing framework
- **Moq** - Mocking framework
- **FluentAssertions** - Fluent test assertions
- **Testcontainers** - Integration testing with Docker
- **AutoFixture** - Test data generation
- **Blazorise** - UI component library
- **Docker** - Containerization
- **GitHub Actions** - CI/CD pipeline

## 🔧 Getting Started

### Prerequisites

- .NET 8.0 SDK
- SQL Server (LocalDB or full instance)
- Docker (optional, for containerized development)
- Visual Studio 2022 or VS Code

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ACC-User-Management
   ```

2. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your Autodesk APS credentials
   ```

3. **Update connection strings**
   - Edit `src/Web/AccAuditTool.Web/appsettings.json`
   - Replace `YOUR_CLIENT_ID` and `YOUR_CLIENT_SECRET` with your Autodesk APS credentials

4. **Build the solution**
   ```bash
   dotnet build
   ```

5. **Run tests**
   ```bash
   dotnet test
   ```

6. **Start the application**
   ```bash
   cd src/Web/AccAuditTool.Web
   dotnet run
   ```

### Docker Development

1. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

2. **Access the application**
   - Web App: http://localhost:8080
   - SQL Server: localhost:1433
   - Redis: localhost:6379

## 📋 What You Need to Provide

To continue development, you'll need to provide:

### 1. Autodesk APS Credentials
- **Client ID** - From your Autodesk APS application
- **Client Secret** - From your Autodesk APS application
- **Callback URL** - Configure in your APS app settings

### 2. Database Configuration
- **Connection String** - SQL Server connection details
- **Environment** - Development, Staging, or Production settings

### 3. Business Requirements
- **Specific Audit Rules** - Custom security rules for your organization
- **Compliance Frameworks** - ISO 27001, SOC 2, or other standards
- **Report Templates** - Custom report formats and layouts
- **User Interface** - Specific dashboard requirements

### 4. Integration Requirements
- **ACC Environment Access** - Test and production ACC accounts
- **Email Configuration** - SMTP settings for notifications
- **External Systems** - Any third-party integrations needed

### 5. Deployment Configuration
- **Cloud Provider** - Azure, AWS, or on-premises
- **Infrastructure** - Kubernetes, App Service, or VM deployment
- **Security** - SSL certificates, firewall rules, and access policies

## 🧪 Testing

The solution includes comprehensive testing:

- **Unit Tests** - 31 tests passing (Domain and Application layers)
- **Integration Tests** - Database and API integration tests (requires Docker)
- **Test Coverage** - Configured for code coverage reporting
- **Test Data** - Helper classes for generating test data

Run tests with:
```bash
# All tests
dotnet test

# Specific project
dotnet test tests/AccAuditTool.Domain.Tests

# With coverage
dotnet test --collect:"XPlat Code Coverage"
```

## 📊 Next Steps

### Phase 2: Data Collection Engine (Weeks 5-8)
- Implement APS API client with rate limiting
- Build permission data extraction services
- Add data normalization and storage
- Implement error handling and retry mechanisms

### Phase 3: Audit Rule Engine (Weeks 9-12)
- Create audit rule implementations
- Build risk scoring algorithms
- Add custom rule creation capabilities
- Implement rule validation framework

### Phase 4: Reporting and Dashboard (Weeks 13-16)
- Complete Blazor Server dashboard
- Add interactive charts and visualizations
- Implement multi-format report generation
- Add mobile-responsive design

### Phase 5: Advanced Features (Weeks 17-20)
- Scheduled audit execution
- Advanced analytics and trend analysis
- Third-party integration API
- Multi-tenant support

### Phase 6: Production Readiness (Weeks 21-24)
- Security penetration testing
- Performance optimization
- Production deployment
- Customer onboarding processes

## 📝 Documentation

- **ProjectScope.md** - Comprehensive project requirements and analysis
- **API Documentation** - Generated from code comments
- **Database Schema** - Entity relationship diagrams
- **Deployment Guide** - Step-by-step deployment instructions

## 🤝 Contributing

1. Follow the established architecture patterns
2. Write comprehensive tests for new features
3. Update documentation for any changes
4. Follow .NET coding standards and conventions
5. Use the provided CI/CD pipeline for validation

## 📄 License

This project is proprietary software for ACC audit and compliance management.

---

**Ready to continue development!** The foundation is solid and ready for the next phase of implementation.
