using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Rules.Security;

/// <summary>
/// Audit rule that identifies users with excessive permissions across projects
/// </summary>
public class ExcessivePermissionsRule : BaseAuditRule
{
    private readonly ILogger<ExcessivePermissionsRule> _logger;

    public ExcessivePermissionsRule(ILogger<ExcessivePermissionsRule> logger) 
        : base(
            "SEC-001", 
            "Excessive Permissions", 
            "Identifies users who have been granted excessive permissions that may violate the principle of least privilege",
            AuditRuleCategory.Security, 
            AuditSeverity.High)
    {
        _logger = logger;
        
        // Default parameters
        Parameters["MaxProjectsPerUser"] = 10;
        Parameters["MaxRolesPerUser"] = 3;
        Parameters["MaxPermissionsPerRole"] = 20;
        Parameters["ExcludeAdminUsers"] = true;
    }

    public override async Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default)
    {
        var result = new AuditRuleResult
        {
            RuleId = RuleId,
            Success = true
        };

        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);

            var maxProjectsPerUser = GetParameter<int>("MaxProjectsPerUser", 10);
            var maxRolesPerUser = GetParameter<int>("MaxRolesPerUser", 3);
            var maxPermissionsPerRole = GetParameter<int>("MaxPermissionsPerRole", 20);
            var excludeAdminUsers = GetParameter<bool>("ExcludeAdminUsers", true);

            // Get all users with their roles and permissions
            var users = await context.UnitOfWork.Users.GetAllAsync(cancellationToken);
            
            foreach (var user in users)
            {
                // Skip admin users if configured
                if (excludeAdminUsers && IsAdminUser(user))
                {
                    continue;
                }

                await CheckUserPermissions(user, context, result, maxProjectsPerUser, maxRolesPerUser, maxPermissionsPerRole, cancellationToken);
            }

            result.ExecutionTime = DateTime.UtcNow - startTime;
            result.Metadata["UsersAnalyzed"] = users.Count();
            result.Metadata["FindingsCount"] = result.Findings.Count;

            _logger.LogInformation("Completed {RuleName} execution. Found {FindingsCount} issues in {ExecutionTime}ms",
                Name, result.Findings.Count, result.ExecutionTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ExecutionTime = DateTime.UtcNow - startTime;
            
            _logger.LogError(ex, "Error executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);
        }

        return result;
    }

    private async Task CheckUserPermissions(
        User user, 
        AuditContext context, 
        AuditRuleResult result,
        int maxProjectsPerUser,
        int maxRolesPerUser,
        int maxPermissionsPerRole,
        CancellationToken cancellationToken)
    {
        // Check number of projects user has access to
        var userRoles = await context.UnitOfWork.UserRoles.FindAsync(ur => ur.UserId == user.Id, cancellationToken);
        var projectCount = userRoles.Select(ur => ur.ProjectId).Distinct().Count();

        if (projectCount > maxProjectsPerUser)
        {
            var finding = CreateFinding(
                $"User has access to excessive number of projects",
                $"User '{user.Email}' has access to {projectCount} projects, which exceeds the maximum allowed ({maxProjectsPerUser})",
                new { 
                    UserId = user.Id, 
                    UserEmail = user.Email, 
                    ProjectCount = projectCount, 
                    MaxAllowed = maxProjectsPerUser,
                    Projects = userRoles.Select(ur => ur.ProjectId).Distinct().ToList()
                },
                $"Review user's project access and remove unnecessary permissions. Consider using role-based access control."
            );
            finding.AffectedEntity = "User";
            finding.AffectedEntityId = user.Id.ToString();
            result.Findings.Add(finding);
        }

        // Check number of roles per user
        var roleCount = userRoles.Count();
        if (roleCount > maxRolesPerUser)
        {
            var finding = CreateFinding(
                $"User has excessive number of roles",
                $"User '{user.Email}' has {roleCount} roles assigned, which exceeds the maximum allowed ({maxRolesPerUser})",
                new { 
                    UserId = user.Id, 
                    UserEmail = user.Email, 
                    RoleCount = roleCount, 
                    MaxAllowed = maxRolesPerUser,
                    Roles = userRoles.Select(ur => new { ur.RoleId, ur.ProjectId }).ToList()
                },
                $"Consolidate user roles or create composite roles to reduce the number of individual role assignments."
            );
            finding.AffectedEntity = "User";
            finding.AffectedEntityId = user.Id.ToString();
            result.Findings.Add(finding);
        }

        // Check permissions within each role
        foreach (var userRole in userRoles)
        {
            var role = await context.UnitOfWork.Roles.GetByIdAsync(userRole.RoleId, cancellationToken);
            if (role != null)
            {
                var rolePermissions = await context.UnitOfWork.RolePermissions.FindAsync(
                    rp => rp.RoleId == role.Id, cancellationToken);
                
                var permissionCount = rolePermissions.Count();
                if (permissionCount > maxPermissionsPerRole)
                {
                    var finding = CreateFinding(
                        $"Role has excessive permissions",
                        $"Role '{role.Name}' assigned to user '{user.Email}' has {permissionCount} permissions, which exceeds the maximum allowed ({maxPermissionsPerRole})",
                        new { 
                            UserId = user.Id, 
                            UserEmail = user.Email,
                            RoleId = role.Id,
                            RoleName = role.Name,
                            PermissionCount = permissionCount, 
                            MaxAllowed = maxPermissionsPerRole,
                            Permissions = rolePermissions.Select(rp => rp.PermissionId).ToList()
                        },
                        $"Review role permissions and split into multiple roles with more specific access rights."
                    );
                    finding.AffectedEntity = "Role";
                    finding.AffectedEntityId = role.Id.ToString();
                    result.Findings.Add(finding);
                }
            }
        }
    }

    private bool IsAdminUser(User user)
    {
        // Simple admin detection - could be enhanced with more sophisticated logic
        return user.Email.ToLowerInvariant().Contains("admin") || 
               user.Email.ToLowerInvariant().Contains("administrator") ||
               user.Name.ToLowerInvariant().Contains("admin");
    }

    public override async Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default)
    {
        var result = await base.ValidateAsync(cancellationToken);

        // Validate specific parameters
        var maxProjectsPerUser = GetParameter<int>("MaxProjectsPerUser", 10);
        if (maxProjectsPerUser <= 0)
        {
            result.IsValid = false;
            result.Errors.Add("MaxProjectsPerUser must be greater than 0");
        }

        var maxRolesPerUser = GetParameter<int>("MaxRolesPerUser", 3);
        if (maxRolesPerUser <= 0)
        {
            result.IsValid = false;
            result.Errors.Add("MaxRolesPerUser must be greater than 0");
        }

        var maxPermissionsPerRole = GetParameter<int>("MaxPermissionsPerRole", 20);
        if (maxPermissionsPerRole <= 0)
        {
            result.IsValid = false;
            result.Errors.Add("MaxPermissionsPerRole must be greater than 0");
        }

        if (maxProjectsPerUser > 100)
        {
            result.Warnings.Add("MaxProjectsPerUser is very high (>100), consider lowering for better security");
        }

        return result;
    }
}
