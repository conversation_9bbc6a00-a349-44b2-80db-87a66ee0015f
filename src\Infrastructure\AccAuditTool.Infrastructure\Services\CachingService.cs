using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for intelligent caching with automatic invalidation
/// </summary>
public interface ICachingService
{
    /// <summary>
    /// Get cached value or execute factory function
    /// </summary>
    Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get cached value
    /// </summary>
    T? Get<T>(string key);

    /// <summary>
    /// Set cached value
    /// </summary>
    void Set<T>(string key, T value, TimeSpan? expiration = null);

    /// <summary>
    /// Remove cached value
    /// </summary>
    void Remove(string key);

    /// <summary>
    /// Remove all cached values matching pattern
    /// </summary>
    void RemoveByPattern(string pattern);

    /// <summary>
    /// Clear all cached values
    /// </summary>
    void Clear();

    /// <summary>
    /// Get cache statistics
    /// </summary>
    CacheStatistics GetStatistics();
}

/// <summary>
/// Cache statistics information
/// </summary>
public class CacheStatistics
{
    public int TotalEntries { get; set; }
    public long TotalHits { get; set; }
    public long TotalMisses { get; set; }
    public double HitRatio => TotalHits + TotalMisses > 0 ? (double)TotalHits / (TotalHits + TotalMisses) * 100 : 0;
    public long MemoryUsageBytes { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Cache entry metadata
/// </summary>
internal class CacheEntryMetadata
{
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ExpiresAt { get; set; }
    public long AccessCount { get; set; }
    public DateTime LastAccessedAt { get; set; } = DateTime.UtcNow;
    public long SizeBytes { get; set; }
}

/// <summary>
/// Implementation of intelligent caching service
/// </summary>
public class CachingService : ICachingService
{
    private readonly IMemoryCache _memoryCache;
    private readonly ILogger<CachingService> _logger;
    private readonly Dictionary<string, CacheEntryMetadata> _metadata;
    private readonly object _metadataLock = new();
    
    // Statistics tracking
    private long _totalHits;
    private long _totalMisses;

    // Default cache settings
    private static readonly TimeSpan DefaultExpiration = TimeSpan.FromMinutes(30);
    private static readonly MemoryCacheEntryOptions DefaultOptions = new()
    {
        SlidingExpiration = DefaultExpiration,
        Priority = CacheItemPriority.Normal
    };

    public CachingService(IMemoryCache memoryCache, ILogger<CachingService> logger)
    {
        _memoryCache = memoryCache;
        _logger = logger;
        _metadata = new Dictionary<string, CacheEntryMetadata>();
    }

    public async Task<T?> GetOrSetAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null, CancellationToken cancellationToken = default)
    {
        // Try to get from cache first
        var cachedValue = Get<T>(key);
        if (cachedValue != null)
        {
            return cachedValue;
        }

        _logger.LogDebug("Cache miss for key: {Key}. Executing factory function", key);

        try
        {
            // Execute factory function
            var value = await factory();
            
            if (value != null)
            {
                Set(key, value, expiration);
                _logger.LogDebug("Cached new value for key: {Key}", key);
            }

            return value;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing factory function for cache key: {Key}", key);
            throw;
        }
    }

    public T? Get<T>(string key)
    {
        if (_memoryCache.TryGetValue(key, out var value))
        {
            Interlocked.Increment(ref _totalHits);
            UpdateMetadata(key, hit: true);
            
            _logger.LogTrace("Cache hit for key: {Key}", key);
            return (T?)value;
        }

        Interlocked.Increment(ref _totalMisses);
        _logger.LogTrace("Cache miss for key: {Key}", key);
        return default;
    }

    public void Set<T>(string key, T value, TimeSpan? expiration = null)
    {
        if (value == null)
        {
            _logger.LogWarning("Attempted to cache null value for key: {Key}", key);
            return;
        }

        var options = CreateCacheOptions(expiration);
        
        // Calculate approximate size
        var sizeBytes = EstimateObjectSize(value);
        
        // Set up eviction callback
        options.PostEvictionCallbacks.Add(new PostEvictionCallbackRegistration
        {
            EvictionCallback = (key, value, reason, state) =>
            {
                _logger.LogTrace("Cache entry evicted. Key: {Key}, Reason: {Reason}", key, reason);
                RemoveMetadata(key.ToString()!);
            }
        });

        _memoryCache.Set(key, value, options);
        
        // Update metadata
        lock (_metadataLock)
        {
            _metadata[key] = new CacheEntryMetadata
            {
                ExpiresAt = expiration.HasValue ? DateTime.UtcNow.Add(expiration.Value) : null,
                SizeBytes = sizeBytes
            };
        }

        _logger.LogTrace("Cached value for key: {Key}, Size: {Size} bytes, Expiration: {Expiration}", 
            key, sizeBytes, expiration);
    }

    public void Remove(string key)
    {
        _memoryCache.Remove(key);
        RemoveMetadata(key);
        _logger.LogTrace("Removed cache entry for key: {Key}", key);
    }

    public void RemoveByPattern(string pattern)
    {
        var keysToRemove = new List<string>();
        
        lock (_metadataLock)
        {
            foreach (var key in _metadata.Keys)
            {
                if (IsPatternMatch(key, pattern))
                {
                    keysToRemove.Add(key);
                }
            }
        }

        foreach (var key in keysToRemove)
        {
            Remove(key);
        }

        _logger.LogDebug("Removed {Count} cache entries matching pattern: {Pattern}", keysToRemove.Count, pattern);
    }

    public void Clear()
    {
        // MemoryCache doesn't have a clear method, so we need to track keys
        var keysToRemove = new List<string>();
        
        lock (_metadataLock)
        {
            keysToRemove.AddRange(_metadata.Keys);
        }

        foreach (var key in keysToRemove)
        {
            Remove(key);
        }

        _logger.LogInformation("Cleared all cache entries. Removed {Count} entries", keysToRemove.Count);
    }

    public CacheStatistics GetStatistics()
    {
        lock (_metadataLock)
        {
            var totalMemory = _metadata.Values.Sum(m => m.SizeBytes);
            
            return new CacheStatistics
            {
                TotalEntries = _metadata.Count,
                TotalHits = _totalHits,
                TotalMisses = _totalMisses,
                MemoryUsageBytes = totalMemory
            };
        }
    }

    private MemoryCacheEntryOptions CreateCacheOptions(TimeSpan? expiration)
    {
        var options = new MemoryCacheEntryOptions();
        
        if (expiration.HasValue)
        {
            if (expiration.Value <= TimeSpan.FromMinutes(5))
            {
                // Short-lived cache entries use absolute expiration
                options.AbsoluteExpirationRelativeToNow = expiration.Value;
            }
            else
            {
                // Longer-lived entries use sliding expiration
                options.SlidingExpiration = expiration.Value;
            }
        }
        else
        {
            options.SlidingExpiration = DefaultExpiration;
        }

        // Set priority based on expiration time
        options.Priority = expiration?.TotalHours switch
        {
            > 24 => CacheItemPriority.High,
            > 1 => CacheItemPriority.Normal,
            _ => CacheItemPriority.Low
        };

        return options;
    }

    private void UpdateMetadata(string key, bool hit)
    {
        lock (_metadataLock)
        {
            if (_metadata.TryGetValue(key, out var metadata))
            {
                metadata.AccessCount++;
                metadata.LastAccessedAt = DateTime.UtcNow;
            }
        }
    }

    private void RemoveMetadata(string key)
    {
        lock (_metadataLock)
        {
            _metadata.Remove(key);
        }
    }

    private static bool IsPatternMatch(string key, string pattern)
    {
        // Simple pattern matching with wildcards
        if (pattern.Contains('*'))
        {
            var parts = pattern.Split('*', StringSplitOptions.RemoveEmptyEntries);
            var currentIndex = 0;
            
            foreach (var part in parts)
            {
                var index = key.IndexOf(part, currentIndex, StringComparison.OrdinalIgnoreCase);
                if (index == -1) return false;
                currentIndex = index + part.Length;
            }
            
            return true;
        }
        
        return key.Contains(pattern, StringComparison.OrdinalIgnoreCase);
    }

    private static long EstimateObjectSize(object obj)
    {
        try
        {
            // Simple size estimation using JSON serialization
            var json = JsonSerializer.Serialize(obj);
            return System.Text.Encoding.UTF8.GetByteCount(json);
        }
        catch
        {
            // Fallback to a rough estimate
            return obj switch
            {
                string str => str.Length * 2, // Unicode characters
                int => 4,
                long => 8,
                double => 8,
                DateTime => 8,
                Guid => 16,
                _ => 1024 // Default estimate for complex objects
            };
        }
    }
}
