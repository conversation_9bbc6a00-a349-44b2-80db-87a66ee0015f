@using AccAuditTool.Domain.Entities

<div class="audit-grid-view">
    <div class="row">
        @foreach (var audit in Audits)
        {
            <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
                <div class="audit-card">
                    <div class="card-header">
                        <div class="audit-header">
                            <div class="audit-title">
                                <h6 class="audit-name">@audit.Name</h6>
                                <span class="badge bg-@GetStatusColor(audit.Status) audit-status">
                                    <i class="@GetStatusIcon(audit.Status)"></i>
                                    @audit.Status
                                </span>
                            </div>
                            <div class="audit-type">
                                <span class="badge bg-light text-dark type-badge">
                                    <i class="@GetTypeIcon(audit.AuditType)"></i>
                                    @(audit.AuditType ?? "Manual")
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        @if (!string.IsNullOrEmpty(audit.Description))
                        {
                            <p class="audit-description">@TruncateText(audit.Description, 100)</p>
                        }

                        <div class="audit-details">
                            <div class="detail-row">
                                <div class="detail-item">
                                    <i class="fas fa-calendar-alt text-muted"></i>
                                    <div class="detail-content">
                                        <div class="detail-label">Started</div>
                                        <div class="detail-value">@audit.StartedAt.ToString("MMM dd, yyyy HH:mm")</div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-item">
                                    <i class="fas fa-clock text-muted"></i>
                                    <div class="detail-content">
                                        <div class="detail-label">Duration</div>
                                        <div class="detail-value">
                                            @if (audit.CompletedAt.HasValue)
                                            {
                                                <span>@GetDuration(audit.StartedAt, audit.CompletedAt.Value)</span>
                                            }
                                            else if (audit.Status == AuditRunStatus.Running)
                                            {
                                                <span class="text-primary">@GetDuration(audit.StartedAt, DateTime.UtcNow) (running)</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-item">
                                    <i class="fas fa-search text-muted"></i>
                                    <div class="detail-content">
                                        <div class="detail-label">Findings</div>
                                        <div class="detail-value">
                                            @if (audit.TotalFindings > 0)
                                            {
                                                <span class="findings-count">@audit.TotalFindings</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">None</span>
                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="detail-row">
                                <div class="detail-item">
                                    <i class="fas fa-building text-muted"></i>
                                    <div class="detail-content">
                                        <div class="detail-label">Account</div>
                                        <div class="detail-value">@audit.AccountId</div>
                                    </div>
                                </div>
                            </div>

                            @if (audit.ProjectIds.Any())
                            {
                                <div class="detail-row">
                                    <div class="detail-item">
                                        <i class="fas fa-folder text-muted"></i>
                                        <div class="detail-content">
                                            <div class="detail-label">Projects</div>
                                            <div class="detail-value">@audit.ProjectIds.Count project(s)</div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>

                        @if (audit.Status == AuditRunStatus.Running)
                        {
                            <div class="progress-section">
                                <div class="progress-label">
                                    <small class="text-muted">Progress</small>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: @GetProgressPercentage(audit)%">
                                    </div>
                                </div>
                            </div>
                        }

                        @if (audit.Status == AuditRunStatus.Failed && !string.IsNullOrEmpty(audit.ErrorMessage))
                        {
                            <div class="error-section">
                                <div class="alert alert-danger alert-sm">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <strong>Error:</strong> @TruncateText(audit.ErrorMessage, 80)
                                </div>
                            </div>
                        }
                    </div>

                    <div class="card-footer">
                        <div class="audit-actions">
                            <button class="btn btn-outline-primary btn-sm" 
                                    @onclick="() => OnViewAudit.InvokeAsync(audit.Id)"
                                    data-bs-toggle="tooltip" 
                                    title="View Details">
                                <i class="fas fa-eye"></i>
                                View
                            </button>

                            @if (audit.Status == AuditRunStatus.Running)
                            {
                                <button class="btn btn-outline-warning btn-sm" 
                                        @onclick="() => OnCancelAudit.InvokeAsync(audit.Id)"
                                        data-bs-toggle="tooltip" 
                                        title="Cancel Audit">
                                    <i class="fas fa-stop"></i>
                                    Cancel
                                </button>
                            }
                            else if (audit.Status == AuditRunStatus.Failed || audit.Status == AuditRunStatus.Cancelled)
                            {
                                <button class="btn btn-outline-success btn-sm" 
                                        @onclick="() => OnRestartAudit.InvokeAsync(audit.Id)"
                                        data-bs-toggle="tooltip" 
                                        title="Restart Audit">
                                    <i class="fas fa-redo"></i>
                                    Restart
                                </button>
                            }

                            @if (audit.Status != AuditRunStatus.Running)
                            {
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary btn-sm dropdown-toggle" 
                                            type="button" 
                                            data-bs-toggle="dropdown" 
                                            aria-expanded="false">
                                        <i class="fas fa-ellipsis-h"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <button class="dropdown-item" @onclick="() => OnEditAudit.InvokeAsync(audit.Id)">
                                                <i class="fas fa-edit"></i>
                                                Edit
                                            </button>
                                        </li>
                                        <li>
                                            <button class="dropdown-item text-danger" @onclick="() => OnDeleteAudit.InvokeAsync(audit.Id)">
                                                <i class="fas fa-trash"></i>
                                                Delete
                                            </button>
                                        </li>
                                    </ul>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<AuditRun> Audits { get; set; } = new();
    [Parameter] public EventCallback<Guid> OnViewAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnEditAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnDeleteAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnCancelAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnRestartAudit { get; set; }

    private string GetStatusColor(AuditRunStatus status)
    {
        return status switch
        {
            AuditRunStatus.Pending => "secondary",
            AuditRunStatus.Running => "primary",
            AuditRunStatus.Completed => "success",
            AuditRunStatus.Failed => "danger",
            AuditRunStatus.Cancelled => "warning",
            _ => "secondary"
        };
    }

    private string GetStatusIcon(AuditRunStatus status)
    {
        return status switch
        {
            AuditRunStatus.Pending => "fas fa-clock",
            AuditRunStatus.Running => "fas fa-spinner fa-spin",
            AuditRunStatus.Completed => "fas fa-check-circle",
            AuditRunStatus.Failed => "fas fa-times-circle",
            AuditRunStatus.Cancelled => "fas fa-ban",
            _ => "fas fa-question-circle"
        };
    }

    private string GetTypeIcon(string? auditType)
    {
        return auditType?.ToLowerInvariant() switch
        {
            "scheduled" => "fas fa-calendar-alt",
            "triggered" => "fas fa-bolt",
            "manual" => "fas fa-hand-paper",
            _ => "fas fa-hand-paper"
        };
    }

    private string GetDuration(DateTime start, DateTime end)
    {
        var duration = end - start;
        
        if (duration.TotalDays >= 1)
        {
            return $"{(int)duration.TotalDays}d {duration.Hours}h";
        }
        else if (duration.TotalHours >= 1)
        {
            return $"{(int)duration.TotalHours}h {duration.Minutes}m";
        }
        else
        {
            return $"{(int)duration.TotalMinutes}m";
        }
    }

    private int GetProgressPercentage(AuditRun audit)
    {
        if (audit.Status != AuditRunStatus.Running)
            return 0;

        var elapsed = DateTime.UtcNow - audit.StartedAt;
        var estimatedTotal = TimeSpan.FromMinutes(30);
        var percentage = (int)((elapsed.TotalMinutes / estimatedTotal.TotalMinutes) * 100);
        return Math.Min(95, Math.Max(5, percentage));
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }
}

<style>
    .audit-grid-view {
        padding: 1.5rem;
    }

    .audit-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .audit-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .audit-card .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 1rem;
    }

    .audit-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 1rem;
    }

    .audit-title {
        flex: 1;
        min-width: 0;
    }

    .audit-name {
        margin: 0 0 0.5rem 0;
        font-weight: 600;
        color: #495057;
        font-size: 1rem;
        line-height: 1.3;
    }

    .audit-status {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .type-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
        border-radius: 0.2rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        border: 1px solid #dee2e6;
    }

    .audit-card .card-body {
        padding: 1rem;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .audit-description {
        color: #6c757d;
        font-size: 0.875rem;
        line-height: 1.4;
        margin-bottom: 1rem;
    }

    .audit-details {
        flex: 1;
    }

    .detail-row {
        margin-bottom: 0.75rem;
    }

    .detail-row:last-child {
        margin-bottom: 0;
    }

    .detail-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .detail-item i {
        width: 1rem;
        text-align: center;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .detail-content {
        flex: 1;
        min-width: 0;
    }

    .detail-label {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
        margin-bottom: 0.125rem;
    }

    .detail-value {
        font-size: 0.875rem;
        color: #495057;
        font-weight: 500;
        word-wrap: break-word;
    }

    .findings-count {
        color: #dc3545;
        font-weight: 600;
    }

    .progress-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f1f3f4;
    }

    .progress-label {
        margin-bottom: 0.5rem;
    }

    .progress {
        height: 6px;
        background-color: rgba(0, 123, 255, 0.1);
    }

    .error-section {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f1f3f4;
    }

    .alert-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
        margin: 0;
    }

    .audit-card .card-footer {
        background-color: #fff;
        border-top: 1px solid #f1f3f4;
        padding: 1rem;
    }

    .audit-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        align-items: center;
    }

    .audit-actions .btn {
        font-size: 0.8rem;
        padding: 0.375rem 0.75rem;
        display: flex;
        align-items: center;
        gap: 0.375rem;
    }

    .audit-actions .dropdown-toggle::after {
        margin-left: 0;
    }

    .dropdown-menu .dropdown-item {
        font-size: 0.875rem;
        padding: 0.5rem 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .dropdown-menu .dropdown-item i {
        width: 1rem;
        text-align: center;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .audit-grid-view {
            padding: 1rem;
        }

        .audit-header {
            flex-direction: column;
            gap: 0.5rem;
            align-items: stretch;
        }

        .audit-actions {
            justify-content: stretch;
        }

        .audit-actions .btn {
            flex: 1;
            justify-content: center;
        }
    }

    @media (max-width: 576px) {
        .audit-grid-view {
            padding: 0.5rem;
        }

        .audit-card .card-header,
        .audit-card .card-body,
        .audit-card .card-footer {
            padding: 0.75rem;
        }

        .audit-actions {
            flex-direction: column;
        }

        .audit-actions .btn {
            width: 100%;
        }
    }
</style>
