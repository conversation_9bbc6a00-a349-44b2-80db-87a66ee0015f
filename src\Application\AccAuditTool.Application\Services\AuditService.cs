using AccAuditTool.Application.Interfaces;
using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Linq.Expressions;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Implementation of audit service
/// </summary>
public class AuditService : IAuditService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IAuditRuleEngine _ruleEngine;
    private readonly ILogger<AuditService> _logger;

    public AuditService(
        IUnitOfWork unitOfWork,
        IAuditRuleEngine ruleEngine,
        ILogger<AuditService> logger)
    {
        _unitOfWork = unitOfWork;
        _ruleEngine = ruleEngine;
        _logger = logger;
    }

    public async Task<AuditRunResult> ExecuteProjectAuditAsync(
        Guid projectId,
        Guid? configurationId = null,
        string? initiatedBy = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting project audit for project {ProjectId}", projectId);

        try
        {
            // Get project
            var project = await _unitOfWork.Projects.GetByIdAsync(projectId, cancellationToken);
            if (project == null)
            {
                throw new ArgumentException($"Project with ID {projectId} not found");
            }

            // Get or create default configuration
            var configuration = await GetAuditConfigurationAsync(project.AccountId, configurationId, cancellationToken);

            // Create audit run
            var auditRun = new AuditRun
            {
                ProjectId = projectId,
                AccountId = project.AccountId,
                AuditConfigurationId = configuration.Id,
                Status = AuditRunStatus.Running,
                Type = AuditRunType.Manual,
                InitiatedBy = initiatedBy,
                StartedAt = DateTime.UtcNow
            };

            await _unitOfWork.AuditRuns.AddAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Execute audit rules
            var findings = await _ruleEngine.ExecuteRulesAsync(projectId, configuration, cancellationToken);
            var findingsList = findings.ToList();

            // Update audit run with results
            auditRun.CompletedAt = DateTime.UtcNow;
            auditRun.Status = AuditRunStatus.Completed;
            auditRun.FindingsCount = findingsList.Count;
            auditRun.OverallRiskScore = CalculateOverallRiskScore(findingsList);

            // Count analyzed entities
            var permissions = await _unitOfWork.Permissions.FindAsync(p => p.ProjectId == projectId, cancellationToken);
            var users = await _unitOfWork.UserRoles.FindAsync(ur => ur.ProjectId == projectId, cancellationToken);
            
            auditRun.PermissionsAnalyzed = permissions.Count();
            auditRun.UsersAnalyzed = users.Select(ur => ur.UserId).Distinct().Count();

            await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Completed project audit for project {ProjectId}. Found {FindingsCount} findings", 
                projectId, findingsList.Count);

            return new AuditRunResult
            {
                AuditRunId = auditRun.Id,
                Status = auditRun.Status,
                StartedAt = auditRun.StartedAt,
                CompletedAt = auditRun.CompletedAt,
                UsersAnalyzed = auditRun.UsersAnalyzed,
                PermissionsAnalyzed = auditRun.PermissionsAnalyzed,
                FindingsCount = auditRun.FindingsCount,
                OverallRiskScore = auditRun.OverallRiskScore
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing project audit for project {ProjectId}", projectId);
            throw;
        }
    }

    public async Task<AuditRunResult> ExecuteAccountAuditAsync(
        Guid accountId,
        Guid? configurationId = null,
        bool includeAllProjects = true,
        string? initiatedBy = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting account audit for account {AccountId}", accountId);

        try
        {
            // Get account
            var account = await _unitOfWork.Accounts.GetByIdAsync(accountId, cancellationToken);
            if (account == null)
            {
                throw new ArgumentException($"Account with ID {accountId} not found");
            }

            // Get or create default configuration
            var configuration = await GetAuditConfigurationAsync(accountId, configurationId, cancellationToken);

            // Create audit run
            var auditRun = new AuditRun
            {
                AccountId = accountId,
                AuditConfigurationId = configuration.Id,
                Status = AuditRunStatus.Running,
                Type = AuditRunType.Manual,
                InitiatedBy = initiatedBy,
                StartedAt = DateTime.UtcNow
            };

            await _unitOfWork.AuditRuns.AddAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Execute audit rules
            var findings = await _ruleEngine.ExecuteRulesAsync(accountId, configuration, includeAllProjects, cancellationToken);
            var findingsList = findings.ToList();

            // Update audit run with results
            auditRun.CompletedAt = DateTime.UtcNow;
            auditRun.Status = AuditRunStatus.Completed;
            auditRun.FindingsCount = findingsList.Count;
            auditRun.OverallRiskScore = CalculateOverallRiskScore(findingsList);

            // Count analyzed entities across all projects
            var projects = await _unitOfWork.Projects.FindAsync(p => p.AccountId == accountId, cancellationToken);
            var projectIds = projects.Select(p => p.Id).ToList();
            
            var permissions = await _unitOfWork.Permissions.FindAsync(p => projectIds.Contains(p.ProjectId), cancellationToken);
            var users = await _unitOfWork.UserRoles.FindAsync(ur => projectIds.Contains(ur.ProjectId), cancellationToken);
            
            auditRun.PermissionsAnalyzed = permissions.Count();
            auditRun.UsersAnalyzed = users.Select(ur => ur.UserId).Distinct().Count();

            await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Completed account audit for account {AccountId}. Found {FindingsCount} findings", 
                accountId, findingsList.Count);

            return new AuditRunResult
            {
                AuditRunId = auditRun.Id,
                Status = auditRun.Status,
                StartedAt = auditRun.StartedAt,
                CompletedAt = auditRun.CompletedAt,
                UsersAnalyzed = auditRun.UsersAnalyzed,
                PermissionsAnalyzed = auditRun.PermissionsAnalyzed,
                FindingsCount = auditRun.FindingsCount,
                OverallRiskScore = auditRun.OverallRiskScore
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing account audit for account {AccountId}", accountId);
            throw;
        }
    }

    public async Task<AuditRunDetails?> GetAuditRunAsync(Guid auditRunId, CancellationToken cancellationToken = default)
    {
        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun == null)
        {
            return null;
        }

        // Get findings statistics
        var findings = await _unitOfWork.AuditFindings.FindAsync(f => f.AuditRunId == auditRunId, cancellationToken);
        var findingsList = findings.ToList();

        var findingsBySeverity = findingsList
            .GroupBy(f => f.Severity)
            .ToDictionary(g => g.Key, g => g.Count());

        var findingsByStatus = findingsList
            .GroupBy(f => f.Status)
            .ToDictionary(g => g.Key, g => g.Count());

        // Get configuration name
        var configuration = await _unitOfWork.AuditConfigurations.GetByIdAsync(auditRun.AuditConfigurationId, cancellationToken);

        return new AuditRunDetails
        {
            Id = auditRun.Id,
            ProjectId = auditRun.ProjectId,
            AccountId = auditRun.AccountId,
            Status = auditRun.Status,
            Type = auditRun.Type,
            StartedAt = auditRun.StartedAt,
            CompletedAt = auditRun.CompletedAt,
            FindingsCount = auditRun.FindingsCount,
            OverallRiskScore = auditRun.OverallRiskScore,
            InitiatedBy = auditRun.InitiatedBy,
            UsersAnalyzed = auditRun.UsersAnalyzed,
            PermissionsAnalyzed = auditRun.PermissionsAnalyzed,
            ErrorMessage = auditRun.ErrorMessage,
            ConfigurationName = configuration?.Name ?? "Unknown",
            FindingsBySeverity = findingsBySeverity,
            FindingsByStatus = findingsByStatus
        };
    }

    public async Task<IEnumerable<AuditRunSummary>> GetProjectAuditRunsAsync(
        Guid projectId,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var (auditRuns, _) = await _unitOfWork.AuditRuns.GetPagedAsync(
            pageNumber,
            pageSize,
            ar => ar.ProjectId == projectId,
            ar => ar.StartedAt,
            false,
            cancellationToken);

        return auditRuns.Select(ar => new AuditRunSummary
        {
            Id = ar.Id,
            ProjectId = ar.ProjectId,
            AccountId = ar.AccountId,
            Status = ar.Status,
            Type = ar.Type,
            StartedAt = ar.StartedAt,
            CompletedAt = ar.CompletedAt,
            FindingsCount = ar.FindingsCount,
            OverallRiskScore = ar.OverallRiskScore,
            InitiatedBy = ar.InitiatedBy
        });
    }

    public async Task<IEnumerable<AuditRunSummary>> GetAccountAuditRunsAsync(
        Guid accountId,
        int pageNumber = 1,
        int pageSize = 20,
        CancellationToken cancellationToken = default)
    {
        var (auditRuns, _) = await _unitOfWork.AuditRuns.GetPagedAsync(
            pageNumber,
            pageSize,
            ar => ar.AccountId == accountId,
            ar => ar.StartedAt,
            false,
            cancellationToken);

        return auditRuns.Select(ar => new AuditRunSummary
        {
            Id = ar.Id,
            ProjectId = ar.ProjectId,
            AccountId = ar.AccountId,
            Status = ar.Status,
            Type = ar.Type,
            StartedAt = ar.StartedAt,
            CompletedAt = ar.CompletedAt,
            FindingsCount = ar.FindingsCount,
            OverallRiskScore = ar.OverallRiskScore,
            InitiatedBy = ar.InitiatedBy
        });
    }

    public async Task<bool> CancelAuditRunAsync(Guid auditRunId, CancellationToken cancellationToken = default)
    {
        var auditRun = await _unitOfWork.AuditRuns.GetByIdAsync(auditRunId, cancellationToken);
        if (auditRun == null || auditRun.Status != AuditRunStatus.Running)
        {
            return false;
        }

        auditRun.Status = AuditRunStatus.Cancelled;
        auditRun.CompletedAt = DateTime.UtcNow;

        await _unitOfWork.AuditRuns.UpdateAsync(auditRun, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    public async Task<IEnumerable<AuditFindingDetails>> GetAuditFindingsAsync(
        Guid auditRunId,
        FindingSeverity? severity = null,
        FindingStatus? status = null,
        int pageNumber = 1,
        int pageSize = 50,
        CancellationToken cancellationToken = default)
    {
        // Build the query step by step
        var query = _unitOfWork.AuditFindings.FindAsync(f => f.AuditRunId == auditRunId, cancellationToken);
        var allFindings = await query;

        var filteredFindings = allFindings.AsQueryable();

        if (severity.HasValue)
        {
            filteredFindings = filteredFindings.Where(f => f.Severity == severity.Value);
        }

        if (status.HasValue)
        {
            filteredFindings = filteredFindings.Where(f => f.Status == status.Value);
        }

        var findings = filteredFindings
            .OrderByDescending(f => f.RiskScore)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToList();

        return findings.Select(f => new AuditFindingDetails
        {
            Id = f.Id,
            RuleId = f.AuditRule?.RuleId ?? "Unknown",
            RuleName = f.AuditRule?.Name ?? "Unknown",
            Category = f.AuditRule?.Category ?? "Unknown",
            Severity = f.Severity,
            RiskScore = f.RiskScore,
            Title = f.Title,
            Description = f.Description,
            Recommendation = f.Recommendation,
            Status = f.Status,
            CreatedAt = f.CreatedAt,
            ResolvedAt = f.ResolvedAt,
            ResolvedBy = f.ResolvedBy,
            ResolutionNotes = f.ResolutionNotes,
            AffectedUserEmail = f.AffectedUser?.Email,
            AffectedUserName = f.AffectedUser?.Name,
            AffectedResourceName = f.AffectedResource?.Name,
            AffectedResourcePath = f.AffectedResource?.Path
        });
    }

    public async Task<bool> UpdateFindingStatusAsync(
        Guid findingId,
        FindingStatus status,
        string? resolvedBy = null,
        string? resolutionNotes = null,
        CancellationToken cancellationToken = default)
    {
        var finding = await _unitOfWork.AuditFindings.GetByIdAsync(findingId, cancellationToken);
        if (finding == null)
        {
            return false;
        }

        finding.Status = status;
        finding.ResolvedBy = resolvedBy;
        finding.ResolutionNotes = resolutionNotes;

        if (status == FindingStatus.Resolved)
        {
            finding.ResolvedAt = DateTime.UtcNow;
        }

        await _unitOfWork.AuditFindings.UpdateAsync(finding, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return true;
    }

    private async Task<AuditConfiguration> GetAuditConfigurationAsync(
        Guid accountId,
        Guid? configurationId,
        CancellationToken cancellationToken)
    {
        if (configurationId.HasValue)
        {
            var config = await _unitOfWork.AuditConfigurations.GetByIdAsync(configurationId.Value, cancellationToken);
            if (config != null)
            {
                return config;
            }
        }

        // Get default configuration for account
        var defaultConfig = await _unitOfWork.AuditConfigurations.FirstOrDefaultAsync(
            c => c.AccountId == accountId && c.IsDefault, cancellationToken);

        if (defaultConfig != null)
        {
            return defaultConfig;
        }

        // Create default configuration if none exists
        return await CreateDefaultConfigurationAsync(accountId, cancellationToken);
    }

    private async Task<AuditConfiguration> CreateDefaultConfigurationAsync(
        Guid accountId,
        CancellationToken cancellationToken)
    {
        var configuration = new AuditConfiguration
        {
            Name = "Default Configuration",
            Description = "Default audit configuration with standard security rules",
            AccountId = accountId,
            IsDefault = true,
            IsScheduleEnabled = false
        };

        await _unitOfWork.AuditConfigurations.AddAsync(configuration, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return configuration;
    }

    private static int CalculateOverallRiskScore(IEnumerable<AuditFinding> findings)
    {
        var findingsList = findings.ToList();
        if (!findingsList.Any())
        {
            return 0;
        }

        // Calculate weighted average based on severity
        var totalWeight = 0;
        var totalScore = 0;

        foreach (var finding in findingsList)
        {
            var weight = finding.Severity switch
            {
                FindingSeverity.Critical => 4,
                FindingSeverity.High => 3,
                FindingSeverity.Medium => 2,
                FindingSeverity.Low => 1,
                _ => 1
            };

            totalWeight += weight;
            totalScore += finding.RiskScore * weight;
        }

        return totalWeight > 0 ? totalScore / totalWeight : 0;
    }
}


