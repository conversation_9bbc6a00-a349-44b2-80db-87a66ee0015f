.page[b-cuxfxweqrc] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-cuxfxweqrc] {
    flex: 1;
}

.sidebar[b-cuxfxweqrc] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-cuxfxweqrc] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-cuxfxweqrc]  a, .top-row .btn-link[b-cuxfxweqrc] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-cuxfxweqrc] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-cuxfxweqrc] {
        display: none;
    }

    .top-row.auth[b-cuxfxweqrc] {
        justify-content: space-between;
    }

    .top-row a[b-cuxfxweqrc], .top-row .btn-link[b-cuxfxweqrc] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-cuxfxweqrc] {
        flex-direction: row;
    }

    .sidebar[b-cuxfxweqrc] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-cuxfxweqrc] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-cuxfxweqrc], article[b-cuxfxweqrc] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
