# Use the official .NET 8.0 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /app

# Copy solution file and project files
COPY AccAuditTool.sln ./
COPY src/Core/AccAuditTool.Domain/AccAuditTool.Domain.csproj ./src/Core/AccAuditTool.Domain/
COPY src/Application/AccAuditTool.Application/AccAuditTool.Application.csproj ./src/Application/AccAuditTool.Application/
COPY src/Infrastructure/AccAuditTool.Infrastructure/AccAuditTool.Infrastructure.csproj ./src/Infrastructure/AccAuditTool.Infrastructure/
COPY src/Web/AccAuditTool.Web/AccAuditTool.Web.csproj ./src/Web/AccAuditTool.Web/

# Restore dependencies
RUN dotnet restore

# Copy the rest of the source code
COPY . .

# Build the application
RUN dotnet build -c Release --no-restore

# Publish the web application
RUN dotnet publish src/Web/AccAuditTool.Web/AccAuditTool.Web.csproj -c Release -o /app/publish --no-restore

# Use the official .NET 8.0 runtime image for running
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS runtime
WORKDIR /app

# Install SQL Server tools (optional, for database operations)
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy the published application
COPY --from=build /app/publish .

# Create logs directory
RUN mkdir -p /app/logs

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Expose port
EXPOSE 80

# Set the entry point
ENTRYPOINT ["dotnet", "AccAuditTool.Web.dll"]
