namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a permission assignment in the ACC system
/// </summary>
public class Permission : BaseEntity
{
    /// <summary>
    /// Project this permission applies to
    /// </summary>
    public Guid ProjectId { get; set; }
    public Project Project { get; set; } = null!;

    /// <summary>
    /// User this permission is assigned to (if direct assignment)
    /// </summary>
    public Guid? UserId { get; set; }
    public User? User { get; set; }

    /// <summary>
    /// Role this permission is assigned to (if role-based)
    /// </summary>
    public Guid? RoleId { get; set; }
    public Role? Role { get; set; }

    /// <summary>
    /// Company this permission is assigned to (if company-based)
    /// </summary>
    public Guid? CompanyId { get; set; }
    public Company? Company { get; set; }

    /// <summary>
    /// Resource this permission applies to
    /// </summary>
    public Guid? ResourceId { get; set; }
    public Resource? Resource { get; set; }

    /// <summary>
    /// Type of resource (Folder, File, Service, etc.)
    /// </summary>
    public ResourceType ResourceType { get; set; }

    /// <summary>
    /// ACC resource identifier
    /// </summary>
    public string AccResourceId { get; set; } = string.Empty;

    /// <summary>
    /// Actions/permissions granted (JSON array)
    /// </summary>
    public string Actions { get; set; } = "[]";

    /// <summary>
    /// Permission source (Direct, Role, Company, Inherited)
    /// </summary>
    public PermissionSource Source { get; set; } = PermissionSource.Direct;

    /// <summary>
    /// Parent resource ID if this permission is inherited
    /// </summary>
    public string? InheritedFrom { get; set; }

    /// <summary>
    /// Permission grant date
    /// </summary>
    public DateTime GrantedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Permission expiration date (if applicable)
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// User who granted this permission
    /// </summary>
    public string? GrantedBy { get; set; }
}

/// <summary>
/// Resource type enumeration
/// </summary>
public enum ResourceType
{
    Account,
    Project,
    Service,
    Folder,
    File,
    Issue,
    Drawing,
    Model
}

/// <summary>
/// Permission source enumeration
/// </summary>
public enum PermissionSource
{
    Direct,
    Role,
    Company,
    Inherited
}
