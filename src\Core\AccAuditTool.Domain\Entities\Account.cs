namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an ACC account (top-level container for projects)
/// </summary>
public class Account : BaseEntity
{
    /// <summary>
    /// ACC account identifier from Autodesk system
    /// </summary>
    public string AccAccountId { get; set; } = string.Empty;

    /// <summary>
    /// Account name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Account region
    /// </summary>
    public string? Region { get; set; }

    /// <summary>
    /// Account status
    /// </summary>
    public AccountStatus Status { get; set; } = AccountStatus.Active;

    /// <summary>
    /// Account subscription type
    /// </summary>
    public string? SubscriptionType { get; set; }

    /// <summary>
    /// Projects under this account
    /// </summary>
    public ICollection<Project> Projects { get; set; } = new List<Project>();

    /// <summary>
    /// Account-level audit configurations
    /// </summary>
    public ICollection<AuditConfiguration> AuditConfigurations { get; set; } = new List<AuditConfiguration>();
}

/// <summary>
/// Account status enumeration
/// </summary>
public enum AccountStatus
{
    Active,
    Inactive,
    Suspended,
    Trial
}
