using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Rules.Compliance;

/// <summary>
/// Audit rule that checks for proper segregation of duties (SoD) violations
/// </summary>
public class RoleSegregationRule : BaseAuditRule
{
    private readonly ILogger<RoleSegregationRule> _logger;

    // Predefined conflicting role combinations
    private static readonly Dictionary<string, List<string>> ConflictingRoleCombinations = new()
    {
        ["Administrator"] = new() { "Auditor", "Reviewer" },
        ["ProjectManager"] = new() { "QualityController", "Auditor" },
        ["DataEntry"] = new() { "DataApprover", "Reviewer" },
        ["FinancialApprover"] = new() { "FinancialRequester", "Purchaser" },
        ["SecurityOfficer"] = new() { "Developer", "SystemUser" }
    };

    public RoleSegregationRule(ILogger<RoleSegregationRule> logger) 
        : base(
            "COMP-001", 
            "Role Segregation Violations", 
            "Identifies users who have conflicting roles that violate segregation of duties principles",
            AuditRuleCategory.Compliance, 
            AuditSeverity.High)
    {
        _logger = logger;
        
        // Default parameters
        Parameters["EnableCustomConflicts"] = true;
        Parameters["CustomConflictingRoles"] = new Dictionary<string, List<string>>();
        Parameters["StrictMode"] = false; // If true, any combination of administrative roles is flagged
        Parameters["ExcludeEmergencyAccounts"] = true;
        Parameters["CheckCrossProjectConflicts"] = true;
    }

    public override async Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default)
    {
        var result = new AuditRuleResult
        {
            RuleId = RuleId,
            Success = true
        };

        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);

            var enableCustomConflicts = GetParameter<bool>("EnableCustomConflicts", true);
            var strictMode = GetParameter<bool>("StrictMode", false);
            var excludeEmergencyAccounts = GetParameter<bool>("ExcludeEmergencyAccounts", true);
            var checkCrossProjectConflicts = GetParameter<bool>("CheckCrossProjectConflicts", true);

            // Build complete conflict matrix
            var conflictMatrix = BuildConflictMatrix(enableCustomConflicts);

            // Get all users with their roles
            var users = await context.UnitOfWork.Users.GetAllAsync(cancellationToken);
            
            foreach (var user in users)
            {
                // Skip emergency accounts if configured
                if (excludeEmergencyAccounts && IsEmergencyAccount(user))
                {
                    continue;
                }

                await CheckUserRoleConflicts(user, context, result, conflictMatrix, strictMode, checkCrossProjectConflicts, cancellationToken);
            }

            result.ExecutionTime = DateTime.UtcNow - startTime;
            result.Metadata["UsersAnalyzed"] = users.Count();
            result.Metadata["ConflictsFound"] = result.Findings.Count;
            result.Metadata["ConflictRulesApplied"] = conflictMatrix.Count;

            _logger.LogInformation("Completed {RuleName} execution. Found {FindingsCount} role conflicts in {ExecutionTime}ms",
                Name, result.Findings.Count, result.ExecutionTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ExecutionTime = DateTime.UtcNow - startTime;
            
            _logger.LogError(ex, "Error executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);
        }

        return result;
    }

    private Dictionary<string, List<string>> BuildConflictMatrix(bool enableCustomConflicts)
    {
        var matrix = new Dictionary<string, List<string>>(ConflictingRoleCombinations);

        if (enableCustomConflicts)
        {
            var customConflicts = GetParameter<Dictionary<string, List<string>>>("CustomConflictingRoles", new());
            foreach (var conflict in customConflicts)
            {
                if (matrix.ContainsKey(conflict.Key))
                {
                    matrix[conflict.Key].AddRange(conflict.Value);
                }
                else
                {
                    matrix[conflict.Key] = new List<string>(conflict.Value);
                }
            }
        }

        return matrix;
    }

    private async Task CheckUserRoleConflicts(
        User user, 
        AuditContext context, 
        AuditRuleResult result,
        Dictionary<string, List<string>> conflictMatrix,
        bool strictMode,
        bool checkCrossProjectConflicts,
        CancellationToken cancellationToken)
    {
        // Get all roles for the user
        var userRoles = await context.UnitOfWork.UserRoles.FindAsync(ur => ur.UserId == user.Id, cancellationToken);
        
        if (userRoles.Count() < 2)
        {
            return; // No conflicts possible with less than 2 roles
        }

        // Group roles by project if checking cross-project conflicts
        if (checkCrossProjectConflicts)
        {
            await CheckCrossProjectConflicts(user, userRoles, context, result, conflictMatrix, cancellationToken);
        }
        else
        {
            // Check conflicts within each project
            var rolesByProject = userRoles.GroupBy(ur => ur.ProjectId);
            foreach (var projectRoles in rolesByProject)
            {
                await CheckProjectRoleConflicts(user, projectRoles, context, result, conflictMatrix, cancellationToken);
            }
        }

        // Check strict mode violations (any administrative roles combined)
        if (strictMode)
        {
            await CheckStrictModeViolations(user, userRoles, context, result, cancellationToken);
        }
    }

    private async Task CheckCrossProjectConflicts(
        User user,
        IEnumerable<UserRole> userRoles,
        AuditContext context,
        AuditRuleResult result,
        Dictionary<string, List<string>> conflictMatrix,
        CancellationToken cancellationToken)
    {
        var roleDetails = new List<(UserRole UserRole, Role Role)>();
        
        // Get role details
        foreach (var userRole in userRoles)
        {
            var role = await context.UnitOfWork.Roles.GetByIdAsync(userRole.RoleId, cancellationToken);
            if (role != null)
            {
                roleDetails.Add((userRole, role));
            }
        }

        // Check for conflicts across all roles
        for (int i = 0; i < roleDetails.Count; i++)
        {
            for (int j = i + 1; j < roleDetails.Count; j++)
            {
                var role1 = roleDetails[i];
                var role2 = roleDetails[j];

                if (HasRoleConflict(role1.Role.Name, role2.Role.Name, conflictMatrix))
                {
                    var project1 = await context.UnitOfWork.Projects.GetByIdAsync(role1.UserRole.ProjectId, cancellationToken);
                    var project2 = await context.UnitOfWork.Projects.GetByIdAsync(role2.UserRole.ProjectId, cancellationToken);

                    var finding = CreateFinding(
                        $"Segregation of duties violation",
                        $"User '{user.Email}' has conflicting roles: '{role1.Role.Name}' in project '{project1?.Name}' and '{role2.Role.Name}' in project '{project2?.Name}'",
                        new { 
                            UserId = user.Id,
                            UserEmail = user.Email,
                            ConflictingRole1 = new { 
                                RoleId = role1.Role.Id, 
                                RoleName = role1.Role.Name, 
                                ProjectId = role1.UserRole.ProjectId,
                                ProjectName = project1?.Name
                            },
                            ConflictingRole2 = new { 
                                RoleId = role2.Role.Id, 
                                RoleName = role2.Role.Name, 
                                ProjectId = role2.UserRole.ProjectId,
                                ProjectName = project2?.Name
                            },
                            ConflictType = "CrossProject"
                        },
                        $"Remove one of the conflicting roles or implement compensating controls such as additional approval workflows."
                    );
                    finding.AffectedEntity = "User";
                    finding.AffectedEntityId = user.Id.ToString();
                    result.Findings.Add(finding);
                }
            }
        }
    }

    private async Task CheckProjectRoleConflicts(
        User user,
        IGrouping<Guid, UserRole> projectRoles,
        AuditContext context,
        AuditRuleResult result,
        Dictionary<string, List<string>> conflictMatrix,
        CancellationToken cancellationToken)
    {
        var roles = new List<Role>();
        foreach (var userRole in projectRoles)
        {
            var role = await context.UnitOfWork.Roles.GetByIdAsync(userRole.RoleId, cancellationToken);
            if (role != null)
            {
                roles.Add(role);
            }
        }

        var project = await context.UnitOfWork.Projects.GetByIdAsync(projectRoles.Key, cancellationToken);

        // Check for conflicts within the project
        for (int i = 0; i < roles.Count; i++)
        {
            for (int j = i + 1; j < roles.Count; j++)
            {
                if (HasRoleConflict(roles[i].Name, roles[j].Name, conflictMatrix))
                {
                    var finding = CreateFinding(
                        $"Segregation of duties violation within project",
                        $"User '{user.Email}' has conflicting roles within project '{project?.Name}': '{roles[i].Name}' and '{roles[j].Name}'",
                        new { 
                            UserId = user.Id,
                            UserEmail = user.Email,
                            ProjectId = projectRoles.Key,
                            ProjectName = project?.Name,
                            ConflictingRole1 = new { RoleId = roles[i].Id, RoleName = roles[i].Name },
                            ConflictingRole2 = new { RoleId = roles[j].Id, RoleName = roles[j].Name },
                            ConflictType = "WithinProject"
                        },
                        $"Remove one of the conflicting roles within the project or implement compensating controls."
                    );
                    finding.AffectedEntity = "User";
                    finding.AffectedEntityId = user.Id.ToString();
                    result.Findings.Add(finding);
                }
            }
        }
    }

    private async Task CheckStrictModeViolations(
        User user,
        IEnumerable<UserRole> userRoles,
        AuditContext context,
        AuditRuleResult result,
        CancellationToken cancellationToken)
    {
        var adminRoles = new List<string>();
        
        foreach (var userRole in userRoles)
        {
            var role = await context.UnitOfWork.Roles.GetByIdAsync(userRole.RoleId, cancellationToken);
            if (role != null && IsAdministrativeRole(role.Name))
            {
                adminRoles.Add(role.Name);
            }
        }

        if (adminRoles.Count > 1)
        {
            var finding = CreateFinding(
                $"Multiple administrative roles assigned (Strict Mode)",
                $"User '{user.Email}' has multiple administrative roles: {string.Join(", ", adminRoles)}",
                new { 
                    UserId = user.Id,
                    UserEmail = user.Email,
                    AdministrativeRoles = adminRoles,
                    ConflictType = "StrictMode"
                },
                $"In strict mode, users should have only one administrative role. Consider role consolidation or separation."
            );
            finding.AffectedEntity = "User";
            finding.AffectedEntityId = user.Id.ToString();
            result.Findings.Add(finding);
        }
    }

    private bool HasRoleConflict(string role1, string role2, Dictionary<string, List<string>> conflictMatrix)
    {
        return (conflictMatrix.ContainsKey(role1) && conflictMatrix[role1].Contains(role2)) ||
               (conflictMatrix.ContainsKey(role2) && conflictMatrix[role2].Contains(role1));
    }

    private bool IsAdministrativeRole(string roleName)
    {
        var adminKeywords = new[] { "admin", "administrator", "manager", "supervisor", "lead", "director" };
        return adminKeywords.Any(keyword => roleName.ToLowerInvariant().Contains(keyword));
    }

    private bool IsEmergencyAccount(User user)
    {
        var email = user.Email.ToLowerInvariant();
        var name = user.Name.ToLowerInvariant();
        
        return email.Contains("emergency") || 
               email.Contains("break-glass") || 
               name.Contains("emergency") ||
               name.Contains("break-glass");
    }

    public override async Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default)
    {
        var result = await base.ValidateAsync(cancellationToken);

        // Validate custom conflicting roles format
        try
        {
            var customConflicts = GetParameter<Dictionary<string, List<string>>>("CustomConflictingRoles", new());
            foreach (var conflict in customConflicts)
            {
                if (string.IsNullOrEmpty(conflict.Key))
                {
                    result.IsValid = false;
                    result.Errors.Add("Custom conflicting role keys cannot be empty");
                }
                
                if (conflict.Value == null || !conflict.Value.Any())
                {
                    result.Warnings.Add($"Custom conflicting role '{conflict.Key}' has no conflicts defined");
                }
            }
        }
        catch (Exception ex)
        {
            result.IsValid = false;
            result.Errors.Add($"Invalid CustomConflictingRoles format: {ex.Message}");
        }

        return result;
    }
}
