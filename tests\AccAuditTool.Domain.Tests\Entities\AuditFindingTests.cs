using AccAuditTool.Domain.Entities;
using AutoFixture.Xunit2;
using FluentAssertions;

namespace AccAuditTool.Domain.Tests.Entities;

public class AuditFindingTests
{
    [Fact]
    public void AuditFinding_WhenCreated_ShouldHaveDefaultValues()
    {
        // Arrange & Act
        var finding = new AuditFinding();

        // Assert
        finding.Id.Should().NotBeEmpty();
        finding.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        finding.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        finding.IsActive.Should().BeTrue();
        finding.Status.Should().Be(FindingStatus.Open);
        finding.Title.Should().BeEmpty();
        finding.Description.Should().BeEmpty();
        finding.Recommendation.Should().BeEmpty();
    }

    [Theory]
    [AutoData]
    public void AuditFinding_WhenPropertiesSet_ShouldRetainValues(
        string title,
        string description,
        string recommendation,
        int riskScore)
    {
        // Arrange
        var finding = new AuditFinding();

        // Act
        finding.Title = title;
        finding.Description = description;
        finding.Recommendation = recommendation;
        finding.RiskScore = riskScore;
        finding.Severity = FindingSeverity.High;

        // Assert
        finding.Title.Should().Be(title);
        finding.Description.Should().Be(description);
        finding.Recommendation.Should().Be(recommendation);
        finding.RiskScore.Should().Be(riskScore);
        finding.Severity.Should().Be(FindingSeverity.High);
    }

    [Theory]
    [InlineData(FindingSeverity.Low)]
    [InlineData(FindingSeverity.Medium)]
    [InlineData(FindingSeverity.High)]
    [InlineData(FindingSeverity.Critical)]
    public void AuditFinding_WhenSeveritySet_ShouldRetainValue(FindingSeverity severity)
    {
        // Arrange
        var finding = new AuditFinding();

        // Act
        finding.Severity = severity;

        // Assert
        finding.Severity.Should().Be(severity);
    }

    [Theory]
    [InlineData(FindingStatus.Open)]
    [InlineData(FindingStatus.InProgress)]
    [InlineData(FindingStatus.Resolved)]
    [InlineData(FindingStatus.Dismissed)]
    [InlineData(FindingStatus.FalsePositive)]
    public void AuditFinding_WhenStatusSet_ShouldRetainValue(FindingStatus status)
    {
        // Arrange
        var finding = new AuditFinding();

        // Act
        finding.Status = status;

        // Assert
        finding.Status.Should().Be(status);
    }

    [Fact]
    public void AuditFinding_WhenResolved_ShouldSetResolvedAt()
    {
        // Arrange
        var finding = new AuditFinding();
        var resolvedBy = "<EMAIL>";
        var resolutionNotes = "Fixed by updating permissions";

        // Act
        finding.Status = FindingStatus.Resolved;
        finding.ResolvedAt = DateTime.UtcNow;
        finding.ResolvedBy = resolvedBy;
        finding.ResolutionNotes = resolutionNotes;

        // Assert
        finding.Status.Should().Be(FindingStatus.Resolved);
        finding.ResolvedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        finding.ResolvedBy.Should().Be(resolvedBy);
        finding.ResolutionNotes.Should().Be(resolutionNotes);
    }

    [Theory]
    [InlineData(0)]
    [InlineData(25)]
    [InlineData(50)]
    [InlineData(75)]
    [InlineData(100)]
    public void AuditFinding_WhenRiskScoreSet_ShouldRetainValue(int riskScore)
    {
        // Arrange
        var finding = new AuditFinding();

        // Act
        finding.RiskScore = riskScore;

        // Assert
        finding.RiskScore.Should().Be(riskScore);
    }
}
