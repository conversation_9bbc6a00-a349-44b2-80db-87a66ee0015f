namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a company's involvement in a project
/// </summary>
public class ProjectCompany : BaseEntity
{
    /// <summary>
    /// Project the company is involved in
    /// </summary>
    public Guid ProjectId { get; set; }
    public Project Project { get; set; } = null!;

    /// <summary>
    /// Company involved in the project
    /// </summary>
    public Guid CompanyId { get; set; }
    public Company Company { get; set; } = null!;

    /// <summary>
    /// Company's role in the project (General Contractor, Architect, etc.)
    /// </summary>
    public string? ProjectRole { get; set; }

    /// <summary>
    /// Date when the company joined the project
    /// </summary>
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Date when the company left the project (if applicable)
    /// </summary>
    public DateTime? LeftAt { get; set; }

    /// <summary>
    /// Company's status in the project
    /// </summary>
    public ProjectCompanyStatus Status { get; set; } = ProjectCompanyStatus.Active;
}

/// <summary>
/// Project company status enumeration
/// </summary>
public enum ProjectCompanyStatus
{
    Active,
    Inactive,
    Completed,
    Terminated
}
