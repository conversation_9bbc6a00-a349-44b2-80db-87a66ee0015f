using AccAuditTool.Domain.Entities;
using AccAuditTool.Infrastructure.Data;
using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using Testcontainers.MsSql;

namespace AccAuditTool.Infrastructure.Tests.Data;

public class AccAuditDbContextTests : IAsyncLifetime
{
    private readonly MsSqlContainer _msSqlContainer = new MsSqlBuilder()
        .WithImage("mcr.microsoft.com/mssql/server:2022-latest")
        .WithPassword("AccAudit123!")
        .Build();

    private AccAuditDbContext _context = null!;

    public async Task InitializeAsync()
    {
        await _msSqlContainer.StartAsync();

        var options = new DbContextOptionsBuilder<AccAuditDbContext>()
            .UseSqlServer(_msSqlContainer.GetConnectionString())
            .Options;

        _context = new AccAuditDbContext(options);
        await _context.Database.EnsureCreatedAsync();
    }

    public async Task DisposeAsync()
    {
        await _context.DisposeAsync();
        await _msSqlContainer.DisposeAsync();
    }

    [Fact]
    public async Task DbContext_WhenCreated_ShouldCreateAllTables()
    {
        // Act
        var tableNames = await _context.Database
            .SqlQueryRaw<string>("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
            .ToListAsync();

        // Assert
        tableNames.Should().Contain(new[]
        {
            "Accounts",
            "Projects",
            "Users",
            "Companies",
            "Roles",
            "Permissions",
            "Resources",
            "UserRoles",
            "ProjectCompanies",
            "RolePermissions",
            "AuditRuns",
            "AuditFindings",
            "AuditRules",
            "AuditConfigurations",
            "AuditConfigurationRules"
        });
    }

    [Fact]
    public async Task DbContext_WhenAddingUser_ShouldPersistToDatabase()
    {
        // Arrange
        var user = new User
        {
            AccUserId = "test-user-123",
            Email = "<EMAIL>",
            Name = "Test User",
            FirstName = "Test",
            LastName = "User",
            Status = UserStatus.Active
        };

        // Act
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Assert
        var savedUser = await _context.Users.FirstOrDefaultAsync(u => u.AccUserId == "test-user-123");
        savedUser.Should().NotBeNull();
        savedUser!.Email.Should().Be("<EMAIL>");
        savedUser.Name.Should().Be("Test User");
        savedUser.Status.Should().Be(UserStatus.Active);
    }

    [Fact]
    public async Task DbContext_WhenAddingCompanyWithUsers_ShouldMaintainRelationship()
    {
        // Arrange
        var company = new Company
        {
            AccCompanyId = "test-company-123",
            Name = "Test Company",
            Trade = "Construction",
            Status = CompanyStatus.Active
        };

        var user = new User
        {
            AccUserId = "test-user-456",
            Email = "<EMAIL>",
            Name = "Company User",
            Company = company,
            Status = UserStatus.Active
        };

        // Act
        _context.Companies.Add(company);
        _context.Users.Add(user);
        await _context.SaveChangesAsync();

        // Assert
        var savedUser = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.AccUserId == "test-user-456");

        savedUser.Should().NotBeNull();
        savedUser!.Company.Should().NotBeNull();
        savedUser.Company!.Name.Should().Be("Test Company");
        savedUser.CompanyId.Should().Be(company.Id);
    }

    [Fact]
    public async Task DbContext_WhenAddingAuditRun_ShouldPersistWithFindings()
    {
        // Arrange
        var account = new Account
        {
            AccAccountId = "test-account-123",
            Name = "Test Account",
            Status = AccountStatus.Active
        };

        var project = new Project
        {
            AccProjectId = "test-project-123",
            Name = "Test Project",
            Account = account,
            Status = ProjectStatus.Active
        };

        var auditConfig = new AuditConfiguration
        {
            Name = "Test Configuration",
            Account = account,
            IsDefault = true
        };

        var auditRun = new AuditRun
        {
            Project = project,
            Account = account,
            AuditConfiguration = auditConfig,
            Status = AuditRunStatus.Completed,
            Type = AuditRunType.Manual,
            UsersAnalyzed = 10,
            PermissionsAnalyzed = 50,
            FindingsCount = 2,
            OverallRiskScore = 75
        };

        // Act
        _context.Accounts.Add(account);
        _context.Projects.Add(project);
        _context.AuditConfigurations.Add(auditConfig);
        _context.AuditRuns.Add(auditRun);
        await _context.SaveChangesAsync();

        // Assert
        var savedAuditRun = await _context.AuditRuns
            .Include(ar => ar.Project)
            .Include(ar => ar.Account)
            .Include(ar => ar.AuditConfiguration)
            .FirstOrDefaultAsync(ar => ar.Id == auditRun.Id);

        savedAuditRun.Should().NotBeNull();
        savedAuditRun!.Project.Should().NotBeNull();
        savedAuditRun.Project!.Name.Should().Be("Test Project");
        savedAuditRun.Account.Should().NotBeNull();
        savedAuditRun.Account!.Name.Should().Be("Test Account");
        savedAuditRun.AuditConfiguration.Should().NotBeNull();
        savedAuditRun.AuditConfiguration!.Name.Should().Be("Test Configuration");
        savedAuditRun.Status.Should().Be(AuditRunStatus.Completed);
        savedAuditRun.UsersAnalyzed.Should().Be(10);
        savedAuditRun.PermissionsAnalyzed.Should().Be(50);
        savedAuditRun.FindingsCount.Should().Be(2);
        savedAuditRun.OverallRiskScore.Should().Be(75);
    }

    [Fact]
    public async Task DbContext_WhenQueryingWithIndexes_ShouldPerformEfficiently()
    {
        // Arrange
        var account = new Account
        {
            AccAccountId = "perf-test-account",
            Name = "Performance Test Account",
            Status = AccountStatus.Active
        };

        var project = new Project
        {
            AccProjectId = "perf-test-project",
            Name = "Performance Test Project",
            Account = account,
            Status = ProjectStatus.Active
        };

        // Add multiple users for performance testing
        var users = Enumerable.Range(1, 100).Select(i => new User
        {
            AccUserId = $"perf-user-{i}",
            Email = $"user{i}@perftest.com",
            Name = $"Performance User {i}",
            Status = UserStatus.Active
        }).ToList();

        _context.Accounts.Add(account);
        _context.Projects.Add(project);
        _context.Users.AddRange(users);
        await _context.SaveChangesAsync();

        // Act & Assert - Query should use indexes efficiently
        var activeUsers = await _context.Users
            .Where(u => u.Status == UserStatus.Active)
            .CountAsync();

        activeUsers.Should().Be(100);

        var usersByEmail = await _context.Users
            .Where(u => u.Email.Contains("@perftest.com"))
            .CountAsync();

        usersByEmail.Should().Be(100);
    }
}
