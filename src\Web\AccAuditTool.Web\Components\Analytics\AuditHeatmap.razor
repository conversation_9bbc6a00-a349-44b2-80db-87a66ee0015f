@inject IJSRuntime JSRuntime

<div class="audit-heatmap">
    @if (Data.Any())
    {
        <div class="heatmap-container">
            <div class="heatmap-header">
                <div class="day-labels">
                    <div class="day-label"></div> <!-- Empty space for month labels -->
                    @foreach (var day in DayLabels)
                    {
                        <div class="day-label">@day</div>
                    }
                </div>
            </div>
            
            <div class="heatmap-body">
                <div class="month-labels">
                    @for (int month = 0; month < GetMonthCount(); month++)
                    {
                        <div class="month-label">@GetMonthLabel(month)</div>
                    }
                </div>
                
                <div class="heatmap-grid">
                    @for (int week = 0; week < GetWeekCount(); week++)
                    {
                        <div class="heatmap-week">
                            @for (int day = 0; day < 7; day++)
                            {
                                var dataPoint = GetDataPoint(week, day);
                                var intensity = GetIntensity(dataPoint?.Value ?? 0);
                                var date = GetDate(week, day);
                                
                                <div class="heatmap-cell intensity-@intensity" 
                                     data-date="@date.ToString("yyyy-MM-dd")"
                                     data-value="@(dataPoint?.Value ?? 0)"
                                     data-bs-toggle="tooltip" 
                                     title="@GetTooltipText(date, dataPoint?.Value ?? 0)"
                                     @onclick="() => OnCellClick(date, dataPoint?.Value ?? 0)">
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>
        
        <div class="heatmap-legend">
            <div class="legend-label">Less</div>
            <div class="legend-scale">
                @for (int i = 0; i <= 4; i++)
                {
                    <div class="legend-cell intensity-@i"></div>
                }
            </div>
            <div class="legend-label">More</div>
        </div>
        
        <div class="heatmap-stats">
            <div class="stat-item">
                <span class="stat-label">Total Activity:</span>
                <span class="stat-value">@GetTotalActivity()</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Average Daily:</span>
                <span class="stat-value">@GetAverageDaily().ToString("F1")</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Peak Day:</span>
                <span class="stat-value">@GetPeakDay()</span>
            </div>
        </div>
    }
    else
    {
        <div class="no-data-message">
            <i class="fas fa-th text-muted"></i>
            <p class="text-muted">No heatmap data available for the selected time range</p>
        </div>
    }
</div>

@code {
    [Parameter] public List<HeatmapDataPoint> Data { get; set; } = new();
    [Parameter] public string TimeRange { get; set; } = "30d";
    [Parameter] public EventCallback<(DateTime Date, int Value)> OnCellClick { get; set; }

    private readonly string[] DayLabels = { "Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat" };
    private readonly string[] MonthLabels = { "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec" };

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("initializeTooltips");
        }
    }

    private HeatmapDataPoint? GetDataPoint(int week, int day)
    {
        return Data.FirstOrDefault(d => d.Week == week && d.Day == day);
    }

    private int GetIntensity(int value)
    {
        // Map values to intensity levels 0-4
        return value switch
        {
            0 => 0,
            1 => 1,
            2 or 3 => 2,
            4 or 5 or 6 => 3,
            _ => 4
        };
    }

    private DateTime GetDate(int week, int day)
    {
        // Calculate the actual date based on week and day
        var startDate = DateTime.Today.AddDays(-GetTotalDays());
        var startOfWeek = startDate.AddDays(-(int)startDate.DayOfWeek);
        return startOfWeek.AddDays(week * 7 + day);
    }

    private int GetTotalDays()
    {
        return TimeRange switch
        {
            "7d" => 7,
            "30d" => 30,
            "90d" => 90,
            "6m" => 180,
            "1y" => 365,
            _ => 30
        };
    }

    private int GetWeekCount()
    {
        return TimeRange switch
        {
            "7d" => 1,
            "30d" => 5,
            "90d" => 13,
            "6m" => 26,
            "1y" => 53,
            _ => 5
        };
    }

    private int GetMonthCount()
    {
        return TimeRange switch
        {
            "7d" => 1,
            "30d" => 1,
            "90d" => 3,
            "6m" => 6,
            "1y" => 12,
            _ => 1
        };
    }

    private string GetMonthLabel(int monthIndex)
    {
        var currentMonth = DateTime.Today.Month - 1;
        var targetMonth = (currentMonth - GetMonthCount() + monthIndex + 1 + 12) % 12;
        return MonthLabels[targetMonth];
    }

    private string GetTooltipText(DateTime date, int value)
    {
        var activityText = value switch
        {
            0 => "No activity",
            1 => "1 audit",
            _ => $"{value} audits"
        };
        
        return $"{activityText} on {date:MMM dd, yyyy}";
    }

    private int GetTotalActivity()
    {
        return Data.Sum(d => d.Value);
    }

    private double GetAverageDaily()
    {
        var totalDays = GetTotalDays();
        return totalDays > 0 ? (double)GetTotalActivity() / totalDays : 0;
    }

    private string GetPeakDay()
    {
        var peakData = Data.OrderByDescending(d => d.Value).FirstOrDefault();
        if (peakData == null) return "None";
        
        var peakDate = GetDate(peakData.Week, peakData.Day);
        return $"{peakDate:MMM dd} ({peakData.Value})";
    }

    public class HeatmapDataPoint
    {
        public int Week { get; set; }
        public int Day { get; set; }
        public int Value { get; set; }
    }
}

<style>
    .audit-heatmap {
        width: 100%;
    }

    .heatmap-container {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .heatmap-header {
        display: flex;
    }

    .day-labels {
        display: flex;
        gap: 2px;
        margin-left: 60px; /* Space for month labels */
    }

    .day-label {
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        color: #6c757d;
        font-weight: 500;
    }

    .heatmap-body {
        display: flex;
        gap: 0.5rem;
    }

    .month-labels {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        width: 50px;
        padding-right: 10px;
    }

    .month-label {
        font-size: 0.7rem;
        color: #6c757d;
        font-weight: 500;
        text-align: right;
    }

    .heatmap-grid {
        display: flex;
        gap: 2px;
        flex-wrap: wrap;
    }

    .heatmap-week {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .heatmap-cell {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
    }

    .heatmap-cell:hover {
        border-color: #495057;
        transform: scale(1.1);
    }

    /* Intensity levels */
    .intensity-0 {
        background-color: #ebedf0;
    }

    .intensity-1 {
        background-color: #c6e48b;
    }

    .intensity-2 {
        background-color: #7bc96f;
    }

    .intensity-3 {
        background-color: #239a3b;
    }

    .intensity-4 {
        background-color: #196127;
    }

    .heatmap-legend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .legend-scale {
        display: flex;
        gap: 2px;
    }

    .legend-cell {
        width: 10px;
        height: 10px;
        border-radius: 2px;
    }

    .legend-label {
        font-size: 0.75rem;
        color: #6c757d;
    }

    .heatmap-stats {
        display: flex;
        justify-content: center;
        gap: 2rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        font-size: 0.8rem;
    }

    .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.25rem;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 500;
    }

    .stat-value {
        color: #495057;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        gap: 1rem;
    }

    .no-data-message i {
        font-size: 3rem;
    }

    .no-data-message p {
        margin: 0;
        font-size: 0.875rem;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .day-labels {
            margin-left: 40px;
        }

        .month-labels {
            width: 30px;
        }

        .month-label {
            font-size: 0.6rem;
        }

        .day-label {
            font-size: 0.6rem;
        }

        .heatmap-cell {
            width: 10px;
            height: 10px;
        }

        .legend-cell {
            width: 8px;
            height: 8px;
        }

        .heatmap-stats {
            flex-direction: column;
            gap: 1rem;
        }

        .stat-item {
            flex-direction: row;
            justify-content: space-between;
        }
    }

    @media (max-width: 576px) {
        .heatmap-cell {
            width: 8px;
            height: 8px;
        }

        .day-labels {
            margin-left: 30px;
        }

        .month-labels {
            width: 25px;
        }
    }
</style>
