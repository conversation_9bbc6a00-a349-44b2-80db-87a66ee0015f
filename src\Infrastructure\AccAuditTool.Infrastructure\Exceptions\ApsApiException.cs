using System.Net;

namespace AccAuditTool.Infrastructure.Exceptions;

/// <summary>
/// Base exception for APS API related errors
/// </summary>
public class ApsApiException : Exception
{
    public HttpStatusCode? StatusCode { get; }
    public string? ErrorCode { get; }
    public string? RequestId { get; }

    public ApsApiException(string message) : base(message)
    {
    }

    public ApsApiException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public ApsApiException(string message, HttpStatusCode statusCode, string? errorCode = null, string? requestId = null) 
        : base(message)
    {
        StatusCode = statusCode;
        ErrorCode = errorCode;
        RequestId = requestId;
    }
}

/// <summary>
/// Exception thrown when authentication fails
/// </summary>
public class ApsAuthenticationException : ApsApiException
{
    public ApsAuthenticationException(string message) : base(message)
    {
    }

    public ApsAuthenticationException(string message, Exception innerException) : base(message, innerException)
    {
    }
}

/// <summary>
/// Exception thrown when rate limits are exceeded
/// </summary>
public class ApsRateLimitException : ApsApiException
{
    public TimeSpan? RetryAfter { get; }
    public int? RemainingRequests { get; }

    public ApsRateLimitException(string message, TimeSpan? retryAfter = null, int? remainingRequests = null) 
        : base(message, HttpStatusCode.TooManyRequests)
    {
        RetryAfter = retryAfter;
        RemainingRequests = remainingRequests;
    }
}

/// <summary>
/// Exception thrown when data synchronization fails
/// </summary>
public class DataSyncException : Exception
{
    public string? EntityType { get; }
    public string? EntityId { get; }
    public List<string> ValidationErrors { get; } = new();

    public DataSyncException(string message, string? entityType = null, string? entityId = null) : base(message)
    {
        EntityType = entityType;
        EntityId = entityId;
    }

    public DataSyncException(string message, Exception innerException, string? entityType = null, string? entityId = null) 
        : base(message, innerException)
    {
        EntityType = entityType;
        EntityId = entityId;
    }

    public DataSyncException(string message, List<string> validationErrors, string? entityType = null, string? entityId = null) 
        : base(message)
    {
        EntityType = entityType;
        EntityId = entityId;
        ValidationErrors = validationErrors;
    }
}

/// <summary>
/// Exception thrown when configuration is invalid
/// </summary>
public class ConfigurationException : Exception
{
    public string? ConfigurationKey { get; }

    public ConfigurationException(string message, string? configurationKey = null) : base(message)
    {
        ConfigurationKey = configurationKey;
    }

    public ConfigurationException(string message, Exception innerException, string? configurationKey = null) 
        : base(message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}

/// <summary>
/// Exception thrown when audit operations fail
/// </summary>
public class AuditException : Exception
{
    public Guid? AuditRunId { get; }
    public string? RuleId { get; }

    public AuditException(string message, Guid? auditRunId = null, string? ruleId = null) : base(message)
    {
        AuditRunId = auditRunId;
        RuleId = ruleId;
    }

    public AuditException(string message, Exception innerException, Guid? auditRunId = null, string? ruleId = null) 
        : base(message, innerException)
    {
        AuditRunId = auditRunId;
        RuleId = ruleId;
    }
}
