<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="8.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authorization" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Blazorise.Bootstrap5" Version="1.4.2" />
    <PackageReference Include="Blazorise.Icons.FontAwesome" Version="1.4.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Application\AccAuditTool.Application\AccAuditTool.Application.csproj" />
    <ProjectReference Include="..\..\Infrastructure\AccAuditTool.Infrastructure\AccAuditTool.Infrastructure.csproj" />
  </ItemGroup>

</Project>
