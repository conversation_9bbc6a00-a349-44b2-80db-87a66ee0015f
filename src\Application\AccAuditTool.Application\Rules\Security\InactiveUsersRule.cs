using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Rules.Security;

/// <summary>
/// Audit rule that identifies inactive users who still have active permissions
/// </summary>
public class InactiveUsersRule : BaseAuditRule
{
    private readonly ILogger<InactiveUsersRule> _logger;

    public InactiveUsersRule(ILogger<InactiveUsersRule> logger) 
        : base(
            "SEC-002", 
            "Inactive Users with Active Permissions", 
            "Identifies users who have not been active for a specified period but still retain active permissions",
            AuditRuleCategory.Security, 
            AuditSeverity.Medium)
    {
        _logger = logger;
        
        // Default parameters
        Parameters["InactivityThresholdDays"] = 90;
        Parameters["CheckLastSeenDate"] = true;
        Parameters["CheckLastLoginDate"] = true;
        Parameters["ExcludeServiceAccounts"] = true;
        Parameters["RequireExplicitDeactivation"] = false;
    }

    public override async Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default)
    {
        var result = new AuditRuleResult
        {
            RuleId = RuleId,
            Success = true
        };

        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);

            var inactivityThresholdDays = GetParameter<int>("InactivityThresholdDays", 90);
            var checkLastSeenDate = GetParameter<bool>("CheckLastSeenDate", true);
            var checkLastLoginDate = GetParameter<bool>("CheckLastLoginDate", true);
            var excludeServiceAccounts = GetParameter<bool>("ExcludeServiceAccounts", true);
            var requireExplicitDeactivation = GetParameter<bool>("RequireExplicitDeactivation", false);

            var cutoffDate = DateTime.UtcNow.AddDays(-inactivityThresholdDays);

            // Get all active users
            var users = await context.UnitOfWork.Users.FindAsync(
                u => u.Status == UserStatus.Active, cancellationToken);

            foreach (var user in users)
            {
                // Skip service accounts if configured
                if (excludeServiceAccounts && IsServiceAccount(user))
                {
                    continue;
                }

                var isInactive = await CheckUserInactivity(user, cutoffDate, checkLastSeenDate, checkLastLoginDate, cancellationToken);
                
                if (isInactive)
                {
                    await ProcessInactiveUser(user, context, result, inactivityThresholdDays, requireExplicitDeactivation, cancellationToken);
                }
            }

            result.ExecutionTime = DateTime.UtcNow - startTime;
            result.Metadata["UsersAnalyzed"] = users.Count();
            result.Metadata["InactiveUsersFound"] = result.Findings.Count;
            result.Metadata["InactivityThresholdDays"] = inactivityThresholdDays;

            _logger.LogInformation("Completed {RuleName} execution. Found {FindingsCount} inactive users in {ExecutionTime}ms",
                Name, result.Findings.Count, result.ExecutionTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ExecutionTime = DateTime.UtcNow - startTime;
            
            _logger.LogError(ex, "Error executing {RuleName} for audit run {AuditRunId}", Name, context.AuditRunId);
        }

        return result;
    }

    private async Task<bool> CheckUserInactivity(
        User user, 
        DateTime cutoffDate, 
        bool checkLastSeenDate, 
        bool checkLastLoginDate,
        CancellationToken cancellationToken)
    {
        var isInactive = false;

        // Check last seen date
        if (checkLastSeenDate && user.LastSeenAt.HasValue)
        {
            if (user.LastSeenAt.Value < cutoffDate)
            {
                isInactive = true;
            }
        }
        else if (checkLastSeenDate && !user.LastSeenAt.HasValue)
        {
            // No last seen date recorded - consider inactive
            isInactive = true;
        }

        // Additional checks could be added here for last login date from external systems
        // For now, we rely on LastSeenAt from the ACC data

        return isInactive;
    }

    private async Task ProcessInactiveUser(
        User user, 
        AuditContext context, 
        AuditRuleResult result,
        int inactivityThresholdDays,
        bool requireExplicitDeactivation,
        CancellationToken cancellationToken)
    {
        // Get user's current permissions and roles
        var userRoles = await context.UnitOfWork.UserRoles.FindAsync(ur => ur.UserId == user.Id, cancellationToken);
        var permissions = await context.UnitOfWork.Permissions.FindAsync(p => p.SubjectId == user.AccUserId, cancellationToken);

        var daysSinceLastSeen = user.LastSeenAt.HasValue 
            ? (DateTime.UtcNow - user.LastSeenAt.Value).Days 
            : (DateTime.UtcNow - user.CreatedAt).Days;

        var severity = DetermineSeverity(daysSinceLastSeen, userRoles.Count(), permissions.Count());

        var finding = CreateFinding(
            $"Inactive user with active permissions",
            $"User '{user.Email}' has been inactive for {daysSinceLastSeen} days (threshold: {inactivityThresholdDays} days) but still has {userRoles.Count()} roles and {permissions.Count()} permissions",
            new { 
                UserId = user.Id,
                UserEmail = user.Email,
                LastSeenAt = user.LastSeenAt,
                DaysSinceLastSeen = daysSinceLastSeen,
                InactivityThreshold = inactivityThresholdDays,
                ActiveRoles = userRoles.Count(),
                ActivePermissions = permissions.Count(),
                Roles = userRoles.Select(ur => new { ur.RoleId, ur.ProjectId }).ToList(),
                Permissions = permissions.Select(p => new { p.Id, p.ResourceId, p.Actions }).Take(10).ToList() // Limit for performance
            },
            GetRecommendation(requireExplicitDeactivation, userRoles.Any(), permissions.Any())
        );

        finding.AffectedEntity = "User";
        finding.AffectedEntityId = user.Id.ToString();
        finding.Severity = severity;
        
        result.Findings.Add(finding);
    }

    private AuditSeverity DetermineSeverity(int daysSinceLastSeen, int roleCount, int permissionCount)
    {
        // More severe if user has been inactive longer and has more permissions
        if (daysSinceLastSeen > 365) // Over a year
        {
            return AuditSeverity.High;
        }
        else if (daysSinceLastSeen > 180) // Over 6 months
        {
            return roleCount > 5 || permissionCount > 20 ? AuditSeverity.High : AuditSeverity.Medium;
        }
        else if (daysSinceLastSeen > 90) // Over 3 months
        {
            return roleCount > 10 || permissionCount > 50 ? AuditSeverity.Medium : AuditSeverity.Low;
        }
        else
        {
            return AuditSeverity.Low;
        }
    }

    private string GetRecommendation(bool requireExplicitDeactivation, bool hasRoles, bool hasPermissions)
    {
        var recommendations = new List<string>();

        if (requireExplicitDeactivation)
        {
            recommendations.Add("Explicitly deactivate the user account");
        }
        else
        {
            recommendations.Add("Review user status and consider deactivation");
        }

        if (hasRoles)
        {
            recommendations.Add("Remove or suspend user role assignments");
        }

        if (hasPermissions)
        {
            recommendations.Add("Revoke direct permissions granted to the user");
        }

        recommendations.Add("Implement automated user lifecycle management");
        recommendations.Add("Set up regular access reviews for inactive users");

        return string.Join(". ", recommendations) + ".";
    }

    private bool IsServiceAccount(User user)
    {
        // Simple service account detection
        var email = user.Email.ToLowerInvariant();
        var name = user.Name.ToLowerInvariant();
        
        return email.Contains("service") || 
               email.Contains("system") || 
               email.Contains("bot") ||
               email.Contains("api") ||
               name.Contains("service") ||
               name.Contains("system") ||
               name.Contains("automation");
    }

    public override async Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default)
    {
        var result = await base.ValidateAsync(cancellationToken);

        // Validate specific parameters
        var inactivityThresholdDays = GetParameter<int>("InactivityThresholdDays", 90);
        if (inactivityThresholdDays <= 0)
        {
            result.IsValid = false;
            result.Errors.Add("InactivityThresholdDays must be greater than 0");
        }

        if (inactivityThresholdDays < 30)
        {
            result.Warnings.Add("InactivityThresholdDays is very low (<30 days), may generate many false positives");
        }

        if (inactivityThresholdDays > 365)
        {
            result.Warnings.Add("InactivityThresholdDays is very high (>365 days), may miss security risks");
        }

        return result;
    }
}
