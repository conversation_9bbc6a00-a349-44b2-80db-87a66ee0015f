@inherits LayoutView
@inject IJSRuntime JSRuntime

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <div class="top-row px-4">
            <TopBar />
        </div>

        <article class="content px-4">
            @Body
        </article>
    </main>
</div>

<div id="blazor-error-ui">
    Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") == "Development" ? "An error has occurred. This application may no longer respond until reloaded." : "An error has occurred."
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("initializeLayout");
        }
    }
}

<script>
    window.initializeLayout = function () {
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Sidebar toggle functionality
        const sidebarToggle = document.querySelector('.sidebar-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });

            // Restore sidebar state
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert.auto-dismiss');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    };
</script>

<style>
    .page {
        position: relative;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
    }

    .sidebar {
        background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
        width: 260px;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 1000;
        transition: all 0.3s ease;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .sidebar.collapsed {
        width: 70px;
    }

    .sidebar::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    .sidebar::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
    }

    .sidebar::-webkit-scrollbar-thumb:hover {
        background: rgba(255, 255, 255, 0.5);
    }

    main {
        flex: 1;
        margin-left: 260px;
        transition: margin-left 0.3s ease;
        min-height: 100vh;
        background-color: #f8f9fa;
    }

    .sidebar.collapsed + main {
        margin-left: 70px;
    }

    .top-row {
        background-color: #fff;
        border-bottom: 1px solid #e3e6f0;
        height: 60px;
        display: flex;
        align-items: center;
        position: sticky;
        top: 0;
        z-index: 999;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    }

    .content {
        padding: 1.5rem 1rem;
        flex: 1;
    }

    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

    .btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
        box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
            width: 260px;
        }

        .sidebar.show {
            transform: translateX(0);
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
            width: 260px;
        }

        main {
            margin-left: 0;
        }

        .sidebar.collapsed + main {
            margin-left: 0;
        }

        .content {
            padding: 1rem 0.5rem;
        }
    }

    @media (max-width: 576px) {
        .top-row {
            height: 50px;
        }

        .content {
            padding: 0.75rem 0.25rem;
        }
    }

    /* Dark mode support */
    @media (prefers-color-scheme: dark) {
        main {
            background-color: #1a1a1a;
        }

        .top-row {
            background-color: #2d2d2d;
            border-bottom-color: #404040;
            color: #fff;
        }

        .content {
            color: #e0e0e0;
        }
    }

    /* Print styles */
    @media print {
        .sidebar {
            display: none;
        }

        main {
            margin-left: 0;
        }

        .top-row {
            display: none;
        }

        .content {
            padding: 0;
        }
    }

    /* Animation for page transitions */
    .content {
        animation: fadeIn 0.3s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading states */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 3rem;
        height: 3rem;
    }

    /* Accessibility improvements */
    .skip-link {
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 10000;
    }

    .skip-link:focus {
        top: 6px;
    }

    /* Focus indicators */
    .btn:focus-visible,
    .nav-link:focus-visible,
    .form-control:focus-visible {
        outline: 2px solid #0d6efd;
        outline-offset: 2px;
    }
</style>
