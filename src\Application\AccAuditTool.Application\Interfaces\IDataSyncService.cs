namespace AccAuditTool.Application.Interfaces;

/// <summary>
/// Interface for synchronizing data from ACC
/// </summary>
public interface IDataSyncService
{
    /// <summary>
    /// Synchronize all data for an account
    /// </summary>
    Task<DataSyncResult> SyncAccountDataAsync(
        string accAccountId,
        bool fullSync = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronize data for a specific project
    /// </summary>
    Task<DataSyncResult> SyncProjectDataAsync(
        string accProjectId,
        bool fullSync = false,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronize user data across all accessible projects
    /// </summary>
    Task<DataSyncResult> SyncUserDataAsync(
        string? accUserId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Synchronize permission data for a project
    /// </summary>
    Task<DataSyncResult> SyncPermissionDataAsync(
        string accProjectId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Get last sync status for an account
    /// </summary>
    Task<DataSyncStatus?> GetLastSyncStatusAsync(
        string accAccountId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Schedule automatic data synchronization
    /// </summary>
    Task<bool> ScheduleDataSyncAsync(
        string accAccountId,
        TimeSpan interval,
        CancellationToken cancellationToken = default);
}

/// <summary>
/// Result of a data synchronization operation
/// </summary>
public class DataSyncResult
{
    public bool Success { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public TimeSpan Duration => CompletedAt - StartedAt;
    public string? ErrorMessage { get; set; }
    
    // Sync statistics
    public int AccountsProcessed { get; set; }
    public int ProjectsProcessed { get; set; }
    public int UsersProcessed { get; set; }
    public int CompaniesProcessed { get; set; }
    public int RolesProcessed { get; set; }
    public int PermissionsProcessed { get; set; }
    public int ResourcesProcessed { get; set; }
    
    // Change statistics
    public int AccountsCreated { get; set; }
    public int AccountsUpdated { get; set; }
    public int ProjectsCreated { get; set; }
    public int ProjectsUpdated { get; set; }
    public int UsersCreated { get; set; }
    public int UsersUpdated { get; set; }
    public int CompaniesCreated { get; set; }
    public int CompaniesUpdated { get; set; }
    public int RolesCreated { get; set; }
    public int RolesUpdated { get; set; }
    public int PermissionsCreated { get; set; }
    public int PermissionsUpdated { get; set; }
    public int PermissionsDeleted { get; set; }
    public int ResourcesCreated { get; set; }
    public int ResourcesUpdated { get; set; }
    
    public List<string> Warnings { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Status of data synchronization for an account
/// </summary>
public class DataSyncStatus
{
    public string AccAccountId { get; set; } = string.Empty;
    public DateTime? LastFullSync { get; set; }
    public DateTime? LastIncrementalSync { get; set; }
    public bool IsScheduled { get; set; }
    public TimeSpan? SyncInterval { get; set; }
    public DateTime? NextScheduledSync { get; set; }
    public DataSyncResult? LastSyncResult { get; set; }
}

/// <summary>
/// Service for extracting data from Autodesk Construction Cloud APIs
/// </summary>
public interface IAccDataService
{
    /// <summary>
    /// Get all accessible accounts
    /// </summary>
    Task<IEnumerable<AccAccountDto>> GetAccountsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get projects for a specific account
    /// </summary>
    Task<IEnumerable<AccProjectDto>> GetProjectsAsync(string accountId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get users for a specific project
    /// </summary>
    Task<IEnumerable<AccUserDto>> GetProjectUsersAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get user details
    /// </summary>
    Task<AccUserDto?> GetUserDetailsAsync(string userId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get permissions for a folder
    /// </summary>
    Task<IEnumerable<AccPermissionDto>> GetFolderPermissionsAsync(string projectId, string folderId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get folders for a project
    /// </summary>
    Task<IEnumerable<AccResourceDto>> GetProjectFoldersAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get companies for a project
    /// </summary>
    Task<IEnumerable<AccCompanyDto>> GetProjectCompaniesAsync(string projectId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get roles for a project
    /// </summary>
    Task<IEnumerable<AccRoleDto>> GetProjectRolesAsync(string projectId, CancellationToken cancellationToken = default);
}
