using AccAuditTool.Domain.Entities;
using AutoFixture.Xunit2;
using FluentAssertions;

namespace AccAuditTool.Domain.Tests.Entities;

public class UserTests
{
    [Fact]
    public void User_WhenCreated_ShouldHaveDefaultValues()
    {
        // Arrange & Act
        var user = new User();

        // Assert
        user.Id.Should().NotBeEmpty();
        user.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.UpdatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));
        user.IsActive.Should().BeTrue();
        user.Status.Should().Be(UserStatus.Active);
        user.UserRoles.Should().NotBeNull().And.BeEmpty();
        user.DirectPermissions.Should().NotBeNull().And.BeEmpty();
        user.AuditFindings.Should().NotBeNull().And.BeEmpty();
    }

    [Theory]
    [AutoData]
    public void User_WhenPropertiesSet_ShouldRetainValues(
        string accUserId,
        string email,
        string name,
        string firstName,
        string lastName)
    {
        // Arrange
        var user = new User();

        // Act
        user.AccUserId = accUserId;
        user.Email = email;
        user.Name = name;
        user.FirstName = firstName;
        user.LastName = lastName;
        user.Status = UserStatus.Inactive;

        // Assert
        user.AccUserId.Should().Be(accUserId);
        user.Email.Should().Be(email);
        user.Name.Should().Be(name);
        user.FirstName.Should().Be(firstName);
        user.LastName.Should().Be(lastName);
        user.Status.Should().Be(UserStatus.Inactive);
    }

    [Fact]
    public void User_WhenLastSeenAtSet_ShouldRetainValue()
    {
        // Arrange
        var user = new User();
        var lastSeenAt = DateTime.UtcNow.AddDays(-1);

        // Act
        user.LastSeenAt = lastSeenAt;

        // Assert
        user.LastSeenAt.Should().Be(lastSeenAt);
    }

    [Theory]
    [InlineData(UserStatus.Active)]
    [InlineData(UserStatus.Inactive)]
    [InlineData(UserStatus.Suspended)]
    [InlineData(UserStatus.PendingActivation)]
    public void User_WhenStatusSet_ShouldRetainValue(UserStatus status)
    {
        // Arrange
        var user = new User();

        // Act
        user.Status = status;

        // Assert
        user.Status.Should().Be(status);
    }

    [Fact]
    public void User_WhenCompanyAssigned_ShouldSetCompanyId()
    {
        // Arrange
        var user = new User();
        var company = new Company { Id = Guid.NewGuid(), Name = "Test Company" };

        // Act
        user.Company = company;
        user.CompanyId = company.Id;

        // Assert
        user.Company.Should().Be(company);
        user.CompanyId.Should().Be(company.Id);
    }
}
