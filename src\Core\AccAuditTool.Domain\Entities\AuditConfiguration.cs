namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an audit configuration defining which rules to run and how
/// </summary>
public class AuditConfiguration : BaseEntity
{
    /// <summary>
    /// Configuration name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Configuration description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Account this configuration belongs to
    /// </summary>
    public Guid AccountId { get; set; }
    public Account Account { get; set; } = null!;

    /// <summary>
    /// Whether this is the default configuration for the account
    /// </summary>
    public bool IsDefault { get; set; } = false;

    /// <summary>
    /// Configuration settings as JSON
    /// </summary>
    public string? Settings { get; set; }

    /// <summary>
    /// Schedule for automatic audit runs (cron expression)
    /// </summary>
    public string? Schedule { get; set; }

    /// <summary>
    /// Whether scheduled audits are enabled
    /// </summary>
    public bool IsScheduleEnabled { get; set; } = false;

    /// <summary>
    /// Email notifications configuration
    /// </summary>
    public string? NotificationSettings { get; set; }

    /// <summary>
    /// Rules included in this configuration
    /// </summary>
    public ICollection<AuditConfigurationRule> AuditConfigurationRules { get; set; } = new List<AuditConfigurationRule>();

    /// <summary>
    /// Audit runs using this configuration
    /// </summary>
    public ICollection<AuditRun> AuditRuns { get; set; } = new List<AuditRun>();
}
