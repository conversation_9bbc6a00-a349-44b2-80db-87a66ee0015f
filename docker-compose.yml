version: '3.8'

services:
  # SQL Server database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: accaudit-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=************
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - accaudit-network
    restart: unless-stopped

  # Redis cache
  redis:
    image: redis:7-alpine
    container_name: accaudit-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - accaudit-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # ACC Audit Tool Web Application
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: accaudit-web
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=AccAuditTool;User Id=sa;Password=************;TrustServerCertificate=true;MultipleActiveResultSets=true
      - Authentication__ClientId=${AUTODESK_CLIENT_ID}
      - Authentication__ClientSecret=${AUTODESK_CLIENT_SECRET}
      - AutodeskAPS__ClientId=${AUTODESK_CLIENT_ID}
      - AutodeskAPS__ClientSecret=${AUTODESK_CLIENT_SECRET}
      - AutodeskAPS__CallbackUrl=https://localhost:8080/signin-oidc
    ports:
      - "8080:80"
    depends_on:
      - sqlserver
      - redis
    networks:
      - accaudit-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local

networks:
  accaudit-network:
    driver: bridge
