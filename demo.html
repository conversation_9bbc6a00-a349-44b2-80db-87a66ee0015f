<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACC Audit Tool - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .demo-container {
            display: flex;
            min-height: 100vh;
            background-color: #f8f9fa;
        }

        .demo-sidebar {
            width: 260px;
            background: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
            color: rgba(255, 255, 255, 0.9);
            flex-shrink: 0;
        }

        .demo-nav-header {
            padding: 1.5rem 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-nav-brand {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .brand-icon {
            font-size: 1.5rem;
            color: #ffd700;
        }

        .demo-nav-content {
            padding: 1rem 0;
        }

        .demo-nav-list {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .demo-nav-item {
            margin: 0;
        }

        .demo-nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .demo-nav-link:hover {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: rgba(255, 255, 255, 0.3);
        }

        .demo-nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.15);
            border-left-color: #ffd700;
            font-weight: 500;
        }

        .nav-icon {
            width: 1.25rem;
            margin-right: 0.75rem;
            text-align: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
        }

        .nav-badge {
            background-color: rgba(255, 255, 255, 0.2);
            color: #fff;
            font-size: 0.7rem;
            padding: 0.2rem 0.4rem;
            border-radius: 0.75rem;
            font-weight: 500;
        }

        .demo-main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }

        .demo-top-bar {
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }

        .demo-page-title {
            color: #495057;
            font-weight: 600;
            margin: 0;
            font-size: 1.5rem;
        }

        .demo-page-title i {
            margin-right: 0.75rem;
            color: #007bff;
        }

        .top-bar-actions {
            display: flex;
            gap: 0.75rem;
            align-items: center;
        }

        .demo-dashboard-content {
            padding: 1.5rem;
            flex: 1;
            overflow-y: auto;
        }

        .demo-metric-card {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1.5rem;
            height: 100%;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
        }

        .demo-metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            flex-shrink: 0;
        }

        .metric-icon i {
            font-size: 1.5rem;
        }

        .metric-content {
            flex: 1;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: #495057;
            margin: 0 0 0.25rem 0;
            line-height: 1;
        }

        .metric-label {
            color: #6c757d;
            margin: 0 0 0.5rem 0;
            font-weight: 500;
        }

        .metric-trend {
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            width: fit-content;
        }

        .trend-up {
            color: #198754;
            background-color: rgba(25, 135, 84, 0.1);
        }

        .trend-down {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }

        .trend-stable {
            color: #6c757d;
            background-color: rgba(108, 117, 125, 0.1);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1rem;
        }

        .card-title i {
            margin-right: 0.5rem;
            color: #007bff;
        }

        .card-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .demo-audit-list, .demo-findings-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .audit-item, .finding-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border: 1px solid #f1f3f4;
            border-radius: 0.375rem;
            transition: background-color 0.2s ease;
        }

        .audit-item:hover, .finding-item:hover {
            background-color: #f8f9fa;
        }

        .audit-status, .finding-severity {
            flex-shrink: 0;
        }

        .audit-details, .finding-details {
            flex: 1;
            min-width: 0;
        }

        .audit-details h6, .finding-details h6 {
            margin: 0 0 0.25rem 0;
            font-weight: 600;
            color: #495057;
        }

        .audit-details p, .finding-details p {
            margin: 0;
            font-size: 0.875rem;
        }

        .audit-findings, .finding-actions {
            flex-shrink: 0;
        }

        .findings-count {
            font-weight: 600;
            color: #495057;
        }

        .progress {
            width: 100px;
            height: 6px;
        }

        /* Mobile responsiveness */
        @media (max-width: 768px) {
            .demo-container {
                flex-direction: column;
            }

            .demo-sidebar {
                width: 100%;
                order: 2;
            }

            .demo-main-content {
                order: 1;
            }

            .demo-top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .demo-dashboard-content {
                padding: 1rem;
            }

            .demo-metric-card {
                margin-bottom: 1rem;
            }

            .audit-item, .finding-item {
                flex-direction: column;
                text-align: center;
                gap: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-sidebar">
            <div class="demo-nav-header">
                <div class="demo-nav-brand">
                    <i class="fas fa-shield-alt brand-icon"></i>
                    <span class="brand-text">ACC Audit Tool</span>
                </div>
            </div>
            
            <nav class="demo-nav-content">
                <ul class="demo-nav-list">
                    <li class="demo-nav-item">
                        <a class="demo-nav-link active" href="#dashboard">
                            <i class="nav-icon fas fa-tachometer-alt"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li class="demo-nav-item">
                        <a class="demo-nav-link" href="#audits">
                            <i class="nav-icon fas fa-clipboard-list"></i>
                            <span class="nav-text">Audits</span>
                            <span class="nav-badge">24</span>
                        </a>
                    </li>
                    <li class="demo-nav-item">
                        <a class="demo-nav-link" href="#rules">
                            <i class="nav-icon fas fa-cogs"></i>
                            <span class="nav-text">Rules</span>
                            <span class="nav-badge">156</span>
                        </a>
                    </li>
                    <li class="demo-nav-item">
                        <a class="demo-nav-link" href="#reports">
                            <i class="nav-icon fas fa-chart-bar"></i>
                            <span class="nav-text">Reports</span>
                        </a>
                    </li>
                    <li class="demo-nav-item">
                        <a class="demo-nav-link" href="#analytics">
                            <i class="nav-icon fas fa-chart-line"></i>
                            <span class="nav-text">Analytics</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>

        <div class="demo-main-content">
            <div class="demo-top-bar">
                <div class="top-bar-left">
                    <h1 class="demo-page-title">
                        <i class="fas fa-tachometer-alt"></i>
                        Dashboard
                    </h1>
                </div>
                <div class="top-bar-right">
                    <div class="top-bar-actions">
                        <button class="btn btn-outline-secondary">
                            <i class="fas fa-bell"></i>
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            New Audit
                        </button>
                    </div>
                </div>
            </div>

            <div class="demo-dashboard-content">
                <!-- Key Metrics -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="demo-metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="metric-content">
                                <h3 class="metric-value">24</h3>
                                <p class="metric-label">Total Audits</p>
                                <div class="metric-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    +12%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="demo-metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-content">
                                <h3 class="metric-value">8</h3>
                                <p class="metric-label">Critical Findings</p>
                                <div class="metric-trend trend-down">
                                    <i class="fas fa-arrow-down"></i>
                                    -3%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="demo-metric-card">
                            <div class="metric-icon bg-success">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="metric-content">
                                <h3 class="metric-value">92%</h3>
                                <p class="metric-label">Compliance Score</p>
                                <div class="metric-trend trend-up">
                                    <i class="fas fa-arrow-up"></i>
                                    +5%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="demo-metric-card">
                            <div class="metric-icon bg-info">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-content">
                                <h3 class="metric-value">156</h3>
                                <p class="metric-label">Active Users</p>
                                <div class="metric-trend trend-stable">
                                    <i class="fas fa-minus"></i>
                                    0%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row -->
                <div class="row mb-4">
                    <div class="col-lg-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-chart-area"></i>
                                    Audit Activity (Last 30 Days)
                                </h5>
                                <div class="card-actions">
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-primary">7D</button>
                                        <button class="btn btn-outline-primary">30D</button>
                                        <button class="btn btn-outline-primary">90D</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <canvas id="demoAuditChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-chart-pie"></i>
                                    Findings by Severity
                                </h5>
                            </div>
                            <div class="card-body">
                                <canvas id="demoSeverityChart" width="300" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity Tables -->
                <div class="row">
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-clock"></i>
                                    Recent Audits
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="demo-audit-list">
                                    <div class="audit-item">
                                        <div class="audit-status">
                                            <span class="badge bg-success">Completed</span>
                                        </div>
                                        <div class="audit-details">
                                            <h6>Monthly Security Audit</h6>
                                            <p class="text-muted">Project Alpha - 2 hours ago</p>
                                        </div>
                                        <div class="audit-findings">
                                            <span class="findings-count">3 findings</span>
                                        </div>
                                    </div>
                                    <div class="audit-item">
                                        <div class="audit-status">
                                            <span class="badge bg-primary">Running</span>
                                        </div>
                                        <div class="audit-details">
                                            <h6>Compliance Check</h6>
                                            <p class="text-muted">Project Beta - Started 30 min ago</p>
                                        </div>
                                        <div class="audit-findings">
                                            <div class="progress">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 65%"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="audit-item">
                                        <div class="audit-status">
                                            <span class="badge bg-danger">Failed</span>
                                        </div>
                                        <div class="audit-details">
                                            <h6>Access Control Review</h6>
                                            <p class="text-muted">Project Gamma - 1 day ago</p>
                                        </div>
                                        <div class="audit-findings">
                                            <span class="text-danger">Error</span>
                                        </div>
                                    </div>
                                    <div class="audit-item">
                                        <div class="audit-status">
                                            <span class="badge bg-warning">Pending</span>
                                        </div>
                                        <div class="audit-details">
                                            <h6>User Permission Audit</h6>
                                            <p class="text-muted">Project Delta - Scheduled for tomorrow</p>
                                        </div>
                                        <div class="audit-findings">
                                            <span class="text-muted">Scheduled</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Critical Findings
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="demo-findings-list">
                                    <div class="finding-item">
                                        <div class="finding-severity">
                                            <span class="badge bg-danger">Critical</span>
                                        </div>
                                        <div class="finding-details">
                                            <h6>Excessive Admin Permissions</h6>
                                            <p class="text-muted">User <EMAIL> has admin access to 15 projects</p>
                                        </div>
                                        <div class="finding-actions">
                                            <button class="btn btn-sm btn-outline-primary">Review</button>
                                        </div>
                                    </div>
                                    <div class="finding-item">
                                        <div class="finding-severity">
                                            <span class="badge bg-danger">Critical</span>
                                        </div>
                                        <div class="finding-details">
                                            <h6>Inactive User with Active Permissions</h6>
                                            <p class="text-muted">User <EMAIL> inactive for 90+ days</p>
                                        </div>
                                        <div class="finding-actions">
                                            <button class="btn btn-sm btn-outline-primary">Review</button>
                                        </div>
                                    </div>
                                    <div class="finding-item">
                                        <div class="finding-severity">
                                            <span class="badge bg-warning">High</span>
                                        </div>
                                        <div class="finding-details">
                                            <h6>Missing Role Segregation</h6>
                                            <p class="text-muted">Project Delta lacks proper role separation</p>
                                        </div>
                                        <div class="finding-actions">
                                            <button class="btn btn-sm btn-outline-primary">Review</button>
                                        </div>
                                    </div>
                                    <div class="finding-item">
                                        <div class="finding-severity">
                                            <span class="badge bg-warning">High</span>
                                        </div>
                                        <div class="finding-details">
                                            <h6>Shared Account Usage</h6>
                                            <p class="text-muted">Multiple users sharing service account credentials</p>
                                        </div>
                                        <div class="finding-actions">
                                            <button class="btn btn-sm btn-outline-primary">Review</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Audit Activity Chart
            const auditCtx = document.getElementById('demoAuditChart');
            if (auditCtx) {
                new Chart(auditCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: ['Jan 1', 'Jan 8', 'Jan 15', 'Jan 22', 'Jan 29', 'Feb 5', 'Feb 12'],
                        datasets: [{
                            label: 'Audits',
                            data: [2, 4, 3, 5, 2, 6, 4],
                            borderColor: 'rgb(13, 110, 253)',
                            backgroundColor: 'rgba(13, 110, 253, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
            }

            // Initialize Severity Chart
            const severityCtx = document.getElementById('demoSeverityChart');
            if (severityCtx) {
                new Chart(severityCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['Critical', 'High', 'Medium', 'Low'],
                        datasets: [{
                            data: [8, 15, 23, 12],
                            backgroundColor: ['#dc3545', '#fd7e14', '#ffc107', '#20c997'],
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { position: 'bottom' }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
