@using AccAuditTool.Application.Services

<div class="top-risks">
    @if (Risks.Any())
    {
        <div class="risks-list">
            @foreach (var risk in Risks.Take(MaxRisks).OrderByDescending(r => r.Impact))
            {
                <div class="risk-item">
                    <div class="risk-header">
                        <div class="risk-info">
                            <h6 class="risk-name">@risk.Name</h6>
                            <span class="risk-category badge bg-@GetCategoryColor(risk.Category)">
                                @risk.Category
                            </span>
                        </div>
                        <div class="risk-impact">
                            <span class="impact-value text-@GetImpactColor(risk.Impact)">
                                @risk.Impact.ToString("F1")
                            </span>
                            <small class="impact-label text-muted">Impact</small>
                        </div>
                    </div>

                    <div class="risk-description">
                        <p>@TruncateDescription(risk.Description, MaxDescriptionLength)</p>
                    </div>

                    <div class="risk-progress">
                        <div class="progress">
                            <div class="progress-bar bg-@GetImpactColor(risk.Impact)" 
                                 role="progressbar" 
                                 style="width: @(risk.Impact * 10)%" 
                                 aria-valuenow="@risk.Impact" 
                                 aria-valuemin="0" 
                                 aria-valuemax="10">
                            </div>
                        </div>
                        <div class="progress-labels">
                            <small class="text-muted">Risk Level: @GetRiskLevel(risk.Impact)</small>
                            <small class="text-muted">@(risk.Impact * 10)%</small>
                        </div>
                    </div>

                    @if (ShowActions)
                    {
                        <div class="risk-actions">
                            <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewRiskDetails(risk)">
                                <i class="fas fa-eye"></i>
                                Details
                            </button>
                            <button class="btn btn-outline-warning btn-sm" @onclick="() => CreateMitigationPlan(risk)">
                                <i class="fas fa-shield-alt"></i>
                                Mitigate
                            </button>
                            @if (ShowAdvancedActions)
                            {
                                <button class="btn btn-outline-info btn-sm" @onclick="() => AnalyzeRisk(risk)">
                                    <i class="fas fa-chart-line"></i>
                                    Analyze
                                </button>
                            }
                        </div>
                    }
                </div>
            }
        </div>

        @if (Risks.Count > MaxRisks)
        {
            <div class="risks-footer">
                <button class="btn btn-link btn-sm" @onclick="ShowAllRisks">
                    <i class="fas fa-ellipsis-h"></i>
                    View all @Risks.Count risk factors
                </button>
            </div>
        }

        @if (ShowSummary)
        {
            <div class="risks-summary">
                <div class="summary-header">
                    <h6>Risk Distribution</h6>
                </div>
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-value text-danger">@Risks.Count(r => r.Impact >= 8)</span>
                        <span class="stat-label">High</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value text-warning">@Risks.Count(r => r.Impact >= 5 && r.Impact < 8)</span>
                        <span class="stat-label">Medium</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value text-info">@Risks.Count(r => r.Impact < 5)</span>
                        <span class="stat-label">Low</span>
                    </div>
                </div>
                <div class="average-risk">
                    <small class="text-muted">
                        Average Risk Impact: 
                        <strong class="text-@GetImpactColor(Risks.Average(r => r.Impact))">
                            @Risks.Average(r => r.Impact).ToString("F1")
                        </strong>
                    </small>
                </div>
            </div>
        }
    }
    else
    {
        <div class="no-risks-message">
            <i class="fas fa-shield-alt text-success"></i>
            <h6 class="text-success">Low Risk Profile</h6>
            <p class="text-muted">No significant risk factors identified.</p>
            @if (ShowActions)
            {
                <button class="btn btn-outline-primary btn-sm" @onclick="RunRiskAssessment">
                    <i class="fas fa-search"></i>
                    Run Risk Assessment
                </button>
            }
        </div>
    }
</div>

@code {
    [Parameter] public List<RiskFactor> Risks { get; set; } = new();
    [Parameter] public int MaxRisks { get; set; } = 5;
    [Parameter] public int MaxDescriptionLength { get; set; } = 100;
    [Parameter] public bool ShowActions { get; set; } = true;
    [Parameter] public bool ShowAdvancedActions { get; set; } = false;
    [Parameter] public bool ShowSummary { get; set; } = true;
    [Parameter] public EventCallback<RiskFactor> OnViewRisk { get; set; }
    [Parameter] public EventCallback<RiskFactor> OnCreateMitigation { get; set; }
    [Parameter] public EventCallback<RiskFactor> OnAnalyzeRisk { get; set; }
    [Parameter] public EventCallback OnShowAll { get; set; }
    [Parameter] public EventCallback OnRunAssessment { get; set; }

    private string GetCategoryColor(string category)
    {
        return category.ToLowerInvariant() switch
        {
            "security" => "danger",
            "compliance" => "warning",
            "access control" => "info",
            "governance" => "primary",
            "performance" => "success",
            "data quality" => "secondary",
            _ => "light"
        };
    }

    private string GetImpactColor(double impact)
    {
        return impact switch
        {
            >= 8 => "danger",
            >= 6 => "warning",
            >= 4 => "info",
            _ => "success"
        };
    }

    private string GetRiskLevel(double impact)
    {
        return impact switch
        {
            >= 8 => "High",
            >= 6 => "Medium",
            >= 4 => "Low",
            _ => "Minimal"
        };
    }

    private string TruncateDescription(string description, int maxLength)
    {
        if (string.IsNullOrEmpty(description) || description.Length <= maxLength)
        {
            return description;
        }

        return description.Substring(0, maxLength) + "...";
    }

    private async Task ViewRiskDetails(RiskFactor risk)
    {
        if (OnViewRisk.HasDelegate)
        {
            await OnViewRisk.InvokeAsync(risk);
        }
    }

    private async Task CreateMitigationPlan(RiskFactor risk)
    {
        if (OnCreateMitigation.HasDelegate)
        {
            await OnCreateMitigation.InvokeAsync(risk);
        }
    }

    private async Task AnalyzeRisk(RiskFactor risk)
    {
        if (OnAnalyzeRisk.HasDelegate)
        {
            await OnAnalyzeRisk.InvokeAsync(risk);
        }
    }

    private async Task ShowAllRisks()
    {
        if (OnShowAll.HasDelegate)
        {
            await OnShowAll.InvokeAsync();
        }
    }

    private async Task RunRiskAssessment()
    {
        if (OnRunAssessment.HasDelegate)
        {
            await OnRunAssessment.InvokeAsync();
        }
    }
}

<style>
    .top-risks {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .risks-list {
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    }

    .risk-item {
        padding: 16px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .risk-item:last-child {
        border-bottom: none;
    }

    .risk-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;
        gap: 12px;
    }

    .risk-info {
        flex: 1;
        min-width: 0;
    }

    .risk-name {
        margin: 0 0 6px 0;
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
        line-height: 1.3;
    }

    .risk-category {
        font-size: 0.7rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .risk-impact {
        text-align: right;
        flex-shrink: 0;
    }

    .impact-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }

    .impact-label {
        font-size: 0.7rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .risk-description {
        margin-bottom: 12px;
    }

    .risk-description p {
        margin: 0;
        font-size: 0.85rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .risk-progress {
        margin-bottom: 12px;
    }

    .progress {
        height: 6px;
        background-color: #e9ecef;
        margin-bottom: 4px;
    }

    .progress-labels {
        display: flex;
        justify-content: space-between;
        font-size: 0.75rem;
    }

    .risk-actions {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
    }

    .risk-actions .btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .risks-footer {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        text-align: center;
        margin-top: 12px;
    }

    .risks-footer .btn-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .risks-footer .btn-link:hover {
        color: #495057;
        text-decoration: underline;
    }

    .risks-summary {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        margin-top: 12px;
    }

    .summary-header {
        margin-bottom: 8px;
    }

    .summary-header h6 {
        margin: 0;
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
    }

    .summary-stats {
        display: flex;
        justify-content: space-around;
        text-align: center;
        margin-bottom: 8px;
    }

    .stat-item {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .stat-value {
        font-size: 1.1rem;
        font-weight: 700;
    }

    .stat-label {
        font-size: 0.7rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .average-risk {
        text-align: center;
        padding-top: 8px;
        border-top: 1px solid #f8f9fa;
    }

    .no-risks-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        gap: 8px;
        text-align: center;
    }

    .no-risks-message i {
        font-size: 2.5rem;
    }

    .no-risks-message h6 {
        margin: 0;
        font-weight: 600;
    }

    .no-risks-message p {
        margin: 0 0 12px 0;
        font-size: 0.875rem;
    }

    /* Custom scrollbar */
    .risks-list::-webkit-scrollbar {
        width: 4px;
    }

    .risks-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .risks-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .risks-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
