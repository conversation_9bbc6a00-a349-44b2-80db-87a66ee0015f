# Ignore build artifacts
**/bin/
**/obj/
**/out/

# Ignore IDE files
.vs/
.vscode/
*.user
*.suo
*.userosscache
*.sln.docstates

# Ignore logs
logs/
*.log

# Ignore temporary files
**/tmp/
**/temp/

# Ignore node modules (if any)
**/node_modules/

# Ignore git
.git/
.gitignore

# Ignore Docker files
Dockerfile*
docker-compose*
.dockerignore

# Ignore test results
**/TestResults/

# Ignore packages
**/packages/

# Ignore documentation
README.md
*.md

# Ignore environment files
.env
.env.local
.env.development
.env.production
