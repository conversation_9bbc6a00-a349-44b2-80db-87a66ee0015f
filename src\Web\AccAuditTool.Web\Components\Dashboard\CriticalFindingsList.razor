@using AccAuditTool.Domain.Entities

<div class="critical-findings">
    @if (Findings.Any())
    {
        <div class="findings-list">
            @foreach (var finding in Findings.Take(MaxFindings))
            {
                <div class="finding-item">
                    <div class="finding-header">
                        <div class="severity-indicator">
                            <span class="severity-badge badge bg-@GetSeverityColor(finding.Severity)">
                                @finding.Severity
                            </span>
                        </div>
                        <div class="finding-age">
                            <small class="text-muted">@GetFindingAge(finding.DetectedAt)</small>
                        </div>
                    </div>
                    
                    <div class="finding-content">
                        <h6 class="finding-title">@finding.Title</h6>
                        <p class="finding-description">@TruncateDescription(finding.Description, MaxDescriptionLength)</p>
                        
                        @if (!string.IsNullOrEmpty(finding.AffectedEntity))
                        {
                            <div class="affected-entity">
                                <i class="fas fa-@GetEntityIcon(finding.AffectedEntity) text-muted"></i>
                                <span class="entity-type">@finding.AffectedEntity:</span>
                                <span class="entity-id">@TruncateEntityId(finding.AffectedEntityId)</span>
                            </div>
                        }
                    </div>

                    <div class="finding-actions">
                        <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewFindingDetails(finding.Id)">
                            <i class="fas fa-eye"></i>
                            View
                        </button>
                        @if (finding.Status == AuditFindingStatus.New)
                        {
                            <button class="btn btn-outline-warning btn-sm" @onclick="() => AcknowledgeFinding(finding.Id)">
                                <i class="fas fa-check"></i>
                                Acknowledge
                            </button>
                        }
                        @if (ShowResolveAction)
                        {
                            <button class="btn btn-outline-success btn-sm" @onclick="() => ResolveFinding(finding.Id)">
                                <i class="fas fa-check-circle"></i>
                                Resolve
                            </button>
                        }
                    </div>

                    @if (ShowRuleInfo)
                    {
                        <div class="rule-info">
                            <small class="text-muted">
                                <i class="fas fa-cog"></i>
                                Rule: @finding.RuleId
                            </small>
                        </div>
                    }
                </div>
            }
        </div>

        @if (Findings.Count > MaxFindings)
        {
            <div class="findings-footer">
                <button class="btn btn-link btn-sm" @onclick="ShowAllFindings">
                    <i class="fas fa-ellipsis-h"></i>
                    View all @Findings.Count critical findings
                </button>
            </div>
        }

        @if (ShowSummary)
        {
            <div class="findings-summary">
                <div class="summary-stats">
                    <div class="stat-item">
                        <span class="stat-value">@Findings.Count(f => f.Status == AuditFindingStatus.New)</span>
                        <span class="stat-label">New</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@Findings.Count(f => f.Status == AuditFindingStatus.InProgress)</span>
                        <span class="stat-label">In Progress</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value">@Findings.Count(f => f.Status == AuditFindingStatus.Resolved)</span>
                        <span class="stat-label">Resolved</span>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="no-findings-message">
            <i class="fas fa-shield-alt text-success"></i>
            <h6 class="text-success">No Critical Findings</h6>
            <p class="text-muted">Great! No critical security issues detected.</p>
        </div>
    }
</div>

@code {
    [Parameter] public List<AuditFinding> Findings { get; set; } = new();
    [Parameter] public int MaxFindings { get; set; } = 5;
    [Parameter] public int MaxDescriptionLength { get; set; } = 120;
    [Parameter] public bool ShowResolveAction { get; set; } = true;
    [Parameter] public bool ShowRuleInfo { get; set; } = true;
    [Parameter] public bool ShowSummary { get; set; } = true;
    [Parameter] public EventCallback<Guid> OnViewFinding { get; set; }
    [Parameter] public EventCallback<Guid> OnAcknowledgeFinding { get; set; }
    [Parameter] public EventCallback<Guid> OnResolveFinding { get; set; }
    [Parameter] public EventCallback OnShowAll { get; set; }

    private string GetSeverityColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "danger",
            AuditSeverity.High => "warning",
            AuditSeverity.Medium => "info",
            AuditSeverity.Low => "secondary",
            AuditSeverity.Info => "light",
            _ => "secondary"
        };
    }

    private string GetFindingAge(DateTime detectedAt)
    {
        var timeSpan = DateTime.UtcNow - detectedAt;
        
        return timeSpan switch
        {
            { TotalMinutes: < 60 } => $"{(int)timeSpan.TotalMinutes}m ago",
            { TotalHours: < 24 } => $"{(int)timeSpan.TotalHours}h ago",
            { TotalDays: < 7 } => $"{(int)timeSpan.TotalDays}d ago",
            { TotalDays: < 30 } => $"{(int)(timeSpan.TotalDays / 7)}w ago",
            _ => detectedAt.ToString("MMM dd, yyyy")
        };
    }

    private string GetEntityIcon(string? entityType)
    {
        return entityType?.ToLowerInvariant() switch
        {
            "user" => "user",
            "project" => "folder",
            "company" => "building",
            "role" => "user-tag",
            "permission" => "key",
            _ => "cube"
        };
    }

    private string TruncateDescription(string description, int maxLength)
    {
        if (string.IsNullOrEmpty(description) || description.Length <= maxLength)
        {
            return description;
        }

        return description.Substring(0, maxLength) + "...";
    }

    private string TruncateEntityId(string? entityId)
    {
        if (string.IsNullOrEmpty(entityId))
        {
            return "Unknown";
        }

        // If it looks like a GUID, show first 8 characters
        if (Guid.TryParse(entityId, out _))
        {
            return entityId.Substring(0, 8) + "...";
        }

        // If it's an email, show it as is (up to reasonable length)
        if (entityId.Contains("@") && entityId.Length <= 30)
        {
            return entityId;
        }

        // For other strings, truncate if too long
        return entityId.Length > 25 ? entityId.Substring(0, 25) + "..." : entityId;
    }

    private async Task ViewFindingDetails(Guid findingId)
    {
        if (OnViewFinding.HasDelegate)
        {
            await OnViewFinding.InvokeAsync(findingId);
        }
    }

    private async Task AcknowledgeFinding(Guid findingId)
    {
        if (OnAcknowledgeFinding.HasDelegate)
        {
            await OnAcknowledgeFinding.InvokeAsync(findingId);
        }
    }

    private async Task ResolveFinding(Guid findingId)
    {
        if (OnResolveFinding.HasDelegate)
        {
            await OnResolveFinding.InvokeAsync(findingId);
        }
    }

    private async Task ShowAllFindings()
    {
        if (OnShowAll.HasDelegate)
        {
            await OnShowAll.InvokeAsync();
        }
    }
}

<style>
    .critical-findings {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .findings-list {
        flex: 1;
        overflow-y: auto;
        max-height: 400px;
    }

    .finding-item {
        padding: 16px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .finding-item:last-child {
        border-bottom: none;
    }

    .finding-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .severity-indicator {
        flex-shrink: 0;
    }

    .severity-badge {
        font-size: 0.7rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .finding-age {
        flex-shrink: 0;
    }

    .finding-content {
        margin-bottom: 12px;
    }

    .finding-title {
        margin: 0 0 6px 0;
        font-size: 0.9rem;
        font-weight: 600;
        color: #495057;
        line-height: 1.3;
    }

    .finding-description {
        margin: 0 0 8px 0;
        font-size: 0.85rem;
        color: #6c757d;
        line-height: 1.4;
    }

    .affected-entity {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .entity-type {
        font-weight: 500;
    }

    .entity-id {
        font-family: 'Courier New', monospace;
        background-color: #f8f9fa;
        padding: 2px 4px;
        border-radius: 3px;
        font-size: 0.75rem;
    }

    .finding-actions {
        display: flex;
        gap: 6px;
        flex-wrap: wrap;
        margin-bottom: 8px;
    }

    .finding-actions .btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .rule-info {
        font-size: 0.75rem;
    }

    .rule-info i {
        margin-right: 4px;
    }

    .findings-footer {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        text-align: center;
        margin-top: 12px;
    }

    .findings-footer .btn-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .findings-footer .btn-link:hover {
        color: #495057;
        text-decoration: underline;
    }

    .findings-summary {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        margin-top: 12px;
    }

    .summary-stats {
        display: flex;
        justify-content: space-around;
        text-align: center;
    }

    .stat-item {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .stat-value {
        font-size: 1.2rem;
        font-weight: 700;
        color: #495057;
    }

    .stat-label {
        font-size: 0.75rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .no-findings-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        gap: 8px;
        text-align: center;
    }

    .no-findings-message i {
        font-size: 2.5rem;
    }

    .no-findings-message h6 {
        margin: 0;
        font-weight: 600;
    }

    .no-findings-message p {
        margin: 0;
        font-size: 0.875rem;
    }

    /* Custom scrollbar */
    .findings-list::-webkit-scrollbar {
        width: 4px;
    }

    .findings-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .findings-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .findings-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
