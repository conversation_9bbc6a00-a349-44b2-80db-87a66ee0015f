{"ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=AccAuditTool;Trusted_Connection=true;MultipleActiveResultSets=true"}, "Authentication": {"Authority": "https://developer.api.autodesk.com", "ClientId": "YOUR_CLIENT_ID", "ClientSecret": "YOUR_CLIENT_SECRET"}, "AutodeskAPS": {"BaseUrl": "https://developer.api.autodesk.com", "ClientId": "YOUR_CLIENT_ID", "ClientSecret": "YOUR_CLIENT_SECRET", "CallbackUrl": "https://localhost:7000/auth/callback", "Scopes": ["data:read", "account:read"], "RateLimit": {"RequestsPerMinute": 100, "BurstCapacity": 20, "EnableAdaptiveRateLimit": true}, "Retry": {"MaxRetryAttempts": 3, "BaseDelayMs": 1000, "MaxDelayMs": 30000, "UseExponentialBackoff": true, "UseJitter": true}, "CircuitBreaker": {"FailureThreshold": 5, "DurationOfBreakInSeconds": 30, "MinimumThroughput": 10}, "Timeout": {"RequestTimeoutSeconds": 30, "OperationTimeoutSeconds": 300}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*"}