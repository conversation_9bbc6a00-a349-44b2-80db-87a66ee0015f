@page "/audits"
@using AccAuditTool.Domain.Entities
@using AccAuditTool.Application.Services
@inject IRuleExecutionEngine RuleExecutionEngine
@inject IUnitOfWork UnitOfWork
@inject IJSRuntime JSRuntime

<PageTitle>Audit Management - ACC Audit Tool</PageTitle>

<div class="audit-management">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                Audit Management
            </h1>
            <p class="page-description">Manage and monitor audit runs, view execution status, and configure audit parameters.</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-outline-secondary" @onclick="RefreshAudits">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <button class="btn btn-primary" @onclick="CreateNewAudit">
                <i class="fas fa-plus"></i>
                New Audit
            </button>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="audit-filters">
        <div class="row">
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Status</label>
                    <select class="form-select" @bind="SelectedStatus" @onchange="ApplyFilters">
                        <option value="">All Statuses</option>
                        <option value="@AuditRunStatus.Pending">Pending</option>
                        <option value="@AuditRunStatus.Running">Running</option>
                        <option value="@AuditRunStatus.Completed">Completed</option>
                        <option value="@AuditRunStatus.Failed">Failed</option>
                        <option value="@AuditRunStatus.Cancelled">Cancelled</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Date Range</label>
                    <select class="form-select" @bind="SelectedDateRange" @onchange="ApplyFilters">
                        <option value="all">All Time</option>
                        <option value="today">Today</option>
                        <option value="week">This Week</option>
                        <option value="month">This Month</option>
                        <option value="quarter">This Quarter</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Audit Type</label>
                    <select class="form-select" @bind="SelectedAuditType" @onchange="ApplyFilters">
                        <option value="">All Types</option>
                        <option value="manual">Manual</option>
                        <option value="scheduled">Scheduled</option>
                        <option value="triggered">Triggered</option>
                    </select>
                </div>
            </div>
            <div class="col-md-3">
                <div class="filter-group">
                    <label class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text"
                               class="form-control"
                               placeholder="Search audits..."
                               @bind="SearchQuery"
                               @onkeypress="HandleSearchKeyPress" />
                        <button class="btn btn-outline-secondary" type="button" @onclick="ApplyFilters">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Statistics -->
    <div class="audit-stats">
        <div class="row">
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-primary">@TotalAudits</div>
                    <div class="stat-label">Total</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-warning">@RunningAudits</div>
                    <div class="stat-label">Running</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-success">@CompletedAudits</div>
                    <div class="stat-label">Completed</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-danger">@FailedAudits</div>
                    <div class="stat-label">Failed</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-info">@ScheduledAudits</div>
                    <div class="stat-label">Scheduled</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="stat-card">
                    <div class="stat-value text-secondary">@CancelledAudits</div>
                    <div class="stat-label">Cancelled</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit List -->
    <div class="audit-list-container">
        @if (IsLoading)
        {
            <div class="loading-container">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading audits...</span>
                </div>
                <p class="loading-text">Loading audits...</p>
            </div>
        }
        else if (FilteredAudits.Any())
        {
            <div class="audit-list">
                <div class="list-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5>Audit Runs (@FilteredAudits.Count)</h5>
                        </div>
                        <div class="col-auto">
                            <div class="view-options">
                                <div class="btn-group" role="group">
                                    <button class="btn @(ViewMode == "list" ? "btn-primary" : "btn-outline-primary") btn-sm"
                                            @onclick="() => SetViewMode(\"list\")">
                                        <i class="fas fa-list"></i>
                                    </button>
                                    <button class="btn @(ViewMode == "grid" ? "btn-primary" : "btn-outline-primary") btn-sm"
                                            @onclick="() => SetViewMode(\"grid\")">
                                        <i class="fas fa-th"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if (ViewMode == "list")
                {
                    <AuditListView Audits="@FilteredAudits"
                                   OnViewAudit="ViewAuditDetails"
                                   OnEditAudit="EditAudit"
                                   OnDeleteAudit="DeleteAudit"
                                   OnCancelAudit="CancelAudit"
                                   OnRestartAudit="RestartAudit" />
                }
                else
                {
                    <AuditGridView Audits="@FilteredAudits"
                                   OnViewAudit="ViewAuditDetails"
                                   OnEditAudit="EditAudit"
                                   OnDeleteAudit="DeleteAudit"
                                   OnCancelAudit="CancelAudit"
                                   OnRestartAudit="RestartAudit" />
                }
            </div>

            <!-- Pagination -->
            <div class="pagination-container">
                <nav aria-label="Audit pagination">
                    <ul class="pagination justify-content-center">
                        <li class="page-item @(CurrentPage == 1 ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(CurrentPage - 1)">
                                <i class="fas fa-chevron-left"></i>
                            </button>
                        </li>
                        @for (int i = Math.Max(1, CurrentPage - 2); i <= Math.Min(TotalPages, CurrentPage + 2); i++)
                        {
                            <li class="page-item @(i == CurrentPage ? "active" : "")">
                                <button class="page-link" @onclick="() => GoToPage(i)">@i</button>
                            </li>
                        }
                        <li class="page-item @(CurrentPage == TotalPages ? "disabled" : "")">
                            <button class="page-link" @onclick="() => GoToPage(CurrentPage + 1)">
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </li>
                    </ul>
                </nav>
                <div class="pagination-info">
                    Showing @((CurrentPage - 1) * PageSize + 1) to @Math.Min(CurrentPage * PageSize, FilteredAudits.Count) of @FilteredAudits.Count audits
                </div>
            </div>
        }
        else
        {
            <div class="no-audits-message">
                <i class="fas fa-clipboard-list text-muted"></i>
                <h5>No Audits Found</h5>
                <p class="text-muted">
                    @if (HasActiveFilters)
                    {
                        <span>No audits match your current filters. Try adjusting your search criteria.</span>
                    }
                    else
                    {
                        <span>You haven't created any audits yet. Get started by creating your first audit.</span>
                    }
                </p>
                @if (!HasActiveFilters)
                {
                    <button class="btn btn-primary" @onclick="CreateNewAudit">
                        <i class="fas fa-plus"></i>
                        Create First Audit
                    </button>
                }
                else
                {
                    <button class="btn btn-outline-secondary" @onclick="ClearFilters">
                        <i class="fas fa-times"></i>
                        Clear Filters
                    </button>
                }
            </div>
        }
    </div>
</div>

@code {
    private bool IsLoading = true;
    private string ViewMode = "list";
    private string SelectedStatus = string.Empty;
    private string SelectedDateRange = "all";
    private string SelectedAuditType = string.Empty;
    private string SearchQuery = string.Empty;
    private int CurrentPage = 1;
    private int PageSize = 10;

    // Sample data - in real implementation, this would come from services
    private List<AuditRun> AllAudits = new();
    private List<AuditRun> FilteredAudits = new();

    // Statistics
    private int TotalAudits => AllAudits.Count;
    private int RunningAudits => AllAudits.Count(a => a.Status == AuditRunStatus.Running);
    private int CompletedAudits => AllAudits.Count(a => a.Status == AuditRunStatus.Completed);
    private int FailedAudits => AllAudits.Count(a => a.Status == AuditRunStatus.Failed);
    private int ScheduledAudits => AllAudits.Count(a => a.Status == AuditRunStatus.Pending);
    private int CancelledAudits => AllAudits.Count(a => a.Status == AuditRunStatus.Cancelled);

    private int TotalPages => (int)Math.Ceiling((double)FilteredAudits.Count / PageSize);
    private bool HasActiveFilters => !string.IsNullOrEmpty(SelectedStatus) ||
                                   SelectedDateRange != "all" ||
                                   !string.IsNullOrEmpty(SelectedAuditType) ||
                                   !string.IsNullOrEmpty(SearchQuery);

    protected override async Task OnInitializedAsync()
    {
        await LoadAudits();
    }

    private async Task LoadAudits()
    {
        IsLoading = true;
        StateHasChanged();

        try
        {
            // In real implementation, load from service
            AllAudits = GenerateSampleAudits();
            await ApplyFilters();
        }
        catch (Exception ex)
        {
            // Handle error
            Console.WriteLine($"Error loading audits: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task ApplyFilters()
    {
        var filtered = AllAudits.AsEnumerable();

        // Status filter
        if (!string.IsNullOrEmpty(SelectedStatus) && Enum.TryParse<AuditRunStatus>(SelectedStatus, out var status))
        {
            filtered = filtered.Where(a => a.Status == status);
        }

        // Date range filter
        if (SelectedDateRange != "all")
        {
            var cutoffDate = SelectedDateRange switch
            {
                "today" => DateTime.Today,
                "week" => DateTime.Today.AddDays(-7),
                "month" => DateTime.Today.AddMonths(-1),
                "quarter" => DateTime.Today.AddMonths(-3),
                _ => DateTime.MinValue
            };

            if (cutoffDate != DateTime.MinValue)
            {
                filtered = filtered.Where(a => a.StartedAt >= cutoffDate);
            }
        }

        // Audit type filter
        if (!string.IsNullOrEmpty(SelectedAuditType))
        {
            filtered = filtered.Where(a => a.AuditType?.ToLowerInvariant() == SelectedAuditType.ToLowerInvariant());
        }

        // Search filter
        if (!string.IsNullOrEmpty(SearchQuery))
        {
            var query = SearchQuery.ToLowerInvariant();
            filtered = filtered.Where(a =>
                a.Name.ToLowerInvariant().Contains(query) ||
                a.Description?.ToLowerInvariant().Contains(query) == true ||
                a.AccountId.ToLowerInvariant().Contains(query));
        }

        FilteredAudits = filtered.OrderByDescending(a => a.StartedAt).ToList();
        CurrentPage = 1; // Reset to first page when filters change
        StateHasChanged();
    }

    private async Task RefreshAudits()
    {
        await LoadAudits();
    }

    private async Task CreateNewAudit()
    {
        // Navigate to audit creation page
        // Navigation.NavigateTo("/audits/create");
    }

    private async Task HandleSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await ApplyFilters();
        }
    }

    private void SetViewMode(string mode)
    {
        ViewMode = mode;
        StateHasChanged();
    }

    private async Task GoToPage(int page)
    {
        if (page >= 1 && page <= TotalPages)
        {
            CurrentPage = page;
            StateHasChanged();
        }
    }

    private async Task ClearFilters()
    {
        SelectedStatus = string.Empty;
        SelectedDateRange = "all";
        SelectedAuditType = string.Empty;
        SearchQuery = string.Empty;
        await ApplyFilters();
    }

    private async Task ViewAuditDetails(Guid auditId)
    {
        // Navigate to audit details page
        // Navigation.NavigateTo($"/audits/{auditId}");
    }

    private async Task EditAudit(Guid auditId)
    {
        // Navigate to audit edit page
        // Navigation.NavigateTo($"/audits/{auditId}/edit");
    }

    private async Task DeleteAudit(Guid auditId)
    {
        // Show confirmation dialog and delete audit
        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this audit?");
        if (confirmed)
        {
            // Delete audit logic
            AllAudits.RemoveAll(a => a.Id == auditId);
            await ApplyFilters();
        }
    }

    private async Task CancelAudit(Guid auditId)
    {
        // Cancel running audit
        var audit = AllAudits.FirstOrDefault(a => a.Id == auditId);
        if (audit != null && audit.Status == AuditRunStatus.Running)
        {
            await RuleExecutionEngine.CancelExecutionAsync(auditId);
            audit.Status = AuditRunStatus.Cancelled;
            await ApplyFilters();
        }
    }

    private async Task RestartAudit(Guid auditId)
    {
        // Restart failed or cancelled audit
        var audit = AllAudits.FirstOrDefault(a => a.Id == auditId);
        if (audit != null && (audit.Status == AuditRunStatus.Failed || audit.Status == AuditRunStatus.Cancelled))
        {
            audit.Status = AuditRunStatus.Pending;
            audit.StartedAt = DateTime.UtcNow;
            audit.CompletedAt = null;
            audit.ErrorMessage = null;
            await ApplyFilters();

            // Start audit execution
            // await RuleExecutionEngine.ExecuteRulesAsync(auditId);
        }
    }

    private List<AuditRun> GenerateSampleAudits()
    {
        var audits = new List<AuditRun>();
        var random = new Random();
        var statuses = Enum.GetValues<AuditRunStatus>();
        var auditTypes = new[] { "manual", "scheduled", "triggered" };

        for (int i = 0; i < 25; i++)
        {
            var startDate = DateTime.UtcNow.AddDays(-random.Next(0, 90));
            var status = statuses[random.Next(statuses.Length)];

            audits.Add(new AuditRun
            {
                Id = Guid.NewGuid(),
                Name = $"Audit Run {i + 1}",
                Description = $"Sample audit run {i + 1} for testing purposes",
                AccountId = $"account-{random.Next(1, 5)}",
                ProjectIds = new List<string> { $"project-{random.Next(1, 10)}" },
                Status = status,
                StartedAt = startDate,
                CompletedAt = status == AuditRunStatus.Completed ? startDate.AddMinutes(random.Next(5, 120)) : null,
                TotalFindings = status == AuditRunStatus.Completed ? random.Next(0, 50) : 0,
                ErrorMessage = status == AuditRunStatus.Failed ? "Sample error message" : null,
                AuditType = auditTypes[random.Next(auditTypes.Length)],
                Scope = "Full system audit"
            });
        }

        return audits;
    }
}

<style>
    .audit-management {
        padding: 0;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .header-content {
        flex: 1;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        margin-right: 0.75rem;
        color: #007bff;
    }

    .page-description {
        color: #6c757d;
        margin: 0;
        font-size: 0.95rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .audit-filters {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .filter-group {
        margin-bottom: 0;
    }

    .filter-group .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .audit-stats {
        margin-bottom: 2rem;
    }

    .stat-card {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 0.25rem;
    }

    .stat-label {
        font-size: 0.875rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-weight: 500;
    }

    .audit-list-container {
        background-color: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .list-header {
        background-color: #f8f9fa;
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }

    .list-header h5 {
        margin: 0;
        color: #495057;
        font-weight: 600;
    }

    .view-options .btn-group .btn {
        border-radius: 0.25rem;
    }

    .view-options .btn-group .btn:first-child {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .view-options .btn-group .btn:last-child {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 3rem;
        gap: 1rem;
    }

    .loading-text {
        color: #6c757d;
        margin: 0;
    }

    .no-audits-message {
        text-align: center;
        padding: 3rem 2rem;
    }

    .no-audits-message i {
        font-size: 4rem;
        margin-bottom: 1rem;
    }

    .no-audits-message h5 {
        color: #495057;
        margin-bottom: 0.75rem;
    }

    .no-audits-message p {
        margin-bottom: 1.5rem;
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    .pagination-container {
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
    }

    .pagination-info {
        color: #6c757d;
        font-size: 0.875rem;
    }

    .pagination .page-link {
        border: none;
        color: #6c757d;
        padding: 0.5rem 0.75rem;
    }

    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
        color: #fff;
    }

    .pagination .page-link:hover {
        background-color: #e9ecef;
        color: #495057;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .header-actions {
            justify-content: stretch;
        }

        .header-actions .btn {
            flex: 1;
        }

        .audit-filters {
            padding: 1rem;
        }

        .stat-card {
            margin-bottom: 1rem;
        }

        .pagination-container {
            flex-direction: column;
            text-align: center;
        }
    }
</style>