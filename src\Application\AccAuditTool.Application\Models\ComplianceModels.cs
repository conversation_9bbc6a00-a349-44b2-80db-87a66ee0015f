using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Represents a compliance framework
/// </summary>
public class ComplianceFramework
{
    /// <summary>
    /// Unique identifier for the framework
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Name of the framework
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the framework
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Version of the framework
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Controls defined in this framework
    /// </summary>
    public List<ComplianceControl> Controls { get; set; } = new();

    /// <summary>
    /// Rule IDs associated with this framework
    /// </summary>
    public List<string> RuleIds { get; set; } = new();

    /// <summary>
    /// Whether the framework is currently enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Represents a compliance control within a framework
/// </summary>
public class ComplianceControl
{
    /// <summary>
    /// Unique identifier for the control
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Name of the control
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Description of the control
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Category of the control
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Rule IDs that validate this control
    /// </summary>
    public List<string> RuleIds { get; set; } = new();

    /// <summary>
    /// Risk level if control fails
    /// </summary>
    public string RiskLevel { get; set; } = "Medium";

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Assessment of compliance for a framework
/// </summary>
public class ComplianceAssessment
{
    /// <summary>
    /// Framework ID
    /// </summary>
    public string FrameworkId { get; set; } = string.Empty;

    /// <summary>
    /// Framework name
    /// </summary>
    public string FrameworkName { get; set; } = string.Empty;

    /// <summary>
    /// Audit run ID
    /// </summary>
    public Guid AuditRunId { get; set; }

    /// <summary>
    /// When the assessment was performed
    /// </summary>
    public DateTime AssessmentDate { get; set; }

    /// <summary>
    /// Overall compliance status
    /// </summary>
    public ComplianceStatus OverallStatus { get; set; }

    /// <summary>
    /// Compliance percentage (0-100)
    /// </summary>
    public double CompliancePercentage { get; set; }

    /// <summary>
    /// Total number of controls
    /// </summary>
    public int TotalControls { get; set; }

    /// <summary>
    /// Number of compliant controls
    /// </summary>
    public int CompliantControls { get; set; }

    /// <summary>
    /// Number of partially compliant controls
    /// </summary>
    public int PartiallyCompliantControls { get; set; }

    /// <summary>
    /// Number of non-compliant controls
    /// </summary>
    public int NonCompliantControls { get; set; }

    /// <summary>
    /// Total number of findings
    /// </summary>
    public int TotalFindings { get; set; }

    /// <summary>
    /// Assessment for each control
    /// </summary>
    public Dictionary<string, ControlAssessment> ControlAssessments { get; set; } = new();

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Assessment of a specific control
/// </summary>
public class ControlAssessment
{
    /// <summary>
    /// Control ID
    /// </summary>
    public string ControlId { get; set; } = string.Empty;

    /// <summary>
    /// Control name
    /// </summary>
    public string ControlName { get; set; } = string.Empty;

    /// <summary>
    /// Control description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Compliance status of the control
    /// </summary>
    public ComplianceStatus Status { get; set; }

    /// <summary>
    /// Risk score for the control (0-100)
    /// </summary>
    public double RiskScore { get; set; }

    /// <summary>
    /// Total number of findings for this control
    /// </summary>
    public int TotalFindings { get; set; }

    /// <summary>
    /// Number of critical findings
    /// </summary>
    public int CriticalFindings { get; set; }

    /// <summary>
    /// Number of high severity findings
    /// </summary>
    public int HighFindings { get; set; }

    /// <summary>
    /// Number of medium severity findings
    /// </summary>
    public int MediumFindings { get; set; }

    /// <summary>
    /// Number of low severity findings
    /// </summary>
    public int LowFindings { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Compliance status enumeration
/// </summary>
public enum ComplianceStatus
{
    Compliant = 0,
    PartiallyCompliant = 1,
    NonCompliant = 2,
    NotAssessed = 3
}

/// <summary>
/// Compliance report
/// </summary>
public class ComplianceReport
{
    /// <summary>
    /// Framework ID
    /// </summary>
    public string FrameworkId { get; set; } = string.Empty;

    /// <summary>
    /// Framework name
    /// </summary>
    public string FrameworkName { get; set; } = string.Empty;

    /// <summary>
    /// Audit run ID
    /// </summary>
    public Guid AuditRunId { get; set; }

    /// <summary>
    /// When the report was generated
    /// </summary>
    public DateTime GeneratedAt { get; set; }

    /// <summary>
    /// Compliance assessment
    /// </summary>
    public ComplianceAssessment Assessment { get; set; } = new();

    /// <summary>
    /// Executive summary
    /// </summary>
    public string ExecutiveSummary { get; set; } = string.Empty;

    /// <summary>
    /// Audit scope
    /// </summary>
    public string AuditScope { get; set; } = string.Empty;

    /// <summary>
    /// Audit period
    /// </summary>
    public AuditPeriod AuditPeriod { get; set; } = new();

    /// <summary>
    /// Findings mapped to controls
    /// </summary>
    public Dictionary<string, List<AuditFinding>> FindingsByControl { get; set; } = new();

    /// <summary>
    /// Recommendations for improving compliance
    /// </summary>
    public List<ComplianceRecommendation> Recommendations { get; set; } = new();

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Audit period information
/// </summary>
public class AuditPeriod
{
    /// <summary>
    /// Start date of the audit period
    /// </summary>
    public DateTime StartDate { get; set; }

    /// <summary>
    /// End date of the audit period
    /// </summary>
    public DateTime EndDate { get; set; }

    /// <summary>
    /// Duration of the audit period
    /// </summary>
    public TimeSpan Duration => EndDate - StartDate;
}

/// <summary>
/// Compliance recommendation
/// </summary>
public class ComplianceRecommendation
{
    /// <summary>
    /// Priority of the recommendation
    /// </summary>
    public RecommendationPriority Priority { get; set; }

    /// <summary>
    /// Control ID this recommendation relates to
    /// </summary>
    public string? ControlId { get; set; }

    /// <summary>
    /// Title of the recommendation
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Expected impact of implementing the recommendation
    /// </summary>
    public string ExpectedImpact { get; set; } = string.Empty;

    /// <summary>
    /// Estimated effort to implement
    /// </summary>
    public string EstimatedEffort { get; set; } = string.Empty;

    /// <summary>
    /// Target completion date
    /// </summary>
    public DateTime? TargetDate { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Compliance dashboard data
/// </summary>
public class ComplianceDashboard
{
    /// <summary>
    /// Overall compliance summary
    /// </summary>
    public ComplianceSummary Summary { get; set; } = new();

    /// <summary>
    /// Compliance by framework
    /// </summary>
    public Dictionary<string, ComplianceAssessment> FrameworkCompliance { get; set; } = new();

    /// <summary>
    /// Compliance trends over time
    /// </summary>
    public List<ComplianceTrendPoint> Trends { get; set; } = new();

    /// <summary>
    /// Top compliance risks
    /// </summary>
    public List<ComplianceRisk> TopRisks { get; set; } = new();

    /// <summary>
    /// Recent compliance activities
    /// </summary>
    public List<ComplianceActivity> RecentActivities { get; set; } = new();
}

/// <summary>
/// Overall compliance summary
/// </summary>
public class ComplianceSummary
{
    /// <summary>
    /// Total number of frameworks
    /// </summary>
    public int TotalFrameworks { get; set; }

    /// <summary>
    /// Number of active frameworks
    /// </summary>
    public int ActiveFrameworks { get; set; }

    /// <summary>
    /// Average compliance percentage across all frameworks
    /// </summary>
    public double AverageCompliance { get; set; }

    /// <summary>
    /// Total number of controls across all frameworks
    /// </summary>
    public int TotalControls { get; set; }

    /// <summary>
    /// Number of compliant controls
    /// </summary>
    public int CompliantControls { get; set; }

    /// <summary>
    /// Total number of findings
    /// </summary>
    public int TotalFindings { get; set; }

    /// <summary>
    /// Number of critical findings
    /// </summary>
    public int CriticalFindings { get; set; }
}

/// <summary>
/// Point in compliance trend
/// </summary>
public class ComplianceTrendPoint
{
    /// <summary>
    /// Date of the data point
    /// </summary>
    public DateTime Date { get; set; }

    /// <summary>
    /// Framework ID
    /// </summary>
    public string FrameworkId { get; set; } = string.Empty;

    /// <summary>
    /// Compliance percentage at this point
    /// </summary>
    public double CompliancePercentage { get; set; }

    /// <summary>
    /// Number of findings at this point
    /// </summary>
    public int FindingCount { get; set; }
}

/// <summary>
/// Compliance risk
/// </summary>
public class ComplianceRisk
{
    /// <summary>
    /// Risk ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Risk title
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Risk description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Framework ID
    /// </summary>
    public string FrameworkId { get; set; } = string.Empty;

    /// <summary>
    /// Control ID
    /// </summary>
    public string ControlId { get; set; } = string.Empty;

    /// <summary>
    /// Risk score (0-100)
    /// </summary>
    public double RiskScore { get; set; }

    /// <summary>
    /// Risk level
    /// </summary>
    public string RiskLevel { get; set; } = string.Empty;

    /// <summary>
    /// Impact if risk materializes
    /// </summary>
    public string Impact { get; set; } = string.Empty;
}

/// <summary>
/// Compliance activity
/// </summary>
public class ComplianceActivity
{
    /// <summary>
    /// Activity ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Activity type
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Activity description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// When the activity occurred
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// User who performed the activity
    /// </summary>
    public string User { get; set; } = string.Empty;

    /// <summary>
    /// Framework ID (if applicable)
    /// </summary>
    public string? FrameworkId { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object> Metadata { get; set; } = new();
}
