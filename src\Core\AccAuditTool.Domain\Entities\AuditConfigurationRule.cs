namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents the relationship between audit configurations and rules
/// </summary>
public class AuditConfigurationRule : BaseEntity
{
    /// <summary>
    /// Audit configuration this rule belongs to
    /// </summary>
    public Guid AuditConfigurationId { get; set; }
    public AuditConfiguration AuditConfiguration { get; set; } = null!;

    /// <summary>
    /// Audit rule included in the configuration
    /// </summary>
    public Guid AuditRuleId { get; set; }
    public AuditRule AuditRule { get; set; } = null!;

    /// <summary>
    /// Whether this rule is enabled in this configuration
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Override severity for this rule in this configuration
    /// </summary>
    public FindingSeverity? OverrideSeverity { get; set; }

    /// <summary>
    /// Rule-specific configuration overrides as JSON
    /// </summary>
    public string? ConfigurationOverrides { get; set; }

    /// <summary>
    /// Order in which this rule should be executed
    /// </summary>
    public int ExecutionOrder { get; set; } = 0;
}
