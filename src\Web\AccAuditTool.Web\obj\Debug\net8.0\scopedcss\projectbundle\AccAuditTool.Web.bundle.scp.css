/* _content/AccAuditTool.Web/Shared/MainLayout.razor.rz.scp.css */
.page[b-cuxfxweqrc] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-cuxfxweqrc] {
    flex: 1;
}

.sidebar[b-cuxfxweqrc] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-cuxfxweqrc] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-cuxfxweqrc]  a, .top-row .btn-link[b-cuxfxweqrc] {
        white-space: nowrap;
        margin-left: 1.5rem;
    }

    .top-row a:first-child[b-cuxfxweqrc] {
        overflow: hidden;
        text-overflow: ellipsis;
    }

@media (max-width: 640.98px) {
    .top-row:not(.auth)[b-cuxfxweqrc] {
        display: none;
    }

    .top-row.auth[b-cuxfxweqrc] {
        justify-content: space-between;
    }

    .top-row a[b-cuxfxweqrc], .top-row .btn-link[b-cuxfxweqrc] {
        margin-left: 0;
    }
}

@media (min-width: 641px) {
    .page[b-cuxfxweqrc] {
        flex-direction: row;
    }

    .sidebar[b-cuxfxweqrc] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-cuxfxweqrc] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

    .top-row[b-cuxfxweqrc], article[b-cuxfxweqrc] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* _content/AccAuditTool.Web/Shared/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-whc1u1eo6p] {
    background-color: rgba(255, 255, 255, 0.1);
}

.top-row[b-whc1u1eo6p] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-whc1u1eo6p] {
    font-size: 1.1rem;
}

.oi[b-whc1u1eo6p] {
    width: 2rem;
    font-size: 1.1rem;
    vertical-align: text-top;
    top: -2px;
}

.nav-item[b-whc1u1eo6p] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-whc1u1eo6p] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-whc1u1eo6p] {
        padding-bottom: 1rem;
    }

    .nav-item[b-whc1u1eo6p]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

.nav-item[b-whc1u1eo6p]  a.active {
    background-color: rgba(255,255,255,0.25);
    color: white;
}

.nav-item[b-whc1u1eo6p]  a:hover {
    background-color: rgba(255,255,255,0.1);
    color: white;
}

@media (min-width: 641px) {
    .navbar-toggler[b-whc1u1eo6p] {
        display: none;
    }

    .collapse[b-whc1u1eo6p] {
        /* Never collapse the sidebar for wide screens */
        display: block;
    }
}
