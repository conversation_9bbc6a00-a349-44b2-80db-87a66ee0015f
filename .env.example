# Autodesk APS (formerly Forge) Configuration
# Get these values from https://aps.autodesk.com/myapps/
AUTODESK_CLIENT_ID=your_client_id_here
AUTODESK_CLIENT_SECRET=your_client_secret_here

# Database Configuration
DB_CONNECTION_STRING=Server=(localdb)\mssqllocaldb;Database=AccAuditTool;Trusted_Connection=true;MultipleActiveResultSets=true

# Docker Database Configuration (for docker-compose)
DB_PASSWORD=AccAudit123!

# Application Configuration
ASPNETCORE_ENVIRONMENT=Development
ASPNETCORE_URLS=https://localhost:7000;http://localhost:5000

# Logging Configuration
LOG_LEVEL=Information
LOG_FILE_PATH=logs/accaudit-.txt

# Redis Configuration (if using distributed caching)
REDIS_CONNECTION_STRING=localhost:6379

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password

# Security Configuration
JWT_SECRET_KEY=your_jwt_secret_key_here_minimum_32_characters
ENCRYPTION_KEY=your_encryption_key_here_32_characters

# Feature Flags
ENABLE_SCHEDULED_AUDITS=true
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_ADVANCED_REPORTING=true
