@inject IJSRuntime JSRuntime

<div class="audit-activity-chart">
    <canvas id="auditActivityChart-@ChartId" width="400" height="200"></canvas>
</div>

@code {
    [Parameter] public List<AuditActivityPoint> Data { get; set; } = new();
    [Parameter] public string Period { get; set; } = "30d";
    
    private string ChartId = Guid.NewGuid().ToString("N")[..8];
    private bool ChartInitialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender || !ChartInitialized)
        {
            await InitializeChart();
            ChartInitialized = true;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ChartInitialized)
        {
            await UpdateChart();
        }
    }

    private async Task InitializeChart()
    {
        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("initializeAuditActivityChart", $"auditActivityChart-{ChartId}", chartData);
    }

    private async Task UpdateChart()
    {
        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("updateAuditActivityChart", $"auditActivityChart-{ChartId}", chartData);
    }

    private object PrepareChartData()
    {
        var sortedData = Data.OrderBy(d => d.Date).ToList();
        
        return new
        {
            labels = sortedData.Select(d => d.Date.ToString("MMM dd")).ToArray(),
            datasets = new[]
            {
                new
                {
                    label = "Audit Runs",
                    data = sortedData.Select(d => d.AuditRuns).ToArray(),
                    borderColor = "rgb(13, 110, 253)",
                    backgroundColor = "rgba(13, 110, 253, 0.1)",
                    borderWidth = 2,
                    fill = true,
                    tension = 0.4,
                    yAxisID = "y"
                },
                new
                {
                    label = "Total Findings",
                    data = sortedData.Select(d => d.Findings).ToArray(),
                    borderColor = "rgb(255, 193, 7)",
                    backgroundColor = "rgba(255, 193, 7, 0.1)",
                    borderWidth = 2,
                    fill = true,
                    tension = 0.4,
                    yAxisID = "y1"
                },
                new
                {
                    label = "Critical Findings",
                    data = sortedData.Select(d => d.CriticalFindings).ToArray(),
                    borderColor = "rgb(220, 53, 69)",
                    backgroundColor = "rgba(220, 53, 69, 0.1)",
                    borderWidth = 3,
                    fill = false,
                    tension = 0.4,
                    yAxisID = "y1",
                    pointBackgroundColor = "rgb(220, 53, 69)",
                    pointBorderColor = "rgb(220, 53, 69)",
                    pointRadius = 4
                }
            }
        };
    }

    public class AuditActivityPoint
    {
        public DateTime Date { get; set; }
        public int AuditRuns { get; set; }
        public int Findings { get; set; }
        public int CriticalFindings { get; set; }
    }
}

<script>
    window.initializeAuditActivityChart = function (canvasId, data) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            title: function(context) {
                                return context[0].label;
                            },
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.y;
                                return label;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: 'Date'
                        },
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: 'Audit Runs'
                        },
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: 'Findings'
                        },
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        }
                    }
                },
                elements: {
                    point: {
                        radius: 3,
                        hoverRadius: 6
                    }
                }
            }
        });

        // Store chart instance for updates
        window[`chart_${canvasId}`] = chart;
    };

    window.updateAuditActivityChart = function (canvasId, data) {
        const chart = window[`chart_${canvasId}`];
        if (chart) {
            chart.data = data;
            chart.update('active');
        }
    };
</script>

<style>
    .audit-activity-chart {
        position: relative;
        height: 300px;
        width: 100%;
    }

    .audit-activity-chart canvas {
        max-height: 300px;
    }
</style>
