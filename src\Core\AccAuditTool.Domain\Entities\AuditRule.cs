namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an audit rule used to evaluate permissions
/// </summary>
public class AuditRule : BaseEntity
{
    /// <summary>
    /// Rule identifier (unique key)
    /// </summary>
    public string RuleId { get; set; } = string.Empty;

    /// <summary>
    /// Rule name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Rule description
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Rule category (Security, Compliance, Best Practice, etc.)
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// Rule severity level
    /// </summary>
    public FindingSeverity DefaultSeverity { get; set; } = FindingSeverity.Medium;

    /// <summary>
    /// Rule type (System, Custom)
    /// </summary>
    public RuleType Type { get; set; } = RuleType.System;

    /// <summary>
    /// Whether the rule is enabled
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// Rule configuration as JSON
    /// </summary>
    public string? Configuration { get; set; }

    /// <summary>
    /// Rule implementation class name
    /// </summary>
    public string? ImplementationClass { get; set; }

    /// <summary>
    /// Rule version
    /// </summary>
    public string Version { get; set; } = "1.0";

    /// <summary>
    /// Tags associated with this rule
    /// </summary>
    public string? Tags { get; set; }

    /// <summary>
    /// Compliance frameworks this rule supports
    /// </summary>
    public string? ComplianceFrameworks { get; set; }

    /// <summary>
    /// Rule documentation/help text
    /// </summary>
    public string? Documentation { get; set; }

    /// <summary>
    /// Audit configurations that use this rule
    /// </summary>
    public ICollection<AuditConfigurationRule> AuditConfigurationRules { get; set; } = new List<AuditConfigurationRule>();

    /// <summary>
    /// Findings generated by this rule
    /// </summary>
    public ICollection<AuditFinding> AuditFindings { get; set; } = new List<AuditFinding>();
}

/// <summary>
/// Rule type enumeration
/// </summary>
public enum RuleType
{
    System,
    Custom
}
