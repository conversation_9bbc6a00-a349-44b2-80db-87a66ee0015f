namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents permissions associated with a role
/// </summary>
public class RolePermission : BaseEntity
{
    /// <summary>
    /// Role this permission belongs to
    /// </summary>
    public Guid RoleId { get; set; }
    public Role Role { get; set; } = null!;

    /// <summary>
    /// Permission action (view, edit, delete, etc.)
    /// </summary>
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// Resource type this permission applies to
    /// </summary>
    public ResourceType ResourceType { get; set; }

    /// <summary>
    /// Whether this permission is granted or denied
    /// </summary>
    public bool IsGranted { get; set; } = true;

    /// <summary>
    /// Permission description
    /// </summary>
    public string? Description { get; set; }
}
