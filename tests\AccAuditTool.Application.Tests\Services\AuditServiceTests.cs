using AccAuditTool.Application.Interfaces;
using AccAuditTool.Application.Services;
using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using AutoFixture.Xunit2;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Linq.Expressions;

namespace AccAuditTool.Application.Tests.Services;

public class AuditServiceTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IAuditRuleEngine> _mockRuleEngine;
    private readonly Mock<ILogger<AuditService>> _mockLogger;
    private readonly AuditService _auditService;

    public AuditServiceTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockRuleEngine = new Mock<IAuditRuleEngine>();
        _mockLogger = new Mock<ILogger<AuditService>>();
        _auditService = new AuditService(_mockUnitOfWork.Object, _mockRuleEngine.Object, _mockLogger.Object);
    }

    [Theory]
    [AutoData]
    public async Task ExecuteProjectAuditAsync_WhenProjectExists_ShouldCreateAuditRun(
        Guid projectId,
        Guid accountId,
        string projectName)
    {
        // Arrange
        var project = new Project
        {
            Id = projectId,
            AccProjectId = "test-project",
            Name = projectName,
            AccountId = accountId,
            Status = ProjectStatus.Active
        };

        var account = new Account
        {
            Id = accountId,
            AccAccountId = "test-account",
            Name = "Test Account",
            Status = AccountStatus.Active
        };

        var auditConfig = new AuditConfiguration
        {
            Id = Guid.NewGuid(),
            Name = "Default Configuration",
            AccountId = accountId,
            IsDefault = true
        };

        var auditFindings = new List<AuditFinding>
        {
            new()
            {
                Id = Guid.NewGuid(),
                Title = "Test Finding 1",
                Severity = FindingSeverity.High,
                RiskScore = 80,
                Status = FindingStatus.Open
            },
            new()
            {
                Id = Guid.NewGuid(),
                Title = "Test Finding 2",
                Severity = FindingSeverity.Medium,
                RiskScore = 60,
                Status = FindingStatus.Open
            }
        };

        _mockUnitOfWork.Setup(x => x.Projects.GetByIdAsync(projectId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(project);

        _mockUnitOfWork.Setup(x => x.AuditConfigurations.FirstOrDefaultAsync(
                It.IsAny<Expression<Func<AuditConfiguration, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditConfig);

        _mockUnitOfWork.Setup(x => x.AuditRuns.AddAsync(It.IsAny<AuditRun>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuditRun ar, CancellationToken ct) => ar);

        _mockRuleEngine.Setup(x => x.ExecuteRulesAsync(projectId, auditConfig, It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditFindings);

        _mockUnitOfWork.Setup(x => x.Permissions.FindAsync(
                It.IsAny<Expression<Func<Permission, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<Permission> { new(), new(), new() });

        _mockUnitOfWork.Setup(x => x.UserRoles.FindAsync(
                It.IsAny<Expression<Func<UserRole, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<UserRole> { new() { UserId = Guid.NewGuid() }, new() { UserId = Guid.NewGuid() } });

        // Act
        var result = await _auditService.ExecuteProjectAuditAsync(projectId);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(AuditRunStatus.Completed);
        result.FindingsCount.Should().Be(2);
        result.OverallRiskScore.Should().BeGreaterThan(0);
        result.UsersAnalyzed.Should().Be(2);
        result.PermissionsAnalyzed.Should().Be(3);

        _mockUnitOfWork.Verify(x => x.AuditRuns.AddAsync(It.IsAny<AuditRun>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
        _mockRuleEngine.Verify(x => x.ExecuteRulesAsync(projectId, auditConfig, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [AutoData]
    public async Task ExecuteProjectAuditAsync_WhenProjectNotFound_ShouldThrowArgumentException(Guid projectId)
    {
        // Arrange
        _mockUnitOfWork.Setup(x => x.Projects.GetByIdAsync(projectId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Project?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(
            () => _auditService.ExecuteProjectAuditAsync(projectId));

        exception.Message.Should().Contain($"Project with ID {projectId} not found");
    }

    [Theory]
    [AutoData]
    public async Task GetAuditRunAsync_WhenAuditRunExists_ShouldReturnDetails(Guid auditRunId)
    {
        // Arrange
        var auditRun = new AuditRun
        {
            Id = auditRunId,
            Status = AuditRunStatus.Completed,
            Type = AuditRunType.Manual,
            StartedAt = DateTime.UtcNow.AddHours(-1),
            CompletedAt = DateTime.UtcNow,
            FindingsCount = 5,
            OverallRiskScore = 75,
            UsersAnalyzed = 10,
            PermissionsAnalyzed = 50,
            InitiatedBy = "<EMAIL>"
        };

        var auditConfig = new AuditConfiguration
        {
            Id = Guid.NewGuid(),
            Name = "Test Configuration"
        };

        var auditFindings = new List<AuditFinding>
        {
            new() { Severity = FindingSeverity.High, Status = FindingStatus.Open },
            new() { Severity = FindingSeverity.High, Status = FindingStatus.Resolved },
            new() { Severity = FindingSeverity.Medium, Status = FindingStatus.Open },
            new() { Severity = FindingSeverity.Low, Status = FindingStatus.Open },
            new() { Severity = FindingSeverity.Low, Status = FindingStatus.Dismissed }
        };

        _mockUnitOfWork.Setup(x => x.AuditRuns.GetByIdAsync(auditRunId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditRun);

        _mockUnitOfWork.Setup(x => x.AuditConfigurations.GetByIdAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditConfig);

        _mockUnitOfWork.Setup(x => x.AuditFindings.FindAsync(
                It.IsAny<Expression<Func<AuditFinding, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(auditFindings);

        // Act
        var result = await _auditService.GetAuditRunAsync(auditRunId);

        // Assert
        result.Should().NotBeNull();
        result!.Id.Should().Be(auditRunId);
        result.Status.Should().Be(AuditRunStatus.Completed);
        result.Type.Should().Be(AuditRunType.Manual);
        result.FindingsCount.Should().Be(5);
        result.OverallRiskScore.Should().Be(75);
        result.UsersAnalyzed.Should().Be(10);
        result.PermissionsAnalyzed.Should().Be(50);
        result.InitiatedBy.Should().Be("<EMAIL>");
        result.ConfigurationName.Should().Be("Test Configuration");

        result.FindingsBySeverity.Should().ContainKeys(
            FindingSeverity.High, FindingSeverity.Medium, FindingSeverity.Low);
        result.FindingsBySeverity[FindingSeverity.High].Should().Be(2);
        result.FindingsBySeverity[FindingSeverity.Medium].Should().Be(1);
        result.FindingsBySeverity[FindingSeverity.Low].Should().Be(2);

        result.FindingsByStatus.Should().ContainKeys(
            FindingStatus.Open, FindingStatus.Resolved, FindingStatus.Dismissed);
        result.FindingsByStatus[FindingStatus.Open].Should().Be(3);
        result.FindingsByStatus[FindingStatus.Resolved].Should().Be(1);
        result.FindingsByStatus[FindingStatus.Dismissed].Should().Be(1);
    }

    [Theory]
    [AutoData]
    public async Task GetAuditRunAsync_WhenAuditRunNotFound_ShouldReturnNull(Guid auditRunId)
    {
        // Arrange
        _mockUnitOfWork.Setup(x => x.AuditRuns.GetByIdAsync(auditRunId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuditRun?)null);

        // Act
        var result = await _auditService.GetAuditRunAsync(auditRunId);

        // Assert
        result.Should().BeNull();
    }

    [Theory]
    [AutoData]
    public async Task UpdateFindingStatusAsync_WhenFindingExists_ShouldUpdateStatus(
        Guid findingId,
        string resolvedBy,
        string resolutionNotes)
    {
        // Arrange
        var finding = new AuditFinding
        {
            Id = findingId,
            Status = FindingStatus.Open,
            Title = "Test Finding",
            Severity = FindingSeverity.Medium
        };

        _mockUnitOfWork.Setup(x => x.AuditFindings.GetByIdAsync(findingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(finding);

        // Act
        var result = await _auditService.UpdateFindingStatusAsync(
            findingId, FindingStatus.Resolved, resolvedBy, resolutionNotes);

        // Assert
        result.Should().BeTrue();
        finding.Status.Should().Be(FindingStatus.Resolved);
        finding.ResolvedBy.Should().Be(resolvedBy);
        finding.ResolutionNotes.Should().Be(resolutionNotes);
        finding.ResolvedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(1));

        _mockUnitOfWork.Verify(x => x.AuditFindings.UpdateAsync(finding, It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Theory]
    [AutoData]
    public async Task UpdateFindingStatusAsync_WhenFindingNotFound_ShouldReturnFalse(Guid findingId)
    {
        // Arrange
        _mockUnitOfWork.Setup(x => x.AuditFindings.GetByIdAsync(findingId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((AuditFinding?)null);

        // Act
        var result = await _auditService.UpdateFindingStatusAsync(
            findingId, FindingStatus.Resolved, "<EMAIL>", "Test resolution");

        // Assert
        result.Should().BeFalse();
        _mockUnitOfWork.Verify(x => x.AuditFindings.UpdateAsync(It.IsAny<AuditFinding>(), It.IsAny<CancellationToken>()), Times.Never);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Never);
    }
}
