namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a role in the ACC system
/// </summary>
public class Role : BaseEntity
{
    /// <summary>
    /// ACC role identifier from Autodesk system
    /// </summary>
    public string AccRoleId { get; set; } = string.Empty;

    /// <summary>
    /// Role name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Role description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Role type (system-defined or custom)
    /// </summary>
    public RoleType Type { get; set; } = RoleType.SystemDefined;

    /// <summary>
    /// Role level (Account, Project, Service)
    /// </summary>
    public RoleLevel Level { get; set; } = RoleLevel.Project;

    /// <summary>
    /// ACC service this role applies to (Docs, Build, etc.)
    /// </summary>
    public string? ServiceKey { get; set; }

    /// <summary>
    /// Permissions associated with this role
    /// </summary>
    public ICollection<RolePermission> RolePermissions { get; set; } = new List<RolePermission>();

    /// <summary>
    /// User assignments to this role
    /// </summary>
    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
}

/// <summary>
/// Role type enumeration
/// </summary>
public enum RoleType
{
    SystemDefined,
    Custom
}

/// <summary>
/// Role level enumeration
/// </summary>
public enum RoleLevel
{
    Account,
    Project,
    Service,
    Folder
}
