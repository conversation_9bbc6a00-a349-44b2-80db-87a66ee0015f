﻿<Router AppAssembly="@typeof(App).Assembly">
    <Found Context="routeData">
        <RouteView RouteData="@routeData" />
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <div class="page-not-found">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 text-center">
                        <i class="fas fa-exclamation-triangle text-warning" style="font-size: 4rem;"></i>
                        <h1 class="mt-3">Page Not Found</h1>
                        <p class="text-muted">Sorry, there's nothing at this address.</p>
                        <a href="/" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Go Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </NotFound>
</Router>
