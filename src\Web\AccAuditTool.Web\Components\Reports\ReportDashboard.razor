@page "/reports"
@using AccAuditTool.Application.Services
@using AccAuditTool.Domain.Entities
@inject IComplianceFrameworkService ComplianceFrameworkService
@inject IRiskScoringEngine RiskScoringEngine
@inject IUnitOfWork UnitOfWork
@inject IJSRuntime JSRuntime

<PageTitle>Reports & Analytics - ACC Audit Tool</PageTitle>

<div class="report-dashboard">
    <div class="page-header">
        <div class="header-content">
            <h1 class="page-title">
                <i class="fas fa-chart-bar"></i>
                Reports & Analytics
            </h1>
            <p class="page-description">Generate comprehensive reports, analyze trends, and export audit data.</p>
        </div>
        <div class="header-actions">
            <button class="btn btn-outline-secondary" @onclick="RefreshData">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" 
                        type="button" 
                        data-bs-toggle="dropdown" 
                        aria-expanded="false">
                    <i class="fas fa-plus"></i>
                    Generate Report
                </button>
                <ul class="dropdown-menu">
                    <li><button class="dropdown-item" @onclick="() => GenerateReport(\"executive\")">
                        <i class="fas fa-briefcase"></i> Executive Summary
                    </button></li>
                    <li><button class="dropdown-item" @onclick="() => GenerateReport(\"detailed\")">
                        <i class="fas fa-list-alt"></i> Detailed Findings
                    </button></li>
                    <li><button class="dropdown-item" @onclick="() => GenerateReport(\"compliance\")">
                        <i class="fas fa-clipboard-check"></i> Compliance Report
                    </button></li>
                    <li><button class="dropdown-item" @onclick="() => GenerateReport(\"trend\")">
                        <i class="fas fa-chart-line"></i> Trend Analysis
                    </button></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><button class="dropdown-item" @onclick="() => GenerateReport(\"custom\")">
                        <i class="fas fa-cog"></i> Custom Report
                    </button></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="report-categories">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="report-category-card" @onclick="() => NavigateToCategory(\"executive\")">
                    <div class="category-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="category-content">
                        <h5>Executive Reports</h5>
                        <p>High-level summaries for leadership and stakeholders</p>
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                @ExecutiveReportCount reports
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="report-category-card" @onclick="() => NavigateToCategory(\"detailed\")">
                    <div class="category-icon">
                        <i class="fas fa-list-alt"></i>
                    </div>
                    <div class="category-content">
                        <h5>Detailed Reports</h5>
                        <p>Comprehensive findings and technical details</p>
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                @DetailedReportCount reports
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="report-category-card" @onclick="() => NavigateToCategory(\"compliance\")">
                    <div class="category-icon">
                        <i class="fas fa-clipboard-check"></i>
                    </div>
                    <div class="category-content">
                        <h5>Compliance Reports</h5>
                        <p>Framework compliance and regulatory assessments</p>
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-file-alt"></i>
                                @ComplianceReportCount reports
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="report-category-card" @onclick="() => NavigateToCategory(\"analytics\")">
                    <div class="category-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="category-content">
                        <h5>Analytics & Trends</h5>
                        <p>Data visualization and trend analysis</p>
                        <div class="category-stats">
                            <span class="stat-item">
                                <i class="fas fa-chart-bar"></i>
                                @AnalyticsReportCount reports
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Analytics -->
    <div class="quick-analytics">
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-chart-area"></i>
                            Audit Activity Overview (Last 30 Days)
                        </h5>
                        <div class="card-actions">
                            <div class="btn-group btn-group-sm" role="group">
                                <button class="btn @(SelectedPeriod == "7d" ? "btn-primary" : "btn-outline-primary")" 
                                        @onclick="() => SetPeriod(\"7d\")">7D</button>
                                <button class="btn @(SelectedPeriod == "30d" ? "btn-primary" : "btn-outline-primary")" 
                                        @onclick="() => SetPeriod(\"30d\")">30D</button>
                                <button class="btn @(SelectedPeriod == "90d" ? "btn-primary" : "btn-outline-primary")" 
                                        @onclick="() => SetPeriod(\"90d\")">90D</button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <AuditActivityChart Data="@AuditActivityData" Period="@SelectedPeriod" />
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            Findings Distribution
                        </h5>
                    </div>
                    <div class="card-body">
                        <FindingsDistributionChart Data="@FindingsDistributionData" />
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="recent-reports">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title">
                            <i class="fas fa-clock"></i>
                            Recent Reports
                        </h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" @onclick="ViewAllReports">
                            <i class="fas fa-list"></i>
                            View All
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @if (RecentReports.Any())
                {
                    <div class="report-list">
                        @foreach (var report in RecentReports.Take(5))
                        {
                            <div class="report-item">
                                <div class="report-icon">
                                    <i class="@GetReportIcon(report.Type) text-@GetReportColor(report.Type)"></i>
                                </div>
                                <div class="report-content">
                                    <div class="report-title">@report.Title</div>
                                    <div class="report-meta">
                                        <span class="report-type">@report.Type</span>
                                        <span class="report-date">@GetRelativeTime(report.GeneratedAt)</span>
                                        <span class="report-size">@FormatFileSize(report.SizeBytes)</span>
                                    </div>
                                </div>
                                <div class="report-actions">
                                    <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewReport(report.Id)">
                                        <i class="fas fa-eye"></i>
                                        View
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" @onclick="() => DownloadReport(report.Id)">
                                        <i class="fas fa-download"></i>
                                        Download
                                    </button>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="no-reports-message">
                        <i class="fas fa-file-alt text-muted"></i>
                        <p class="text-muted">No reports generated yet. Create your first report to get started.</p>
                        <button class="btn btn-primary btn-sm" @onclick="() => GenerateReport(\"executive\")">
                            <i class="fas fa-plus"></i>
                            Generate First Report
                        </button>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="export-options">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="fas fa-download"></i>
                    Export Data
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="export-option" @onclick="() => ExportData(\"audit-runs\")">
                            <i class="fas fa-clipboard-list"></i>
                            <span>Audit Runs</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="export-option" @onclick="() => ExportData(\"findings\")">
                            <i class="fas fa-search"></i>
                            <span>Findings</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="export-option" @onclick="() => ExportData(\"compliance\")">
                            <i class="fas fa-clipboard-check"></i>
                            <span>Compliance Data</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="export-option" @onclick="() => ExportData(\"risk-scores\")">
                            <i class="fas fa-shield-alt"></i>
                            <span>Risk Scores</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private bool IsLoading = true;
    private string SelectedPeriod = "30d";

    // Sample data - in real implementation, this would come from services
    private int ExecutiveReportCount = 12;
    private int DetailedReportCount = 28;
    private int ComplianceReportCount = 8;
    private int AnalyticsReportCount = 15;

    private List<AuditActivityPoint> AuditActivityData = new();
    private Dictionary<AuditSeverity, int> FindingsDistributionData = new();
    private List<ReportInfo> RecentReports = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        IsLoading = true;
        StateHasChanged();

        try
        {
            // Load audit activity data
            AuditActivityData = GenerateAuditActivityData();
            
            // Load findings distribution
            FindingsDistributionData = GenerateFindingsDistribution();
            
            // Load recent reports
            RecentReports = GenerateRecentReports();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task RefreshData()
    {
        await LoadDashboardData();
    }

    private async Task SetPeriod(string period)
    {
        SelectedPeriod = period;
        AuditActivityData = GenerateAuditActivityData();
        StateHasChanged();
    }

    private async Task NavigateToCategory(string category)
    {
        // Navigate to specific report category page
        // Navigation.NavigateTo($"/reports/{category}");
    }

    private async Task GenerateReport(string reportType)
    {
        // Navigate to report generation page
        // Navigation.NavigateTo($"/reports/generate/{reportType}");
    }

    private async Task ViewAllReports()
    {
        // Navigate to all reports page
        // Navigation.NavigateTo("/reports/all");
    }

    private async Task ViewReport(Guid reportId)
    {
        // Navigate to report view page
        // Navigation.NavigateTo($"/reports/view/{reportId}");
    }

    private async Task DownloadReport(Guid reportId)
    {
        // Download report file
        await JSRuntime.InvokeVoidAsync("downloadFile", $"/api/reports/{reportId}/download");
    }

    private async Task ExportData(string dataType)
    {
        // Export specific data type
        await JSRuntime.InvokeVoidAsync("downloadFile", $"/api/export/{dataType}");
    }

    private string GetReportIcon(string reportType)
    {
        return reportType.ToLowerInvariant() switch
        {
            "executive" => "fas fa-briefcase",
            "detailed" => "fas fa-list-alt",
            "compliance" => "fas fa-clipboard-check",
            "trend" => "fas fa-chart-line",
            "custom" => "fas fa-cog",
            _ => "fas fa-file-alt"
        };
    }

    private string GetReportColor(string reportType)
    {
        return reportType.ToLowerInvariant() switch
        {
            "executive" => "primary",
            "detailed" => "info",
            "compliance" => "warning",
            "trend" => "success",
            "custom" => "secondary",
            _ => "muted"
        };
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;

        return timeSpan switch
        {
            { TotalMinutes: < 60 } => $"{(int)timeSpan.TotalMinutes}m ago",
            { TotalHours: < 24 } => $"{(int)timeSpan.TotalHours}h ago",
            { TotalDays: < 7 } => $"{(int)timeSpan.TotalDays}d ago",
            { TotalDays: < 30 } => $"{(int)(timeSpan.TotalDays / 7)}w ago",
            _ => dateTime.ToString("MMM dd, yyyy")
        };
    }

    private string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    private List<AuditActivityPoint> GenerateAuditActivityData()
    {
        var data = new List<AuditActivityPoint>();
        var random = new Random();
        var days = SelectedPeriod switch
        {
            "7d" => 7,
            "30d" => 30,
            "90d" => 90,
            _ => 30
        };

        for (int i = days - 1; i >= 0; i--)
        {
            var date = DateTime.Today.AddDays(-i);
            data.Add(new AuditActivityPoint
            {
                Date = date,
                AuditRuns = random.Next(0, 5),
                Findings = random.Next(0, 20),
                CriticalFindings = random.Next(0, 3)
            });
        }

        return data;
    }

    private Dictionary<AuditSeverity, int> GenerateFindingsDistribution()
    {
        return new Dictionary<AuditSeverity, int>
        {
            [AuditSeverity.Critical] = 5,
            [AuditSeverity.High] = 12,
            [AuditSeverity.Medium] = 28,
            [AuditSeverity.Low] = 45,
            [AuditSeverity.Info] = 18
        };
    }

    private List<ReportInfo> GenerateRecentReports()
    {
        return new List<ReportInfo>
        {
            new ReportInfo
            {
                Id = Guid.NewGuid(),
                Title = "Monthly Security Audit Report",
                Type = "Executive",
                GeneratedAt = DateTime.UtcNow.AddHours(-2),
                SizeBytes = 2048576 // 2MB
            },
            new ReportInfo
            {
                Id = Guid.NewGuid(),
                Title = "Detailed Findings Analysis",
                Type = "Detailed",
                GeneratedAt = DateTime.UtcNow.AddHours(-6),
                SizeBytes = 5242880 // 5MB
            },
            new ReportInfo
            {
                Id = Guid.NewGuid(),
                Title = "ISO 27001 Compliance Assessment",
                Type = "Compliance",
                GeneratedAt = DateTime.UtcNow.AddDays(-1),
                SizeBytes = 1572864 // 1.5MB
            },
            new ReportInfo
            {
                Id = Guid.NewGuid(),
                Title = "Risk Trend Analysis Q4",
                Type = "Trend",
                GeneratedAt = DateTime.UtcNow.AddDays(-2),
                SizeBytes = 3145728 // 3MB
            }
        };
    }

    public class AuditActivityPoint
    {
        public DateTime Date { get; set; }
        public int AuditRuns { get; set; }
        public int Findings { get; set; }
        public int CriticalFindings { get; set; }
    }

    public class ReportInfo
    {
        public Guid Id { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public DateTime GeneratedAt { get; set; }
        public long SizeBytes { get; set; }
    }
}

<style>
    .report-dashboard {
        padding: 0;
    }

    .page-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .header-content {
        flex: 1;
    }

    .page-title {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        margin-right: 0.75rem;
        color: #007bff;
    }

    .page-description {
        color: #6c757d;
        margin: 0;
        font-size: 0.95rem;
    }

    .header-actions {
        display: flex;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .report-categories {
        margin-bottom: 2rem;
    }

    .report-category-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1.5rem;
        height: 100%;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
    }

    .report-category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-color: #007bff;
    }

    .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #007bff, #0056b3);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }

    .category-icon i {
        font-size: 1.5rem;
        color: #fff;
    }

    .category-content {
        flex: 1;
    }

    .category-content h5 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .category-content p {
        color: #6c757d;
        font-size: 0.875rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .category-stats {
        margin-top: auto;
    }

    .stat-item {
        color: #6c757d;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .quick-analytics {
        margin-bottom: 2rem;
    }

    .card-actions {
        display: flex;
        gap: 0.5rem;
    }

    .recent-reports {
        margin-bottom: 2rem;
    }

    .report-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }

    .report-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid #f1f3f4;
        border-radius: 0.375rem;
        transition: background-color 0.2s ease;
    }

    .report-item:hover {
        background-color: #f8f9fa;
    }

    .report-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .report-icon i {
        font-size: 1.2rem;
    }

    .report-content {
        flex: 1;
        min-width: 0;
    }

    .report-title {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .report-meta {
        display: flex;
        gap: 1rem;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .report-actions {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
    }

    .no-reports-message {
        text-align: center;
        padding: 2rem;
    }

    .no-reports-message i {
        font-size: 3rem;
        margin-bottom: 1rem;
    }

    .export-options {
        margin-bottom: 2rem;
    }

    .export-option {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1.5rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.2s ease;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }

    .export-option:hover {
        background-color: #f8f9fa;
        border-color: #007bff;
        transform: translateY(-1px);
    }

    .export-option i {
        font-size: 2rem;
        color: #007bff;
    }

    .export-option span {
        font-weight: 500;
        color: #495057;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .page-header {
            flex-direction: column;
            gap: 1rem;
            align-items: stretch;
        }

        .header-actions {
            justify-content: stretch;
        }

        .report-category-card {
            margin-bottom: 1rem;
        }

        .report-item {
            flex-direction: column;
            text-align: center;
            gap: 0.75rem;
        }

        .report-actions {
            justify-content: center;
        }
    }
</style>
