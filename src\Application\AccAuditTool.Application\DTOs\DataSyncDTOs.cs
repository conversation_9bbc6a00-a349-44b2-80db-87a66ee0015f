namespace AccAuditTool.Application.Interfaces;

/// <summary>
/// Result of data synchronization operation
/// </summary>
public class DataSyncResult
{
    public bool Success { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime CompletedAt { get; set; }
    public TimeSpan Duration => CompletedAt - StartedAt;
    public string? ErrorMessage { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    // Entity counts
    public int AccountsProcessed { get; set; }
    public int AccountsCreated { get; set; }
    public int AccountsUpdated { get; set; }
    
    public int ProjectsProcessed { get; set; }
    public int ProjectsCreated { get; set; }
    public int ProjectsUpdated { get; set; }
    
    public int UsersProcessed { get; set; }
    public int UsersCreated { get; set; }
    public int UsersUpdated { get; set; }
    
    public int CompaniesProcessed { get; set; }
    public int CompaniesCreated { get; set; }
    public int CompaniesUpdated { get; set; }
    
    public int RolesProcessed { get; set; }
    public int RolesCreated { get; set; }
    public int RolesUpdated { get; set; }
    
    public int ResourcesProcessed { get; set; }
    public int ResourcesCreated { get; set; }
    public int ResourcesUpdated { get; set; }
    
    public int PermissionsProcessed { get; set; }
    public int PermissionsCreated { get; set; }
    public int PermissionsUpdated { get; set; }
}

/// <summary>
/// Status of data synchronization
/// </summary>
public class DataSyncStatus
{
    public string AccAccountId { get; set; } = string.Empty;
    public DateTime? LastFullSync { get; set; }
    public DateTime? LastIncrementalSync { get; set; }
    public bool IsScheduled { get; set; }
    public TimeSpan SyncInterval { get; set; }
    public DateTime? NextScheduledSync { get; set; }
    public string? LastSyncStatus { get; set; }
    public string? LastErrorMessage { get; set; }
}

/// <summary>
/// ACC Account DTO
/// </summary>
public class AccAccountDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Region { get; set; }
    public string? SubscriptionType { get; set; }
}

/// <summary>
/// ACC Project DTO
/// </summary>
public class AccProjectDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AccountId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? ProjectType { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// ACC User DTO
/// </summary>
public class AccUserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? CompanyId { get; set; }
    public string? CompanyName { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LastSeenAt { get; set; }
    public List<AccUserRoleDto> Roles { get; set; } = new();
}

/// <summary>
/// ACC User Role DTO
/// </summary>
public class AccUserRoleDto
{
    public string RoleId { get; set; } = string.Empty;
    public string RoleName { get; set; } = string.Empty;
    public string? ServiceKey { get; set; }
    public DateTime AssignedAt { get; set; }
}

/// <summary>
/// ACC Company DTO
/// </summary>
public class AccCompanyDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Trade { get; set; }
    public string? Address { get; set; }
    public string? Phone { get; set; }
}

/// <summary>
/// ACC Role DTO
/// </summary>
public class AccRoleDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ServiceKey { get; set; }
    public List<string> Permissions { get; set; } = new();
}

/// <summary>
/// ACC Resource DTO
/// </summary>
public class AccResourceDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? ParentId { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string? LastModifiedBy { get; set; }
}

/// <summary>
/// ACC Permission DTO
/// </summary>
public class AccPermissionDto
{
    public string SubjectType { get; set; } = string.Empty;
    public string SubjectId { get; set; } = string.Empty;
    public string ResourceId { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public string? InheritedFrom { get; set; }
}
