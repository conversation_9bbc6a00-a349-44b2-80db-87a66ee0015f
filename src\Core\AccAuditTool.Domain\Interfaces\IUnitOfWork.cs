using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Domain.Interfaces;

/// <summary>
/// Unit of Work pattern interface for managing database transactions
/// </summary>
public interface IUnitOfWork : IDisposable
{
    /// <summary>
    /// Users repository
    /// </summary>
    IRepository<User> Users { get; }

    /// <summary>
    /// Companies repository
    /// </summary>
    IRepository<Company> Companies { get; }

    /// <summary>
    /// Projects repository
    /// </summary>
    IRepository<Project> Projects { get; }

    /// <summary>
    /// Accounts repository
    /// </summary>
    IRepository<Account> Accounts { get; }

    /// <summary>
    /// Roles repository
    /// </summary>
    IRepository<Role> Roles { get; }

    /// <summary>
    /// Permissions repository
    /// </summary>
    IRepository<Permission> Permissions { get; }

    /// <summary>
    /// Resources repository
    /// </summary>
    IRepository<Resource> Resources { get; }

    /// <summary>
    /// User roles repository
    /// </summary>
    IRepository<UserRole> UserRoles { get; }

    /// <summary>
    /// Project companies repository
    /// </summary>
    IRepository<ProjectCompany> ProjectCompanies { get; }

    /// <summary>
    /// Role permissions repository
    /// </summary>
    IRepository<RolePermission> RolePermissions { get; }

    /// <summary>
    /// Audit runs repository
    /// </summary>
    IRepository<AuditRun> AuditRuns { get; }

    /// <summary>
    /// Audit findings repository
    /// </summary>
    IRepository<AuditFinding> AuditFindings { get; }

    /// <summary>
    /// Audit rules repository
    /// </summary>
    IRepository<AuditRule> AuditRules { get; }

    /// <summary>
    /// Audit configurations repository
    /// </summary>
    IRepository<AuditConfiguration> AuditConfigurations { get; }

    /// <summary>
    /// Audit configuration rules repository
    /// </summary>
    IRepository<AuditConfigurationRule> AuditConfigurationRules { get; }

    /// <summary>
    /// Save all changes to the database
    /// </summary>
    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Begin a database transaction
    /// </summary>
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Commit the current transaction
    /// </summary>
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Rollback the current transaction
    /// </summary>
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
