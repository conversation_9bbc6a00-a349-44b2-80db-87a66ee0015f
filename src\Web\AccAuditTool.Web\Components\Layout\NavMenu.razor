@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<div class="nav-menu">
    <div class="nav-header">
        <div class="nav-brand">
            <i class="fas fa-shield-alt brand-icon"></i>
            <span class="brand-text">ACC Audit Tool</span>
        </div>
        <button class="sidebar-toggle btn btn-link" type="button">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <nav class="nav-content">
        <ul class="nav-list">
            <!-- Dashboard -->
            <li class="nav-item">
                <NavLink class="nav-link" href="/" Match="NavLinkMatch.All">
                    <i class="nav-icon fas fa-tachometer-alt"></i>
                    <span class="nav-text">Dashboard</span>
                </NavLink>
            </li>

            <!-- Audits Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-clipboard-list"></i>
                    <span>Audits</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/audits">
                    <i class="nav-icon fas fa-list"></i>
                    <span class="nav-text">All Audits</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/audits/create">
                    <i class="nav-icon fas fa-plus"></i>
                    <span class="nav-text">New Audit</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/audits/schedule">
                    <i class="nav-icon fas fa-calendar-alt"></i>
                    <span class="nav-text">Scheduled</span>
                </NavLink>
            </li>

            <!-- Findings Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Findings</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/findings">
                    <i class="nav-icon fas fa-search"></i>
                    <span class="nav-text">All Findings</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/findings/critical">
                    <i class="nav-icon fas fa-fire"></i>
                    <span class="nav-text">Critical</span>
                    @if (CriticalFindingsCount > 0)
                    {
                        <span class="nav-badge badge bg-danger">@CriticalFindingsCount</span>
                    }
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/findings/unresolved">
                    <i class="nav-icon fas fa-clock"></i>
                    <span class="nav-text">Unresolved</span>
                    @if (UnresolvedFindingsCount > 0)
                    {
                        <span class="nav-badge badge bg-warning">@UnresolvedFindingsCount</span>
                    }
                </NavLink>
            </li>

            <!-- Rules Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-cogs"></i>
                    <span>Rules</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/rules">
                    <i class="nav-icon fas fa-list-ul"></i>
                    <span class="nav-text">All Rules</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/rules/create">
                    <i class="nav-icon fas fa-plus-circle"></i>
                    <span class="nav-text">Create Rule</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/rules/templates">
                    <i class="nav-icon fas fa-copy"></i>
                    <span class="nav-text">Templates</span>
                </NavLink>
            </li>

            <!-- Compliance Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-clipboard-check"></i>
                    <span>Compliance</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/compliance">
                    <i class="nav-icon fas fa-chart-pie"></i>
                    <span class="nav-text">Overview</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/compliance/frameworks">
                    <i class="nav-icon fas fa-building"></i>
                    <span class="nav-text">Frameworks</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/compliance/reports">
                    <i class="nav-icon fas fa-file-alt"></i>
                    <span class="nav-text">Reports</span>
                </NavLink>
            </li>

            <!-- Reports Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-chart-bar"></i>
                    <span>Reports</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/reports">
                    <i class="nav-icon fas fa-chart-line"></i>
                    <span class="nav-text">Analytics</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/reports/executive">
                    <i class="nav-icon fas fa-briefcase"></i>
                    <span class="nav-text">Executive</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/reports/export">
                    <i class="nav-icon fas fa-download"></i>
                    <span class="nav-text">Export</span>
                </NavLink>
            </li>

            <!-- Administration Section -->
            <li class="nav-section">
                <div class="nav-section-header">
                    <i class="fas fa-cog"></i>
                    <span>Administration</span>
                </div>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/admin/users">
                    <i class="nav-icon fas fa-users"></i>
                    <span class="nav-text">Users</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/admin/settings">
                    <i class="nav-icon fas fa-wrench"></i>
                    <span class="nav-text">Settings</span>
                </NavLink>
            </li>
            <li class="nav-item">
                <NavLink class="nav-link" href="/admin/logs">
                    <i class="nav-icon fas fa-file-text"></i>
                    <span class="nav-text">Logs</span>
                </NavLink>
            </li>
        </ul>
    </nav>

    <div class="nav-footer">
        <div class="nav-user">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
                <div class="user-name">@CurrentUserName</div>
                <div class="user-role">@CurrentUserRole</div>
            </div>
        </div>
        <div class="nav-version">
            <small class="text-muted">v1.0.0</small>
        </div>
    </div>
</div>

@code {
    private int CriticalFindingsCount = 5; // Would be loaded from service
    private int UnresolvedFindingsCount = 12; // Would be loaded from service
    private string CurrentUserName = "John Doe"; // Would be loaded from auth service
    private string CurrentUserRole = "Audit Manager"; // Would be loaded from auth service

    protected override async Task OnInitializedAsync()
    {
        // Load navigation data
        await LoadNavigationData();
    }

    private async Task LoadNavigationData()
    {
        // In a real implementation, this would load from services
        // CriticalFindingsCount = await FindingsService.GetCriticalCountAsync();
        // UnresolvedFindingsCount = await FindingsService.GetUnresolvedCountAsync();
        // CurrentUserName = await AuthService.GetCurrentUserNameAsync();
        // CurrentUserRole = await AuthService.GetCurrentUserRoleAsync();
    }
}

<style>
    .nav-menu {
        height: 100%;
        display: flex;
        flex-direction: column;
        color: rgba(255, 255, 255, 0.9);
    }

    .nav-header {
        padding: 1rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-height: 60px;
    }

    .nav-brand {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-weight: 600;
        font-size: 1.1rem;
    }

    .brand-icon {
        font-size: 1.5rem;
        color: #ffd700;
    }

    .brand-text {
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .brand-text {
        opacity: 0;
        width: 0;
        overflow: hidden;
    }

    .sidebar-toggle {
        color: rgba(255, 255, 255, 0.7);
        border: none;
        background: none;
        padding: 0.5rem;
        border-radius: 0.25rem;
        transition: all 0.2s ease;
    }

    .sidebar-toggle:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
    }

    .nav-content {
        flex: 1;
        overflow-y: auto;
        padding: 1rem 0;
    }

    .nav-list {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    .nav-section {
        margin: 1.5rem 0 0.5rem 0;
    }

    .nav-section:first-child {
        margin-top: 0;
    }

    .nav-section-header {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: rgba(255, 255, 255, 0.6);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .nav-section-header {
        opacity: 0;
        height: 0;
        padding: 0;
        margin: 0;
        overflow: hidden;
    }

    .nav-item {
        margin: 0;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
        position: relative;
    }

    .nav-link:hover {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.1);
        border-left-color: rgba(255, 255, 255, 0.3);
    }

    .nav-link.active {
        color: #fff;
        background-color: rgba(255, 255, 255, 0.15);
        border-left-color: #ffd700;
        font-weight: 500;
    }

    .nav-icon {
        width: 1.25rem;
        margin-right: 0.75rem;
        text-align: center;
        flex-shrink: 0;
    }

    .nav-text {
        flex: 1;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .nav-text {
        opacity: 0;
        width: 0;
        overflow: hidden;
    }

    .nav-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: 0.75rem;
        margin-left: auto;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .nav-badge {
        opacity: 0;
        width: 0;
        overflow: hidden;
    }

    .nav-footer {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding: 1rem;
    }

    .nav-user {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
    }

    .user-info {
        flex: 1;
        min-width: 0;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .user-info {
        opacity: 0;
        width: 0;
        overflow: hidden;
    }

    .user-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: #fff;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .user-role {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .nav-version {
        text-align: center;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .nav-version {
        opacity: 0;
    }

    /* Tooltip for collapsed sidebar */
    .sidebar.collapsed .nav-link {
        position: relative;
    }

    .sidebar.collapsed .nav-link::after {
        content: attr(title);
        position: absolute;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        background-color: #333;
        color: #fff;
        padding: 0.5rem 0.75rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
        white-space: nowrap;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.2s ease;
        z-index: 1000;
        margin-left: 0.5rem;
    }

    .sidebar.collapsed .nav-link:hover::after {
        opacity: 1;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .nav-header {
            padding: 0.75rem;
        }

        .nav-link {
            padding: 0.625rem 1rem;
        }

        .nav-footer {
            padding: 0.75rem;
        }
    }

    /* Accessibility */
    .nav-link:focus {
        outline: 2px solid #ffd700;
        outline-offset: -2px;
    }

    /* Animation for active state */
    .nav-link.active::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 3px;
        background-color: #ffd700;
        animation: slideIn 0.3s ease;
    }

    @keyframes slideIn {
        from {
            transform: scaleY(0);
        }
        to {
            transform: scaleY(1);
        }
    }
</style>
