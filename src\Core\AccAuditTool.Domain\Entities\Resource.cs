namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents a resource (folder, file, etc.) in the ACC system
/// </summary>
public class Resource : BaseEntity
{
    /// <summary>
    /// ACC resource identifier from Autodesk system
    /// </summary>
    public string AccResourceId { get; set; } = string.Empty;

    /// <summary>
    /// Resource name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Resource path in the project hierarchy
    /// </summary>
    public string Path { get; set; } = string.Empty;

    /// <summary>
    /// Resource type
    /// </summary>
    public ResourceType Type { get; set; }

    /// <summary>
    /// Project this resource belongs to
    /// </summary>
    public Guid ProjectId { get; set; }
    public Project Project { get; set; } = null!;

    /// <summary>
    /// Parent resource (for hierarchical structure)
    /// </summary>
    public Guid? ParentResourceId { get; set; }
    public Resource? ParentResource { get; set; }

    /// <summary>
    /// Child resources
    /// </summary>
    public ICollection<Resource> ChildResources { get; set; } = new List<Resource>();

    /// <summary>
    /// File size (if applicable)
    /// </summary>
    public long? FileSize { get; set; }

    /// <summary>
    /// File extension (if applicable)
    /// </summary>
    public string? FileExtension { get; set; }

    /// <summary>
    /// Resource version
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// Last modified date
    /// </summary>
    public DateTime? LastModifiedAt { get; set; }

    /// <summary>
    /// User who last modified the resource
    /// </summary>
    public string? LastModifiedBy { get; set; }

    /// <summary>
    /// Permissions assigned to this resource
    /// </summary>
    public ICollection<Permission> Permissions { get; set; } = new List<Permission>();
}
