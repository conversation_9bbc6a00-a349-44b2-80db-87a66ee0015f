@page "/dashboard"
@using AccAuditTool.Application.Services
@using AccAuditTool.Domain.Entities
@inject IRuleExecutionEngine RuleExecutionEngine
@inject IComplianceFrameworkService ComplianceFrameworkService
@inject IRiskScoringEngine RiskScoringEngine
@inject IUnitOfWork UnitOfWork

<PageTitle>Dashboard - ACC Audit Tool</PageTitle>

<div class="dashboard-container">
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-tachometer-alt"></i>
            Audit Dashboard
        </h1>
        <div class="dashboard-actions">
            <button class="btn btn-primary" @onclick="RefreshDashboard">
                <i class="fas fa-sync-alt"></i>
                Refresh
            </button>
            <button class="btn btn-success" @onclick="StartNewAudit">
                <i class="fas fa-play"></i>
                Start New Audit
            </button>
        </div>
    </div>

    @if (IsLoading)
    {
        <div class="loading-container">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="loading-text">Loading dashboard data...</p>
        </div>
    }
    else
    {
        <div class="dashboard-content">
            <!-- Key Metrics Row -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <DashboardMetricCard 
                        Title="Total Audits"
                        Value="@TotalAudits.ToString()"
                        Icon="fas fa-clipboard-list"
                        Color="primary"
                        Trend="@AuditTrend" />
                </div>
                <div class="col-md-3">
                    <DashboardMetricCard 
                        Title="Active Findings"
                        Value="@ActiveFindings.ToString()"
                        Icon="fas fa-exclamation-triangle"
                        Color="warning"
                        Trend="@FindingsTrend" />
                </div>
                <div class="col-md-3">
                    <DashboardMetricCard 
                        Title="Risk Score"
                        Value="@OverallRiskScore.ToString("F1")"
                        Icon="fas fa-shield-alt"
                        Color="@GetRiskScoreColor(OverallRiskScore)"
                        Trend="@RiskTrend" />
                </div>
                <div class="col-md-3">
                    <DashboardMetricCard 
                        Title="Compliance %"
                        Value="@CompliancePercentage.ToString("F1")%"
                        Icon="fas fa-check-circle"
                        Color="@GetComplianceColor(CompliancePercentage)"
                        Trend="@ComplianceTrend" />
                </div>
            </div>

            <!-- Charts and Visualizations Row -->
            <div class="row mb-4">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                Risk Trends (Last 30 Days)
                            </h5>
                        </div>
                        <div class="card-body">
                            <RiskTrendChart Data="@RiskTrendData" />
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                Findings by Severity
                            </h5>
                        </div>
                        <div class="card-body">
                            <FindingsSeverityChart Data="@FindingsBySeverity" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity and Compliance Status Row -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-clock"></i>
                                Recent Audit Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <RecentAuditActivity Activities="@RecentActivities" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-clipboard-check"></i>
                                Compliance Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <ComplianceStatusOverview Frameworks="@ComplianceFrameworks" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Critical Findings and Top Risks Row -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-exclamation-circle text-danger"></i>
                                Critical Findings
                            </h5>
                        </div>
                        <div class="card-body">
                            <CriticalFindingsList Findings="@CriticalFindings" />
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title">
                                <i class="fas fa-fire text-danger"></i>
                                Top Risk Factors
                            </h5>
                        </div>
                        <div class="card-body">
                            <TopRiskFactors Risks="@TopRisks" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool IsLoading = true;
    
    // Dashboard metrics
    private int TotalAudits;
    private int ActiveFindings;
    private double OverallRiskScore;
    private double CompliancePercentage;
    
    // Trend indicators
    private TrendDirection AuditTrend;
    private TrendDirection FindingsTrend;
    private TrendDirection RiskTrend;
    private TrendDirection ComplianceTrend;
    
    // Dashboard data
    private List<RiskTrendPoint> RiskTrendData = new();
    private Dictionary<AuditSeverity, int> FindingsBySeverity = new();
    private List<AuditActivity> RecentActivities = new();
    private List<ComplianceFramework> ComplianceFrameworks = new();
    private List<AuditFinding> CriticalFindings = new();
    private List<RiskFactor> TopRisks = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadDashboardData();
    }

    private async Task LoadDashboardData()
    {
        IsLoading = true;
        StateHasChanged();

        try
        {
            // Load basic metrics
            await LoadBasicMetrics();
            
            // Load trend data
            await LoadTrendData();
            
            // Load chart data
            await LoadChartData();
            
            // Load recent activity
            await LoadRecentActivity();
            
            // Load compliance data
            await LoadComplianceData();
            
            // Load critical findings and risks
            await LoadCriticalData();
        }
        catch (Exception ex)
        {
            // Handle error - could show toast notification
            Console.WriteLine($"Error loading dashboard data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadBasicMetrics()
    {
        // Get recent audit runs
        var recentAudits = await UnitOfWork.AuditRuns.FindAsync(
            ar => ar.StartedAt >= DateTime.UtcNow.AddDays(-30));
        TotalAudits = recentAudits.Count();

        // Get active findings
        var activeFindings = await UnitOfWork.AuditFindings.FindAsync(
            f => f.Status == AuditFindingStatus.New || f.Status == AuditFindingStatus.InProgress);
        ActiveFindings = activeFindings.Count();

        // Get latest risk score
        var latestAudit = recentAudits.OrderByDescending(ar => ar.StartedAt).FirstOrDefault();
        if (latestAudit != null)
        {
            var riskScore = await RiskScoringEngine.CalculateOverallRiskScoreAsync(latestAudit.Id);
            OverallRiskScore = riskScore.Score;
        }

        // Calculate compliance percentage (placeholder)
        CompliancePercentage = 85.5; // Would calculate from actual compliance assessments
    }

    private async Task LoadTrendData()
    {
        // Calculate trends (simplified)
        AuditTrend = TrendDirection.Increasing;
        FindingsTrend = TrendDirection.Decreasing;
        RiskTrend = TrendDirection.Stable;
        ComplianceTrend = TrendDirection.Increasing;
    }

    private async Task LoadChartData()
    {
        // Load risk trend data for chart
        var endDate = DateTime.UtcNow;
        var startDate = endDate.AddDays(-30);
        
        // Generate sample risk trend data
        RiskTrendData = GenerateSampleRiskTrendData(startDate, endDate);
        
        // Load findings by severity
        var allFindings = await UnitOfWork.AuditFindings.FindAsync(
            f => f.DetectedAt >= startDate);
        
        FindingsBySeverity = allFindings
            .GroupBy(f => f.Severity)
            .ToDictionary(g => g.Key, g => g.Count());
    }

    private async Task LoadRecentActivity()
    {
        // Load recent audit activities
        RecentActivities = GenerateSampleRecentActivities();
    }

    private async Task LoadComplianceData()
    {
        // Load compliance frameworks
        ComplianceFrameworks = (await ComplianceFrameworkService.GetAvailableFrameworksAsync()).ToList();
    }

    private async Task LoadCriticalData()
    {
        // Load critical findings
        CriticalFindings = (await UnitOfWork.AuditFindings.FindAsync(
            f => f.Severity == AuditSeverity.Critical && 
                 f.Status == AuditFindingStatus.New))
            .Take(5)
            .ToList();

        // Generate top risk factors
        TopRisks = GenerateSampleTopRisks();
    }

    private async Task RefreshDashboard()
    {
        await LoadDashboardData();
    }

    private async Task StartNewAudit()
    {
        // Navigate to audit creation page or show modal
        // Navigation.NavigateTo("/audits/create");
    }

    private string GetRiskScoreColor(double score)
    {
        return score switch
        {
            >= 80 => "danger",
            >= 60 => "warning",
            >= 40 => "info",
            _ => "success"
        };
    }

    private string GetComplianceColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "success",
            >= 70 => "warning",
            _ => "danger"
        };
    }

    // Helper methods for sample data generation
    private List<RiskTrendPoint> GenerateSampleRiskTrendData(DateTime startDate, DateTime endDate)
    {
        var data = new List<RiskTrendPoint>();
        var random = new Random();
        var currentDate = startDate;
        var baseScore = 45.0;

        while (currentDate <= endDate)
        {
            baseScore += (random.NextDouble() - 0.5) * 10;
            baseScore = Math.Max(0, Math.Min(100, baseScore));

            data.Add(new RiskTrendPoint
            {
                Date = currentDate,
                Score = baseScore,
                Level = DetermineRiskLevel(baseScore),
                FindingCount = random.Next(0, 20)
            });

            currentDate = currentDate.AddDays(1);
        }

        return data;
    }

    private RiskLevel DetermineRiskLevel(double score)
    {
        return score switch
        {
            >= 80 => RiskLevel.Critical,
            >= 60 => RiskLevel.High,
            >= 40 => RiskLevel.Medium,
            >= 20 => RiskLevel.Low,
            _ => RiskLevel.Minimal
        };
    }

    private List<AuditActivity> GenerateSampleRecentActivities()
    {
        return new List<AuditActivity>
        {
            new() { Type = "Audit Completed", Description = "Monthly security audit completed", Timestamp = DateTime.UtcNow.AddHours(-2), User = "System" },
            new() { Type = "Critical Finding", Description = "Excessive permissions detected <NAME_EMAIL>", Timestamp = DateTime.UtcNow.AddHours(-4), User = "Audit Engine" },
            new() { Type = "Rule Updated", Description = "SEC-001 rule parameters updated", Timestamp = DateTime.UtcNow.AddHours(-6), User = "<EMAIL>" },
            new() { Type = "Compliance Check", Description = "ISO 27001 compliance assessment completed", Timestamp = DateTime.UtcNow.AddHours(-8), User = "System" }
        };
    }

    private List<RiskFactor> GenerateSampleTopRisks()
    {
        return new List<RiskFactor>
        {
            new() { Name = "Inactive Users with Active Permissions", Description = "Users who haven't logged in for 90+ days still have active permissions", Impact = 8.5, Category = "Security" },
            new() { Name = "Excessive Project Access", Description = "Users with access to more than 10 projects", Impact = 7.2, Category = "Access Control" },
            new() { Name = "Role Segregation Violations", Description = "Users with conflicting administrative roles", Impact = 6.8, Category = "Compliance" },
            new() { Name = "Unreviewed Permissions", Description = "Permissions not reviewed in the last 6 months", Impact = 5.9, Category = "Governance" }
        };
    }
}

<style>
    .dashboard-container {
        padding: 20px;
    }

    .dashboard-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #dee2e6;
    }

    .dashboard-title {
        color: #495057;
        font-weight: 600;
        margin: 0;
    }

    .dashboard-title i {
        margin-right: 10px;
        color: #007bff;
    }

    .dashboard-actions {
        display: flex;
        gap: 10px;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 400px;
    }

    .loading-text {
        margin-top: 15px;
        color: #6c757d;
    }

    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
        margin-bottom: 20px;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-title {
        margin: 0;
        font-weight: 600;
        color: #495057;
    }

    .card-title i {
        margin-right: 8px;
    }
</style>
