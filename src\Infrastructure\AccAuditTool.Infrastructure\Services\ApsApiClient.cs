using AccAuditTool.Domain.Interfaces;
using AccAuditTool.Infrastructure.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using System.Net;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Resilient HTTP client for Autodesk APS API with rate limiting, retries, and circuit breaker
/// </summary>
public interface IApsApiClient
{
    /// <summary>
    /// Execute a GET request with full resilience patterns
    /// </summary>
    Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default) where T : class;

    /// <summary>
    /// Execute a POST request with full resilience patterns
    /// </summary>
    Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default) 
        where TRequest : class where TResponse : class;

    /// <summary>
    /// Execute a raw HTTP request with resilience patterns
    /// </summary>
    Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current client health status
    /// </summary>
    ApiClientHealth GetHealthStatus();
}

/// <summary>
/// API client health status
/// </summary>
public class ApiClientHealth
{
    public bool IsHealthy { get; set; }
    public string CircuitBreakerState { get; set; } = "Closed";
    public RateLimitStatus RateLimitStatus { get; set; } = new();
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public double SuccessRate => TotalRequests > 0 ? (double)SuccessfulRequests / TotalRequests * 100 : 0;
    public DateTime LastRequestAt { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
}

/// <summary>
/// Implementation of resilient APS API client
/// </summary>
public class ApsApiClient : IApsApiClient
{
    private readonly HttpClient _httpClient;
    private readonly IApsAuthenticationService _authService;
    private readonly IRateLimitService _rateLimitService;
    private readonly ApsApiOptions _options;
    private readonly ILogger<ApsApiClient> _logger;
    
    // private readonly ResilienceStrategy _resilienceStrategy; // TODO: Implement with Polly v8
    private readonly JsonSerializerOptions _jsonOptions;
    
    // Health tracking
    private volatile int _totalRequests;
    private volatile int _successfulRequests;
    private volatile int _failedRequests;
    private DateTime _lastRequestAt;
    private readonly List<TimeSpan> _responseTimes = new();
    private readonly object _healthLock = new();

    public ApsApiClient(
        HttpClient httpClient,
        IApsAuthenticationService authService,
        IRateLimitService rateLimitService,
        IOptions<ApsApiOptions> options,
        ILogger<ApsApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _rateLimitService = rateLimitService;
        _options = options.Value;
        _logger = logger;

        // Configure JSON serialization
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
        };

        // Configure resilience strategy
        // _resilienceStrategy = CreateResilienceStrategy(); // TODO: Implement with Polly v8

        _logger.LogInformation("APS API client initialized with base URL: {BaseUrl}", _options.BaseUrl);
    }

    public async Task<T?> GetAsync<T>(string endpoint, CancellationToken cancellationToken = default) where T : class
    {
        _logger.LogDebug("GET request to endpoint: {Endpoint}", endpoint);

        var request = new HttpRequestMessage(HttpMethod.Get, endpoint);
        var response = await SendAsync(request, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (string.IsNullOrEmpty(content))
            {
                _logger.LogWarning("Empty response content for endpoint: {Endpoint}", endpoint);
                return null;
            }

            try
            {
                var result = JsonSerializer.Deserialize<T>(content, _jsonOptions);
                _logger.LogDebug("Successfully deserialized response for endpoint: {Endpoint}", endpoint);
                return result;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize response for endpoint: {Endpoint}. Content: {Content}", 
                    endpoint, content[..Math.Min(500, content.Length)]);
                throw new InvalidOperationException($"Failed to deserialize API response: {ex.Message}", ex);
            }
        }

        await HandleErrorResponse(response, endpoint, cancellationToken);
        return null;
    }

    public async Task<TResponse?> PostAsync<TRequest, TResponse>(string endpoint, TRequest request, CancellationToken cancellationToken = default) 
        where TRequest : class where TResponse : class
    {
        _logger.LogDebug("POST request to endpoint: {Endpoint}", endpoint);

        var json = JsonSerializer.Serialize(request, _jsonOptions);
        var content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
        
        var httpRequest = new HttpRequestMessage(HttpMethod.Post, endpoint) { Content = content };
        var response = await SendAsync(httpRequest, cancellationToken);

        if (response.IsSuccessStatusCode)
        {
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
            
            if (string.IsNullOrEmpty(responseContent))
            {
                _logger.LogWarning("Empty response content for POST endpoint: {Endpoint}", endpoint);
                return null;
            }

            try
            {
                var result = JsonSerializer.Deserialize<TResponse>(responseContent, _jsonOptions);
                _logger.LogDebug("Successfully deserialized POST response for endpoint: {Endpoint}", endpoint);
                return result;
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize POST response for endpoint: {Endpoint}", endpoint);
                throw new InvalidOperationException($"Failed to deserialize API response: {ex.Message}", ex);
            }
        }

        await HandleErrorResponse(response, endpoint, cancellationToken);
        return null;
    }

    public async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Apply rate limiting
            await _rateLimitService.WaitForPermissionAsync(cancellationToken);

            // Add authentication header
            var accessToken = await _authService.GetAccessTokenAsync(cancellationToken);
            request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);

            // Add standard headers
            request.Headers.Add("User-Agent", "AccAuditTool/1.0");
            request.Headers.Add("Accept", "application/json");

            _logger.LogTrace("Sending {Method} request to {Uri}", request.Method, request.RequestUri);

            // Execute request with resilience strategy
            var response = await _httpClient.SendAsync(request, cancellationToken);

            // Update rate limits based on response
            _rateLimitService.UpdateLimitsFromResponse(response);

            // Track metrics
            TrackRequestMetrics(true, DateTime.UtcNow - startTime);

            _logger.LogTrace("Received {StatusCode} response from {Uri}", response.StatusCode, request.RequestUri);
            return response;
        }
        catch (Exception ex)
        {
            TrackRequestMetrics(false, DateTime.UtcNow - startTime);
            _logger.LogError(ex, "Error sending {Method} request to {Uri}", request.Method, request.RequestUri);
            throw;
        }
    }

    public ApiClientHealth GetHealthStatus()
    {
        lock (_healthLock)
        {
            return new ApiClientHealth
            {
                IsHealthy = _successfulRequests > 0 && (_failedRequests == 0 || (double)_successfulRequests / _totalRequests > 0.95),
                CircuitBreakerState = "Closed", // Simplified for now
                RateLimitStatus = _rateLimitService.GetStatus(),
                TotalRequests = _totalRequests,
                SuccessfulRequests = _successfulRequests,
                FailedRequests = _failedRequests,
                LastRequestAt = _lastRequestAt,
                AverageResponseTime = _responseTimes.Count > 0 ?
                    TimeSpan.FromMilliseconds(_responseTimes.Average(t => t.TotalMilliseconds)) :
                    TimeSpan.Zero
            };
        }
    }

    // TODO: Implement proper resilience strategy with Polly v8
    // private ResilienceStrategy CreateResilienceStrategy() { ... }

    private async Task<HttpRequestMessage> CloneRequestAsync(HttpRequestMessage request)
    {
        var clone = new HttpRequestMessage(request.Method, request.RequestUri);
        
        // Copy headers
        foreach (var header in request.Headers)
        {
            clone.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }

        // Copy content if present
        if (request.Content != null)
        {
            var content = await request.Content.ReadAsStringAsync();
            clone.Content = new StringContent(content, System.Text.Encoding.UTF8, request.Content.Headers.ContentType?.MediaType);
            
            // Copy content headers
            foreach (var header in request.Content.Headers)
            {
                clone.Content.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }
        }

        return clone;
    }

    private async Task HandleErrorResponse(HttpResponseMessage response, string endpoint, CancellationToken cancellationToken)
    {
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        
        _logger.LogError("API request failed. Endpoint: {Endpoint}, Status: {StatusCode}, Content: {Content}",
            endpoint, response.StatusCode, content);

        var errorMessage = $"API request failed with status {response.StatusCode}";
        
        if (!string.IsNullOrEmpty(content))
        {
            try
            {
                var errorResponse = JsonSerializer.Deserialize<JsonElement>(content);
                if (errorResponse.TryGetProperty("error", out var error))
                {
                    errorMessage = error.GetString() ?? errorMessage;
                }
            }
            catch
            {
                // Ignore JSON parsing errors for error responses
            }
        }

        throw new HttpRequestException(errorMessage, null, response.StatusCode);
    }

    private void TrackRequestMetrics(bool success, TimeSpan responseTime)
    {
        lock (_healthLock)
        {
            _totalRequests++;
            _lastRequestAt = DateTime.UtcNow;
            
            if (success)
                _successfulRequests++;
            else
                _failedRequests++;

            _responseTimes.Add(responseTime);
            
            // Keep only last 100 response times for average calculation
            if (_responseTimes.Count > 100)
            {
                _responseTimes.RemoveAt(0);
            }
        }
    }
}
