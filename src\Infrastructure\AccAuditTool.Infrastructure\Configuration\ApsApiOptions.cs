namespace AccAuditTool.Infrastructure.Configuration;

/// <summary>
/// Configuration options for Autodesk APS API integration
/// </summary>
public class ApsApiOptions
{
    public const string SectionName = "AutodeskAPS";

    /// <summary>
    /// Base URL for APS API (e.g., https://developer.api.autodesk.com)
    /// </summary>
    public string BaseUrl { get; set; } = "https://developer.api.autodesk.com";

    /// <summary>
    /// OAuth 2.0 Client ID from APS application
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// OAuth 2.0 Client Secret from APS application
    /// </summary>
    public string ClientSecret { get; set; } = string.Empty;

    /// <summary>
    /// OAuth 2.0 callback URL for authorization code flow
    /// </summary>
    public string CallbackUrl { get; set; } = string.Empty;

    /// <summary>
    /// Required scopes for ACC data access
    /// </summary>
    public List<string> Scopes { get; set; } = new() { "data:read", "account:read" };

    /// <summary>
    /// Rate limiting configuration
    /// </summary>
    public RateLimitOptions RateLimit { get; set; } = new();

    /// <summary>
    /// Retry policy configuration
    /// </summary>
    public RetryOptions Retry { get; set; } = new();

    /// <summary>
    /// Circuit breaker configuration
    /// </summary>
    public CircuitBreakerOptions CircuitBreaker { get; set; } = new();

    /// <summary>
    /// Timeout configuration
    /// </summary>
    public TimeoutOptions Timeout { get; set; } = new();
}

/// <summary>
/// Rate limiting configuration
/// </summary>
public class RateLimitOptions
{
    /// <summary>
    /// Maximum requests per minute
    /// </summary>
    public int RequestsPerMinute { get; set; } = 100;

    /// <summary>
    /// Burst capacity for short-term spikes
    /// </summary>
    public int BurstCapacity { get; set; } = 20;

    /// <summary>
    /// Enable adaptive rate limiting based on API responses
    /// </summary>
    public bool EnableAdaptiveRateLimit { get; set; } = true;
}

/// <summary>
/// Retry policy configuration
/// </summary>
public class RetryOptions
{
    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    public int MaxRetryAttempts { get; set; } = 3;

    /// <summary>
    /// Base delay between retries in milliseconds
    /// </summary>
    public int BaseDelayMs { get; set; } = 1000;

    /// <summary>
    /// Maximum delay between retries in milliseconds
    /// </summary>
    public int MaxDelayMs { get; set; } = 30000;

    /// <summary>
    /// Enable exponential backoff
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// Enable jitter to avoid thundering herd
    /// </summary>
    public bool UseJitter { get; set; } = true;
}

/// <summary>
/// Circuit breaker configuration
/// </summary>
public class CircuitBreakerOptions
{
    /// <summary>
    /// Number of consecutive failures before opening circuit
    /// </summary>
    public int FailureThreshold { get; set; } = 5;

    /// <summary>
    /// Duration to keep circuit open in seconds
    /// </summary>
    public int DurationOfBreakInSeconds { get; set; } = 30;

    /// <summary>
    /// Minimum throughput before circuit breaker activates
    /// </summary>
    public int MinimumThroughput { get; set; } = 10;
}

/// <summary>
/// Timeout configuration
/// </summary>
public class TimeoutOptions
{
    /// <summary>
    /// HTTP request timeout in seconds
    /// </summary>
    public int RequestTimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Overall operation timeout in seconds
    /// </summary>
    public int OperationTimeoutSeconds { get; set; } = 300;
}
