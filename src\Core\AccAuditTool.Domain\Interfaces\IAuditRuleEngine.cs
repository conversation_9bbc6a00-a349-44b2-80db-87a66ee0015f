using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Domain.Interfaces;

/// <summary>
/// Interface for the audit rule engine
/// </summary>
public interface IAuditRuleEngine
{
    /// <summary>
    /// Execute audit rules against a project
    /// </summary>
    Task<IEnumerable<AuditFinding>> ExecuteRulesAsync(
        Guid projectId,
        AuditConfiguration configuration,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute audit rules against an account
    /// </summary>
    Task<IEnumerable<AuditFinding>> ExecuteRulesAsync(
        Guid accountId,
        AuditConfiguration configuration,
        bool includeAllProjects,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Execute a specific rule
    /// </summary>
    Task<IEnumerable<AuditFinding>> ExecuteRuleAsync(
        AuditRule rule,
        Guid? projectId = null,
        Guid? accountId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate rule configuration
    /// </summary>
    Task<bool> ValidateRuleAsync(AuditRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available rule implementations
    /// </summary>
    Task<IEnumerable<string>> GetAvailableRuleImplementationsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Context provided to audit rules during execution
/// </summary>
public class AuditRuleContext
{
    public Guid AuditRunId { get; set; }
    public Guid? ProjectId { get; set; }
    public Guid? AccountId { get; set; }
    public string? Configuration { get; set; }
    public FindingSeverity? OverrideSeverity { get; set; }
    public IUnitOfWork UnitOfWork { get; set; } = null!;
}
