namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an audit finding/violation
/// </summary>
public class AuditFinding : BaseEntity
{
    /// <summary>
    /// Audit run that generated this finding
    /// </summary>
    public Guid AuditRunId { get; set; }
    public AuditRun AuditRun { get; set; } = null!;

    /// <summary>
    /// Audit rule that was violated
    /// </summary>
    public Guid AuditRuleId { get; set; }
    public AuditRule AuditRule { get; set; } = null!;

    /// <summary>
    /// Finding severity level
    /// </summary>
    public FindingSeverity Severity { get; set; }

    /// <summary>
    /// Risk score for this finding (0-100)
    /// </summary>
    public int RiskScore { get; set; }

    /// <summary>
    /// Finding title/summary
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Detailed description of the finding
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// Recommended remediation action
    /// </summary>
    public string Recommendation { get; set; } = string.Empty;

    /// <summary>
    /// User affected by this finding (if applicable)
    /// </summary>
    public Guid? AffectedUserId { get; set; }
    public User? AffectedUser { get; set; }

    /// <summary>
    /// Resource affected by this finding (if applicable)
    /// </summary>
    public Guid? AffectedResourceId { get; set; }
    public Resource? AffectedResource { get; set; }

    /// <summary>
    /// Permission affected by this finding (if applicable)
    /// </summary>
    public Guid? AffectedPermissionId { get; set; }
    public Permission? AffectedPermission { get; set; }

    /// <summary>
    /// ACC resource identifier for the affected resource
    /// </summary>
    public string? AccResourceId { get; set; }

    /// <summary>
    /// Finding status
    /// </summary>
    public FindingStatus Status { get; set; } = FindingStatus.Open;

    /// <summary>
    /// Date when the finding was resolved
    /// </summary>
    public DateTime? ResolvedAt { get; set; }

    /// <summary>
    /// User who resolved the finding
    /// </summary>
    public string? ResolvedBy { get; set; }

    /// <summary>
    /// Resolution notes
    /// </summary>
    public string? ResolutionNotes { get; set; }

    /// <summary>
    /// Additional metadata as JSON
    /// </summary>
    public string? Metadata { get; set; }
}

/// <summary>
/// Finding severity enumeration
/// </summary>
public enum FindingSeverity
{
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Finding status enumeration
/// </summary>
public enum FindingStatus
{
    Open,
    InProgress,
    Resolved,
    Dismissed,
    FalsePositive
}
