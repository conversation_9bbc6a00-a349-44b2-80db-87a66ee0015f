namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an ACC project
/// </summary>
public class Project : BaseEntity
{
    /// <summary>
    /// ACC project identifier from Autodesk system
    /// </summary>
    public string AccProjectId { get; set; } = string.Empty;

    /// <summary>
    /// Project name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Project description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// ACC account this project belongs to
    /// </summary>
    public Guid AccountId { get; set; }
    public Account Account { get; set; } = null!;

    /// <summary>
    /// Project status
    /// </summary>
    public ProjectStatus Status { get; set; } = ProjectStatus.Active;

    /// <summary>
    /// Project type (e.g., Building, Infrastructure, etc.)
    /// </summary>
    public string? ProjectType { get; set; }

    /// <summary>
    /// Project start date
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Project end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Project location
    /// </summary>
    public string? Location { get; set; }

    /// <summary>
    /// User roles assigned to this project
    /// </summary>
    public ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();

    /// <summary>
    /// Companies involved in this project
    /// </summary>
    public ICollection<ProjectCompany> ProjectCompanies { get; set; } = new List<ProjectCompany>();

    /// <summary>
    /// Permissions defined for this project
    /// </summary>
    public ICollection<Permission> Permissions { get; set; } = new List<Permission>();

    /// <summary>
    /// Audit runs performed on this project
    /// </summary>
    public ICollection<AuditRun> AuditRuns { get; set; } = new List<AuditRun>();

    /// <summary>
    /// Resources (folders, files) in this project
    /// </summary>
    public ICollection<Resource> Resources { get; set; } = new List<Resource>();
}

/// <summary>
/// Project status enumeration
/// </summary>
public enum ProjectStatus
{
    Active,
    Inactive,
    Completed,
    Archived,
    Suspended
}
