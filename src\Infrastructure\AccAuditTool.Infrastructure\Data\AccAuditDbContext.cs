using AccAuditTool.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace AccAuditTool.Infrastructure.Data;

/// <summary>
/// Entity Framework database context for ACC Audit Tool
/// </summary>
public class AccAuditDbContext : DbContext
{
    public AccAuditDbContext(DbContextOptions<AccAuditDbContext> options) : base(options)
    {
    }

    // Core entities
    public DbSet<User> Users { get; set; }
    public DbSet<Company> Companies { get; set; }
    public DbSet<Project> Projects { get; set; }
    public DbSet<Account> Accounts { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<Resource> Resources { get; set; }

    // Relationship entities
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<ProjectCompany> ProjectCompanies { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }

    // Audit entities
    public DbSet<AuditRun> AuditRuns { get; set; }
    public DbSet<AuditFinding> AuditFindings { get; set; }
    public DbSet<AuditRule> AuditRules { get; set; }
    public DbSet<AuditConfiguration> AuditConfigurations { get; set; }
    public DbSet<AuditConfigurationRule> AuditConfigurationRules { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure entity relationships and constraints
        ConfigureUserEntities(modelBuilder);
        ConfigureProjectEntities(modelBuilder);
        ConfigurePermissionEntities(modelBuilder);
        ConfigureAuditEntities(modelBuilder);
        ConfigureIndexes(modelBuilder);
    }

    private static void ConfigureUserEntities(ModelBuilder modelBuilder)
    {
        // User configuration
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccUserId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Email).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.FirstName).HasMaxLength(100);
            entity.Property(e => e.LastName).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasIndex(e => e.AccUserId).IsUnique();
            entity.HasIndex(e => e.Email);

            entity.HasOne(e => e.Company)
                .WithMany(e => e.Users)
                .HasForeignKey(e => e.CompanyId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Company configuration
        modelBuilder.Entity<Company>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccCompanyId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Trade).HasMaxLength(100);
            entity.Property(e => e.Address).HasMaxLength(500);
            entity.Property(e => e.Phone).HasMaxLength(50);
            entity.Property(e => e.Website).HasMaxLength(255);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasIndex(e => e.AccCompanyId).IsUnique();
        });

        // UserRole configuration
        modelBuilder.Entity<UserRole>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AssignedBy).HasMaxLength(255);
            entity.Property(e => e.AssignmentReason).HasMaxLength(500);

            entity.HasOne(e => e.User)
                .WithMany(e => e.UserRoles)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Role)
                .WithMany(e => e.UserRoles)
                .HasForeignKey(e => e.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Project)
                .WithMany(e => e.UserRoles)
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.UserId, e.RoleId, e.ProjectId }).IsUnique();
        });
    }

    private static void ConfigureProjectEntities(ModelBuilder modelBuilder)
    {
        // Account configuration
        modelBuilder.Entity<Account>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccAccountId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Region).HasMaxLength(100);
            entity.Property(e => e.SubscriptionType).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasIndex(e => e.AccAccountId).IsUnique();
        });

        // Project configuration
        modelBuilder.Entity<Project>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccProjectId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.ProjectType).HasMaxLength(100);
            entity.Property(e => e.Location).HasMaxLength(255);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasIndex(e => e.AccProjectId).IsUnique();

            entity.HasOne(e => e.Account)
                .WithMany(e => e.Projects)
                .HasForeignKey(e => e.AccountId)
                .OnDelete(DeleteBehavior.Cascade);
        });

        // ProjectCompany configuration
        modelBuilder.Entity<ProjectCompany>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.ProjectRole).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();

            entity.HasOne(e => e.Project)
                .WithMany(e => e.ProjectCompanies)
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.Company)
                .WithMany(e => e.ProjectCompanies)
                .HasForeignKey(e => e.CompanyId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.ProjectId, e.CompanyId }).IsUnique();
        });
    }

    private static void ConfigurePermissionEntities(ModelBuilder modelBuilder)
    {
        // Role configuration
        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccRoleId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Type).HasConversion<string>();
            entity.Property(e => e.Level).HasConversion<string>();
            entity.Property(e => e.ServiceKey).HasMaxLength(50);
            entity.Property(e => e.Version).HasMaxLength(20);
            entity.Property(e => e.Tags).HasMaxLength(500);
            entity.Property(e => e.ComplianceFrameworks).HasMaxLength(500);

            entity.HasIndex(e => e.AccRoleId).IsUnique();
        });

        // Permission configuration
        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccResourceId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Actions).IsRequired();
            entity.Property(e => e.ResourceType).HasConversion<string>();
            entity.Property(e => e.Source).HasConversion<string>();
            entity.Property(e => e.InheritedFrom).HasMaxLength(100);
            entity.Property(e => e.GrantedBy).HasMaxLength(255);

            entity.HasOne(e => e.Project)
                .WithMany(e => e.Permissions)
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.User)
                .WithMany(e => e.DirectPermissions)
                .HasForeignKey(e => e.UserId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.Role)
                .WithMany()
                .HasForeignKey(e => e.RoleId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.Company)
                .WithMany(e => e.CompanyPermissions)
                .HasForeignKey(e => e.CompanyId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.Resource)
                .WithMany(e => e.Permissions)
                .HasForeignKey(e => e.ResourceId)
                .OnDelete(DeleteBehavior.SetNull);
        });

        // Resource configuration
        modelBuilder.Entity<Resource>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.AccResourceId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Path).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Type).HasConversion<string>();
            entity.Property(e => e.FileExtension).HasMaxLength(10);
            entity.Property(e => e.Version).HasMaxLength(50);
            entity.Property(e => e.LastModifiedBy).HasMaxLength(255);

            entity.HasOne(e => e.Project)
                .WithMany(e => e.Resources)
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.ParentResource)
                .WithMany(e => e.ChildResources)
                .HasForeignKey(e => e.ParentResourceId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => e.AccResourceId);
            entity.HasIndex(e => new { e.ProjectId, e.Path });
        });

        // RolePermission configuration
        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Action).IsRequired().HasMaxLength(100);
            entity.Property(e => e.ResourceType).HasConversion<string>();
            entity.Property(e => e.Description).HasMaxLength(500);

            entity.HasOne(e => e.Role)
                .WithMany(e => e.RolePermissions)
                .HasForeignKey(e => e.RoleId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.RoleId, e.Action, e.ResourceType }).IsUnique();
        });
    }

    private static void ConfigureAuditEntities(ModelBuilder modelBuilder)
    {
        // AuditRule configuration
        modelBuilder.Entity<AuditRule>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.RuleId).IsRequired().HasMaxLength(100);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).IsRequired().HasMaxLength(1000);
            entity.Property(e => e.Category).IsRequired().HasMaxLength(100);
            entity.Property(e => e.DefaultSeverity).HasConversion<string>();
            entity.Property(e => e.Type).HasConversion<string>();
            entity.Property(e => e.ImplementationClass).HasMaxLength(255);
            entity.Property(e => e.Version).HasMaxLength(20);
            entity.Property(e => e.Tags).HasMaxLength(500);
            entity.Property(e => e.ComplianceFrameworks).HasMaxLength(500);

            entity.HasIndex(e => e.RuleId).IsUnique();
            entity.HasIndex(e => e.Category);
        });

        // AuditConfiguration configuration
        modelBuilder.Entity<AuditConfiguration>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).HasMaxLength(1000);
            entity.Property(e => e.Schedule).HasMaxLength(100);

            entity.HasOne(e => e.Account)
                .WithMany(e => e.AuditConfigurations)
                .HasForeignKey(e => e.AccountId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.AccountId, e.Name }).IsUnique();
        });

        // AuditConfigurationRule configuration
        modelBuilder.Entity<AuditConfigurationRule>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.OverrideSeverity).HasConversion<string>();

            entity.HasOne(e => e.AuditConfiguration)
                .WithMany(e => e.AuditConfigurationRules)
                .HasForeignKey(e => e.AuditConfigurationId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.AuditRule)
                .WithMany(e => e.AuditConfigurationRules)
                .HasForeignKey(e => e.AuditRuleId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasIndex(e => new { e.AuditConfigurationId, e.AuditRuleId }).IsUnique();
        });

        // AuditRun configuration
        modelBuilder.Entity<AuditRun>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Type).HasConversion<string>();
            entity.Property(e => e.InitiatedBy).HasMaxLength(255);
            entity.Property(e => e.ErrorMessage).HasMaxLength(2000);

            entity.HasOne(e => e.Project)
                .WithMany(e => e.AuditRuns)
                .HasForeignKey(e => e.ProjectId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.Account)
                .WithMany()
                .HasForeignKey(e => e.AccountId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.AuditConfiguration)
                .WithMany(e => e.AuditRuns)
                .HasForeignKey(e => e.AuditConfigurationId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasIndex(e => e.StartedAt);
            entity.HasIndex(e => e.Status);
        });

        // AuditFinding configuration
        modelBuilder.Entity<AuditFinding>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Severity).HasConversion<string>();
            entity.Property(e => e.Title).IsRequired().HasMaxLength(255);
            entity.Property(e => e.Description).IsRequired().HasMaxLength(2000);
            entity.Property(e => e.Recommendation).IsRequired().HasMaxLength(2000);
            entity.Property(e => e.AccResourceId).HasMaxLength(100);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.ResolvedBy).HasMaxLength(255);
            entity.Property(e => e.ResolutionNotes).HasMaxLength(1000);

            entity.HasOne(e => e.AuditRun)
                .WithMany(e => e.AuditFindings)
                .HasForeignKey(e => e.AuditRunId)
                .OnDelete(DeleteBehavior.Cascade);

            entity.HasOne(e => e.AuditRule)
                .WithMany(e => e.AuditFindings)
                .HasForeignKey(e => e.AuditRuleId)
                .OnDelete(DeleteBehavior.Restrict);

            entity.HasOne(e => e.AffectedUser)
                .WithMany(e => e.AuditFindings)
                .HasForeignKey(e => e.AffectedUserId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.AffectedResource)
                .WithMany()
                .HasForeignKey(e => e.AffectedResourceId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasOne(e => e.AffectedPermission)
                .WithMany()
                .HasForeignKey(e => e.AffectedPermissionId)
                .OnDelete(DeleteBehavior.SetNull);

            entity.HasIndex(e => e.Severity);
            entity.HasIndex(e => e.Status);
            entity.HasIndex(e => e.RiskScore);
        });
    }

    private static void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // Additional performance indexes
        modelBuilder.Entity<Permission>()
            .HasIndex(e => new { e.ProjectId, e.UserId, e.ResourceType });

        modelBuilder.Entity<Permission>()
            .HasIndex(e => new { e.ProjectId, e.Source, e.ResourceType });

        modelBuilder.Entity<AuditFinding>()
            .HasIndex(e => new { e.AuditRunId, e.Severity, e.Status });

        modelBuilder.Entity<User>()
            .HasIndex(e => new { e.Status, e.LastSeenAt });

        modelBuilder.Entity<Project>()
            .HasIndex(e => new { e.AccountId, e.Status });
    }
}
