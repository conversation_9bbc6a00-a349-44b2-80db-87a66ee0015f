@using AccAuditTool.Domain.Entities
@inject IJSRuntime JSRuntime

<div class="findings-severity-chart">
    <canvas id="findingsSeverityChart-@ChartId" width="300" height="300"></canvas>
    
    @if (Data.Any())
    {
        <div class="severity-legend">
            @foreach (var item in Data.OrderByDescending(d => (int)d.Key))
            {
                <div class="legend-item">
                    <span class="legend-color" style="background-color: @GetSeverityColor(item.Key)"></span>
                    <span class="legend-label">@item.Key</span>
                    <span class="legend-value">@item.Value</span>
                </div>
            }
        </div>
    }
    else
    {
        <div class="no-data-message">
            <i class="fas fa-chart-pie text-muted"></i>
            <p class="text-muted">No findings data available</p>
        </div>
    }
</div>

@code {
    [Parameter] public Dictionary<AuditSeverity, int> Data { get; set; } = new();
    
    private string ChartId = Guid.NewGuid().ToString("N")[..8];
    private bool ChartInitialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender || !ChartInitialized)
        {
            await InitializeChart();
            ChartInitialized = true;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ChartInitialized)
        {
            await UpdateChart();
        }
    }

    private async Task InitializeChart()
    {
        if (!Data.Any())
        {
            return;
        }

        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("initializeFindingsSeverityChart", $"findingsSeverityChart-{ChartId}", chartData);
    }

    private async Task UpdateChart()
    {
        if (!Data.Any())
        {
            return;
        }

        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("updateFindingsSeverityChart", $"findingsSeverityChart-{ChartId}", chartData);
    }

    private object PrepareChartData()
    {
        var sortedData = Data.OrderByDescending(d => (int)d.Key).ToList();
        
        return new
        {
            labels = sortedData.Select(d => d.Key.ToString()).ToArray(),
            datasets = new[]
            {
                new
                {
                    data = sortedData.Select(d => d.Value).ToArray(),
                    backgroundColor = sortedData.Select(d => GetSeverityColor(d.Key)).ToArray(),
                    borderColor = sortedData.Select(d => GetSeverityBorderColor(d.Key)).ToArray(),
                    borderWidth = 2,
                    hoverOffset = 4
                }
            }
        };
    }

    private string GetSeverityColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#dc3545",
            AuditSeverity.High => "#fd7e14",
            AuditSeverity.Medium => "#ffc107",
            AuditSeverity.Low => "#20c997",
            AuditSeverity.Info => "#0dcaf0",
            _ => "#6c757d"
        };
    }

    private string GetSeverityBorderColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#b02a37",
            AuditSeverity.High => "#e8681a",
            AuditSeverity.Medium => "#e6ac00",
            AuditSeverity.Low => "#1aa179",
            AuditSeverity.Info => "#0bb5d6",
            _ => "#5a6268"
        };
    }

    private int GetTotalFindings()
    {
        return Data.Values.Sum();
    }
}

<script>
    window.initializeFindingsSeverityChart = function (canvasId, data) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false // We'll use custom legend
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });

        // Store chart instance for updates
        window[`chart_${canvasId}`] = chart;
    };

    window.updateFindingsSeverityChart = function (canvasId, data) {
        const chart = window[`chart_${canvasId}`];
        if (chart) {
            chart.data = data;
            chart.update();
        }
    };
</script>

<style>
    .findings-severity-chart {
        position: relative;
        height: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .findings-severity-chart canvas {
        max-height: 200px;
        margin-bottom: 20px;
    }

    .severity-legend {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
        max-width: 200px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        flex-shrink: 0;
    }

    .legend-label {
        flex: 1;
        font-weight: 500;
    }

    .legend-value {
        font-weight: 600;
        color: #495057;
        min-width: 30px;
        text-align: right;
    }

    .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: 10px;
    }

    .no-data-message i {
        font-size: 3rem;
    }

    .no-data-message p {
        margin: 0;
        font-size: 0.875rem;
    }
</style>
