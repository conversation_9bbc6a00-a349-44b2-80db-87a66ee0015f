using AccAuditTool.Infrastructure.Exceptions;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for centralized error handling and logging
/// </summary>
public interface IErrorHandlingService
{
    /// <summary>
    /// Handle HTTP response errors and convert to appropriate exceptions
    /// </summary>
    Task<Exception> HandleHttpErrorAsync(HttpResponseMessage response, string operation, CancellationToken cancellationToken = default);

    /// <summary>
    /// Handle general exceptions with context
    /// </summary>
    void HandleException(Exception exception, string operation, object? context = null);

    /// <summary>
    /// Log error with structured data
    /// </summary>
    void LogError(Exception exception, string operation, object? context = null);

    /// <summary>
    /// Determine if an exception is retryable
    /// </summary>
    bool IsRetryableException(Exception exception);

    /// <summary>
    /// Get user-friendly error message
    /// </summary>
    string GetUserFriendlyMessage(Exception exception);
}

/// <summary>
/// Implementation of error handling service
/// </summary>
public class ErrorHandlingService : IErrorHandlingService
{
    private readonly ILogger<ErrorHandlingService> _logger;

    public ErrorHandlingService(ILogger<ErrorHandlingService> logger)
    {
        _logger = logger;
    }

    public async Task<Exception> HandleHttpErrorAsync(HttpResponseMessage response, string operation, CancellationToken cancellationToken = default)
    {
        var content = await response.Content.ReadAsStringAsync(cancellationToken);
        var requestId = GetRequestId(response);

        _logger.LogError("HTTP error during {Operation}. Status: {StatusCode}, Content: {Content}, RequestId: {RequestId}",
            operation, response.StatusCode, content, requestId);

        return response.StatusCode switch
        {
            HttpStatusCode.Unauthorized => new ApsAuthenticationException(
                "Authentication failed. Please check your credentials."),
            
            HttpStatusCode.Forbidden => new ApsApiException(
                "Access forbidden. Insufficient permissions for this operation.", 
                response.StatusCode, null, requestId),
            
            HttpStatusCode.TooManyRequests => CreateRateLimitException(response, content),
            
            HttpStatusCode.BadRequest => CreateBadRequestException(content, response.StatusCode, requestId),
            
            HttpStatusCode.NotFound => new ApsApiException(
                "The requested resource was not found.", 
                response.StatusCode, null, requestId),
            
            HttpStatusCode.InternalServerError => new ApsApiException(
                "Internal server error occurred. Please try again later.", 
                response.StatusCode, null, requestId),
            
            HttpStatusCode.BadGateway or 
            HttpStatusCode.ServiceUnavailable or 
            HttpStatusCode.GatewayTimeout => new ApsApiException(
                "Service temporarily unavailable. Please try again later.", 
                response.StatusCode, null, requestId),
            
            _ => new ApsApiException(
                $"Unexpected HTTP error: {response.StatusCode}", 
                response.StatusCode, null, requestId)
        };
    }

    public void HandleException(Exception exception, string operation, object? context = null)
    {
        LogError(exception, operation, context);

        // Additional handling based on exception type
        switch (exception)
        {
            case ApsRateLimitException rateLimitEx:
                _logger.LogWarning("Rate limit exceeded during {Operation}. Retry after: {RetryAfter}", 
                    operation, rateLimitEx.RetryAfter);
                break;

            case ApsAuthenticationException authEx:
                _logger.LogError("Authentication failed during {Operation}: {Message}", 
                    operation, authEx.Message);
                break;

            case DataSyncException syncEx:
                _logger.LogError("Data sync failed during {Operation} for {EntityType} {EntityId}: {Message}", 
                    operation, syncEx.EntityType, syncEx.EntityId, syncEx.Message);
                break;

            case ConfigurationException configEx:
                _logger.LogError("Configuration error during {Operation} for key {ConfigKey}: {Message}", 
                    operation, configEx.ConfigurationKey, configEx.Message);
                break;
        }
    }

    public void LogError(Exception exception, string operation, object? context = null)
    {
        var contextJson = context != null ? JsonSerializer.Serialize(context) : null;
        
        _logger.LogError(exception, 
            "Error during {Operation}. Exception: {ExceptionType}, Message: {Message}, Context: {Context}",
            operation, exception.GetType().Name, exception.Message, contextJson);

        // Log inner exceptions
        var innerException = exception.InnerException;
        var depth = 1;
        while (innerException != null && depth <= 3)
        {
            _logger.LogError("Inner exception {Depth}: {ExceptionType} - {Message}",
                depth, innerException.GetType().Name, innerException.Message);
            innerException = innerException.InnerException;
            depth++;
        }
    }

    public bool IsRetryableException(Exception exception)
    {
        return exception switch
        {
            HttpRequestException => true,
            TaskCanceledException => true,
            ApsRateLimitException => true,
            ApsApiException apiEx when IsRetryableStatusCode(apiEx.StatusCode) => true,
            DataSyncException => false, // Data sync errors usually need manual intervention
            ApsAuthenticationException => false, // Auth errors need credential fix
            ConfigurationException => false, // Config errors need manual fix
            _ => false
        };
    }

    public string GetUserFriendlyMessage(Exception exception)
    {
        return exception switch
        {
            ApsAuthenticationException => "Authentication failed. Please check your Autodesk credentials and try again.",
            ApsRateLimitException rateLimitEx => $"Rate limit exceeded. Please wait {rateLimitEx.RetryAfter?.TotalMinutes:F0} minutes before trying again.",
            ApsApiException apiEx when apiEx.StatusCode == HttpStatusCode.Forbidden => "You don't have permission to access this resource.",
            ApsApiException apiEx when apiEx.StatusCode == HttpStatusCode.NotFound => "The requested resource was not found.",
            DataSyncException syncEx => $"Data synchronization failed for {syncEx.EntityType}. Please check the logs for details.",
            ConfigurationException configEx => $"Configuration error: {configEx.ConfigurationKey}. Please check your settings.",
            AuditException auditEx => $"Audit operation failed. Please check the audit configuration and try again.",
            HttpRequestException => "Network error occurred. Please check your internet connection and try again.",
            TaskCanceledException => "The operation timed out. Please try again.",
            _ => "An unexpected error occurred. Please contact support if the problem persists."
        };
    }

    private static ApsRateLimitException CreateRateLimitException(HttpResponseMessage response, string content)
    {
        TimeSpan? retryAfter = null;
        int? remainingRequests = null;

        // Parse Retry-After header
        if (response.Headers.RetryAfter != null)
        {
            retryAfter = response.Headers.RetryAfter.Delta;
        }

        // Parse rate limit headers
        if (response.Headers.TryGetValues("X-RateLimit-Remaining", out var remainingValues))
        {
            if (int.TryParse(remainingValues.FirstOrDefault(), out var remaining))
            {
                remainingRequests = remaining;
            }
        }

        var message = "Rate limit exceeded";
        if (retryAfter.HasValue)
        {
            message += $". Retry after {retryAfter.Value.TotalSeconds} seconds";
        }

        return new ApsRateLimitException(message, retryAfter, remainingRequests);
    }

    private static ApsApiException CreateBadRequestException(string content, HttpStatusCode statusCode, string? requestId)
    {
        var message = "Bad request";
        string? errorCode = null;

        try
        {
            var errorResponse = JsonSerializer.Deserialize<JsonElement>(content);
            if (errorResponse.TryGetProperty("error", out var error))
            {
                message = error.GetString() ?? message;
            }
            if (errorResponse.TryGetProperty("error_code", out var code))
            {
                errorCode = code.GetString();
            }
        }
        catch
        {
            // Ignore JSON parsing errors
        }

        return new ApsApiException(message, statusCode, errorCode, requestId);
    }

    private static string? GetRequestId(HttpResponseMessage response)
    {
        response.Headers.TryGetValues("X-Request-Id", out var requestIdValues);
        return requestIdValues?.FirstOrDefault();
    }

    private static bool IsRetryableStatusCode(HttpStatusCode? statusCode)
    {
        return statusCode switch
        {
            HttpStatusCode.InternalServerError => true,
            HttpStatusCode.BadGateway => true,
            HttpStatusCode.ServiceUnavailable => true,
            HttpStatusCode.GatewayTimeout => true,
            HttpStatusCode.TooManyRequests => true,
            _ => false
        };
    }
}
