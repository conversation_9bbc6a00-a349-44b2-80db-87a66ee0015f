using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace AccAuditTool.Infrastructure.Validation;

/// <summary>
/// Service for validating data integrity and business rules
/// </summary>
public interface IDataValidationService
{
    /// <summary>
    /// Validate ACC data before synchronization
    /// </summary>
    Task<ValidationResult> ValidateAccDataAsync<T>(T data, string entityType, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate domain entity before persistence
    /// </summary>
    Task<ValidationResult> ValidateEntityAsync<T>(T entity, CancellationToken cancellationToken = default) where T : BaseEntity;

    /// <summary>
    /// Validate data consistency across related entities
    /// </summary>
    Task<ValidationResult> ValidateDataConsistencyAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate business rules for audit operations
    /// </summary>
    Task<ValidationResult> ValidateAuditRulesAsync(AuditConfiguration configuration, CancellationToken cancellationToken = default);

    /// <summary>
    /// Perform comprehensive data integrity check
    /// </summary>
    Task<DataIntegrityReport> PerformDataIntegrityCheckAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Validation result with detailed error information
/// </summary>
public class ValidationResult
{
    public bool IsValid => !Errors.Any() && !Warnings.Any();
    public List<ValidationError> Errors { get; set; } = new();
    public List<ValidationWarning> Warnings { get; set; } = new();
    public string EntityType { get; set; } = string.Empty;
    public string? EntityId { get; set; }
    public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Validation error details
/// </summary>
public class ValidationError
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string PropertyName { get; set; } = string.Empty;
    public object? AttemptedValue { get; set; }
    public ValidationSeverity Severity { get; set; } = ValidationSeverity.Error;
}

/// <summary>
/// Validation warning details
/// </summary>
public class ValidationWarning
{
    public string Code { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string PropertyName { get; set; } = string.Empty;
    public object? Value { get; set; }
}

/// <summary>
/// Validation severity levels
/// </summary>
public enum ValidationSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// Comprehensive data integrity report
/// </summary>
public class DataIntegrityReport
{
    public bool IsHealthy => !Issues.Any(i => i.Severity >= ValidationSeverity.Error);
    public List<DataIntegrityIssue> Issues { get; set; } = new();
    public Dictionary<string, int> EntityCounts { get; set; } = new();
    public Dictionary<string, List<string>> OrphanedRecords { get; set; } = new();
    public Dictionary<string, List<string>> DuplicateRecords { get; set; } = new();
    public DateTime GeneratedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan GenerationTime { get; set; }
}

/// <summary>
/// Data integrity issue
/// </summary>
public class DataIntegrityIssue
{
    public string Code { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public ValidationSeverity Severity { get; set; }
    public string EntityType { get; set; } = string.Empty;
    public string? EntityId { get; set; }
    public string? RecommendedAction { get; set; }
}

/// <summary>
/// Implementation of data validation service
/// </summary>
public class DataValidationService : IDataValidationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<DataValidationService> _logger;

    // Validation patterns
    private static readonly Regex EmailPattern = new(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.Compiled);
    private static readonly Regex AccIdPattern = new(@"^[a-zA-Z0-9\-_]{1,100}$", RegexOptions.Compiled);

    public DataValidationService(IUnitOfWork unitOfWork, ILogger<DataValidationService> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<ValidationResult> ValidateAccDataAsync<T>(T data, string entityType, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult { EntityType = entityType };

        try
        {
            _logger.LogDebug("Validating ACC data for entity type: {EntityType}", entityType);

            switch (entityType.ToLowerInvariant())
            {
                case "account":
                    await ValidateAccountDataAsync(data, result, cancellationToken);
                    break;
                case "project":
                    await ValidateProjectDataAsync(data, result, cancellationToken);
                    break;
                case "user":
                    await ValidateUserDataAsync(data, result, cancellationToken);
                    break;
                case "company":
                    await ValidateCompanyDataAsync(data, result, cancellationToken);
                    break;
                case "permission":
                    await ValidatePermissionDataAsync(data, result, cancellationToken);
                    break;
                default:
                    result.Warnings.Add(new ValidationWarning
                    {
                        Code = "UNKNOWN_ENTITY_TYPE",
                        Message = $"Unknown entity type: {entityType}",
                        PropertyName = "EntityType"
                    });
                    break;
            }

            _logger.LogDebug("ACC data validation completed for {EntityType}. Errors: {ErrorCount}, Warnings: {WarningCount}",
                entityType, result.Errors.Count, result.Warnings.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during ACC data validation for {EntityType}", entityType);
            result.Errors.Add(new ValidationError
            {
                Code = "VALIDATION_ERROR",
                Message = $"Validation failed: {ex.Message}",
                Severity = ValidationSeverity.Critical
            });
        }

        return result;
    }

    public async Task<ValidationResult> ValidateEntityAsync<T>(T entity, CancellationToken cancellationToken = default) where T : BaseEntity
    {
        var result = new ValidationResult 
        { 
            EntityType = typeof(T).Name,
            EntityId = entity.Id.ToString()
        };

        try
        {
            // Basic entity validation
            if (entity.Id == Guid.Empty)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "INVALID_ID",
                    Message = "Entity ID cannot be empty",
                    PropertyName = nameof(entity.Id),
                    Severity = ValidationSeverity.Error
                });
            }

            if (entity.CreatedAt == default)
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "INVALID_CREATED_DATE",
                    Message = "Created date cannot be default value",
                    PropertyName = nameof(entity.CreatedAt),
                    Severity = ValidationSeverity.Error
                });
            }

            if (entity.CreatedAt > DateTime.UtcNow.AddMinutes(5))
            {
                result.Warnings.Add(new ValidationWarning
                {
                    Code = "FUTURE_CREATED_DATE",
                    Message = "Created date is in the future",
                    PropertyName = nameof(entity.CreatedAt),
                    Value = entity.CreatedAt
                });
            }

            // Entity-specific validation
            await ValidateSpecificEntityAsync(entity, result, cancellationToken);

            _logger.LogTrace("Entity validation completed for {EntityType} {EntityId}. Errors: {ErrorCount}",
                typeof(T).Name, entity.Id, result.Errors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during entity validation for {EntityType} {EntityId}", typeof(T).Name, entity.Id);
            result.Errors.Add(new ValidationError
            {
                Code = "VALIDATION_ERROR",
                Message = $"Entity validation failed: {ex.Message}",
                Severity = ValidationSeverity.Critical
            });
        }

        return result;
    }

    public async Task<ValidationResult> ValidateDataConsistencyAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult 
        { 
            EntityType = entityType,
            EntityId = entityId.ToString()
        };

        try
        {
            _logger.LogDebug("Validating data consistency for {EntityType} {EntityId}", entityType, entityId);

            switch (entityType.ToLowerInvariant())
            {
                case "user":
                    await ValidateUserConsistencyAsync(entityId, result, cancellationToken);
                    break;
                case "project":
                    await ValidateProjectConsistencyAsync(entityId, result, cancellationToken);
                    break;
                case "permission":
                    await ValidatePermissionConsistencyAsync(entityId, result, cancellationToken);
                    break;
                default:
                    result.Warnings.Add(new ValidationWarning
                    {
                        Code = "UNSUPPORTED_CONSISTENCY_CHECK",
                        Message = $"Consistency check not implemented for entity type: {entityType}",
                        PropertyName = "EntityType"
                    });
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during consistency validation for {EntityType} {EntityId}", entityType, entityId);
            result.Errors.Add(new ValidationError
            {
                Code = "CONSISTENCY_CHECK_ERROR",
                Message = $"Consistency check failed: {ex.Message}",
                Severity = ValidationSeverity.Critical
            });
        }

        return result;
    }

    public async Task<ValidationResult> ValidateAuditRulesAsync(AuditConfiguration configuration, CancellationToken cancellationToken = default)
    {
        var result = new ValidationResult 
        { 
            EntityType = "AuditConfiguration",
            EntityId = configuration.Id.ToString()
        };

        try
        {
            _logger.LogDebug("Validating audit rules for configuration {ConfigurationId}", configuration.Id);

            // Validate configuration basic properties
            if (string.IsNullOrWhiteSpace(configuration.Name))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "MISSING_CONFIGURATION_NAME",
                    Message = "Audit configuration name is required",
                    PropertyName = nameof(configuration.Name),
                    Severity = ValidationSeverity.Error
                });
            }

            // Validate schedule if enabled
            if (configuration.IsScheduleEnabled && string.IsNullOrWhiteSpace(configuration.Schedule))
            {
                result.Errors.Add(new ValidationError
                {
                    Code = "MISSING_SCHEDULE",
                    Message = "Schedule is required when schedule is enabled",
                    PropertyName = nameof(configuration.Schedule),
                    Severity = ValidationSeverity.Error
                });
            }

            // Validate rules
            var rules = await _unitOfWork.AuditConfigurationRules.FindAsync(
                r => r.AuditConfigurationId == configuration.Id, cancellationToken);

            if (!rules.Any())
            {
                result.Warnings.Add(new ValidationWarning
                {
                    Code = "NO_AUDIT_RULES",
                    Message = "No audit rules configured",
                    PropertyName = "Rules"
                });
            }

            foreach (var rule in rules)
            {
                var auditRule = await _unitOfWork.AuditRules.GetByIdAsync(rule.AuditRuleId, cancellationToken);
                if (auditRule == null)
                {
                    result.Errors.Add(new ValidationError
                    {
                        Code = "MISSING_AUDIT_RULE",
                        Message = $"Referenced audit rule {rule.AuditRuleId} not found",
                        PropertyName = "AuditRuleId",
                        AttemptedValue = rule.AuditRuleId,
                        Severity = ValidationSeverity.Error
                    });
                }
                else if (!auditRule.IsEnabled)
                {
                    result.Warnings.Add(new ValidationWarning
                    {
                        Code = "DISABLED_AUDIT_RULE",
                        Message = $"Audit rule '{auditRule.Name}' is disabled",
                        PropertyName = "IsEnabled",
                        Value = auditRule.IsEnabled
                    });
                }
            }

            _logger.LogDebug("Audit rules validation completed for configuration {ConfigurationId}. Errors: {ErrorCount}",
                configuration.Id, result.Errors.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during audit rules validation for configuration {ConfigurationId}", configuration.Id);
            result.Errors.Add(new ValidationError
            {
                Code = "AUDIT_RULES_VALIDATION_ERROR",
                Message = $"Audit rules validation failed: {ex.Message}",
                Severity = ValidationSeverity.Critical
            });
        }

        return result;
    }

    public async Task<DataIntegrityReport> PerformDataIntegrityCheckAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        var report = new DataIntegrityReport();

        try
        {
            _logger.LogInformation("Starting comprehensive data integrity check");

            // Check entity counts
            await CheckEntityCountsAsync(report, cancellationToken);

            // Check for orphaned records
            await CheckOrphanedRecordsAsync(report, cancellationToken);

            // Check for duplicate records
            await CheckDuplicateRecordsAsync(report, cancellationToken);

            // Check referential integrity
            await CheckReferentialIntegrityAsync(report, cancellationToken);

            report.GenerationTime = DateTime.UtcNow - startTime;

            _logger.LogInformation("Data integrity check completed in {Duration}ms. Issues found: {IssueCount}",
                report.GenerationTime.TotalMilliseconds, report.Issues.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during data integrity check");
            report.Issues.Add(new DataIntegrityIssue
            {
                Code = "INTEGRITY_CHECK_ERROR",
                Description = $"Data integrity check failed: {ex.Message}",
                Severity = ValidationSeverity.Critical,
                EntityType = "System"
            });
        }

        return report;
    }

    // Placeholder helper methods - would be fully implemented in production
    private async Task ValidateAccountDataAsync<T>(T data, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidateUserDataAsync<T>(T data, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidateProjectDataAsync<T>(T data, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidateCompanyDataAsync<T>(T data, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidatePermissionDataAsync<T>(T data, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidateSpecificEntityAsync<T>(T entity, ValidationResult result, CancellationToken cancellationToken) where T : BaseEntity { }
    private async Task ValidateUserConsistencyAsync(Guid userId, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidateProjectConsistencyAsync(Guid projectId, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task ValidatePermissionConsistencyAsync(Guid permissionId, ValidationResult result, CancellationToken cancellationToken) { }
    private async Task CheckEntityCountsAsync(DataIntegrityReport report, CancellationToken cancellationToken) { }
    private async Task CheckOrphanedRecordsAsync(DataIntegrityReport report, CancellationToken cancellationToken) { }
    private async Task CheckDuplicateRecordsAsync(DataIntegrityReport report, CancellationToken cancellationToken) { }
    private async Task CheckReferentialIntegrityAsync(DataIntegrityReport report, CancellationToken cancellationToken) { }
}
