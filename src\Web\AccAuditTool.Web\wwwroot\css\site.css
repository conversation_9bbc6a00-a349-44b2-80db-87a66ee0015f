/* ACC Audit Tool - Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --sidebar-bg: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

html, body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    height: 100%;
    margin: 0;
    padding: 0;
}

/* Global styles */
.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
    box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem var(--primary-color);
}

/* Page not found styles */
.page-not-found {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--light-color);
}

/* Blazor error UI */
#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

.blazor-error-boundary::after {
    content: "An error has occurred."
}

/* Utility classes */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border 0.75s linear infinite;
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg);
    }
}

/* Demo Dashboard Styles */
.sidebar {
    background: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    min-height: 100vh;
    color: rgba(255, 255, 255, 0.9);
    padding: 0;
}

.nav-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.brand-icon {
    font-size: 1.5rem;
    color: #ffd700;
}

.nav-content {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.3);
}

.nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
    border-left-color: #ffd700;
    font-weight: 500;
}

.nav-icon {
    width: 1.25rem;
    margin-right: 0.75rem;
    text-align: center;
    flex-shrink: 0;
}

.main-content {
    background-color: #f8f9fa;
    min-height: 100vh;
    padding: 0;
}

.top-bar {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.page-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1.5rem;
}

.page-title i {
    margin-right: 0.75rem;
    color: #007bff;
}

.dashboard-content {
    padding: 1.5rem;
}

.metric-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    flex-shrink: 0;
}

.metric-icon i {
    font-size: 1.5rem;
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: #495057;
    margin: 0 0 0.25rem 0;
    line-height: 1;
}

.metric-label {
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

.card {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

.card-title {
    margin: 0;
    color: #495057;
    font-weight: 600;
    font-size: 1rem;
}

.card-title i {
    margin-right: 0.5rem;
    color: #007bff;
}

.card-body {
    padding: 1.5rem;
}

.audit-list, .findings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.audit-item, .finding-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #f1f3f4;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.audit-item:hover, .finding-item:hover {
    background-color: #f8f9fa;
}

.audit-status, .finding-severity {
    flex-shrink: 0;
}

.audit-details, .finding-details {
    flex: 1;
    min-width: 0;
}

.audit-details h6, .finding-details h6 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: #495057;
}

.audit-details p, .finding-details p {
    margin: 0;
    font-size: 0.875rem;
}

.audit-findings, .finding-actions {
    flex-shrink: 0;
}

.findings-count {
    font-weight: 600;
    color: #495057;
}

.progress {
    width: 100px;
    height: 6px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: -100%;
        width: 260px;
        z-index: 1000;
        transition: left 0.3s ease;
    }

    .sidebar.show {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .top-bar {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .dashboard-content {
        padding: 1rem;
    }

    .metric-card {
        margin-bottom: 1rem;
    }

    .audit-item, .finding-item {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
}

/* Demo Styles */
.demo-container {
    display: flex;
    min-height: 100vh;
    background-color: #f8f9fa;
}

.demo-sidebar {
    width: 260px;
    background: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
    color: rgba(255, 255, 255, 0.9);
    flex-shrink: 0;
}

.demo-nav-header {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    font-size: 1.1rem;
}

.demo-nav-content {
    padding: 1rem 0;
}

.demo-nav-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.demo-nav-item {
    margin: 0;
}

.demo-nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
    position: relative;
}

.demo-nav-link:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: rgba(255, 255, 255, 0.3);
}

.demo-nav-link.active {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.15);
    border-left-color: #ffd700;
    font-weight: 500;
}

.nav-text {
    flex: 1;
}

.nav-badge {
    background-color: rgba(255, 255, 255, 0.2);
    color: #fff;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 0.75rem;
    font-weight: 500;
}

.demo-main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.demo-top-bar {
    background-color: #fff;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.demo-page-title {
    color: #495057;
    font-weight: 600;
    margin: 0;
    font-size: 1.5rem;
}

.demo-page-title i {
    margin-right: 0.75rem;
    color: #007bff;
}

.top-bar-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.demo-dashboard-content {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
}

.demo-metric-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    position: relative;
}

.demo-metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.demo-audit-list, .demo-findings-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
