using AccAuditTool.Application.Services;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Application.Rules;

/// <summary>
/// Implementation of a custom audit rule based on user-defined conditions and actions
/// </summary>
public class CustomAuditRule : BaseAuditRule
{
    private readonly CustomRuleDefinition _definition;
    private readonly IRuleExpressionEvaluator _expressionEvaluator;
    private readonly ILogger _logger;

    public CustomAuditRule(
        CustomRuleDefinition definition, 
        IRuleExpressionEvaluator expressionEvaluator,
        ILogger logger) 
        : base(definition.RuleId, definition.Name, definition.Description, definition.Category, definition.Severity)
    {
        _definition = definition;
        _expressionEvaluator = expressionEvaluator;
        _logger = logger;
        IsEnabled = definition.IsEnabled;
    }

    public override async Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default)
    {
        var result = new AuditRuleResult
        {
            RuleId = RuleId,
            Success = true
        };

        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Executing custom rule {RuleId}: {RuleName}", RuleId, Name);

            // Get entities to evaluate based on rule scope
            var entities = await GetEntitiesForEvaluation(context, cancellationToken);

            foreach (var entity in entities)
            {
                var evaluationContext = new RuleEvaluationContext
                {
                    Entity = entity,
                    AuditContext = context,
                    RuleDefinition = _definition
                };

                // Evaluate conditions
                var conditionsMatch = await EvaluateConditionsAsync(evaluationContext, cancellationToken);

                if (conditionsMatch)
                {
                    // Execute actions
                    await ExecuteActionsAsync(evaluationContext, result, cancellationToken);
                }
            }

            result.ExecutionTime = DateTime.UtcNow - startTime;
            result.Metadata["EntitiesEvaluated"] = entities.Count();
            result.Metadata["ConditionsCount"] = _definition.Conditions.Count;
            result.Metadata["ActionsCount"] = _definition.Actions.Count;

            _logger.LogDebug("Completed custom rule {RuleId} execution. Found {FindingsCount} issues in {ExecutionTime}ms",
                RuleId, result.Findings.Count, result.ExecutionTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            result.ExecutionTime = DateTime.UtcNow - startTime;
            
            _logger.LogError(ex, "Error executing custom rule {RuleId}: {RuleName}", RuleId, Name);
        }

        return result;
    }

    private async Task<IEnumerable<object>> GetEntitiesForEvaluation(AuditContext context, CancellationToken cancellationToken)
    {
        var entities = new List<object>();

        // Determine what entities to evaluate based on rule conditions
        var entityTypes = _definition.Conditions
            .Select(c => GetEntityTypeFromField(c.Field))
            .Distinct()
            .ToList();

        foreach (var entityType in entityTypes)
        {
            switch (entityType.ToLowerInvariant())
            {
                case "user":
                    var users = await context.UnitOfWork.Users.GetAllAsync(cancellationToken);
                    entities.AddRange(users);
                    break;

                case "project":
                    var projects = await context.UnitOfWork.Projects.GetAllAsync(cancellationToken);
                    entities.AddRange(projects);
                    break;

                case "company":
                    var companies = await context.UnitOfWork.Companies.GetAllAsync(cancellationToken);
                    entities.AddRange(companies);
                    break;

                case "role":
                    var roles = await context.UnitOfWork.Roles.GetAllAsync(cancellationToken);
                    entities.AddRange(roles);
                    break;

                case "permission":
                    var permissions = await context.UnitOfWork.Permissions.GetAllAsync(cancellationToken);
                    entities.AddRange(permissions);
                    break;

                default:
                    _logger.LogWarning("Unknown entity type in custom rule: {EntityType}", entityType);
                    break;
            }
        }

        return entities.Distinct();
    }

    private async Task<bool> EvaluateConditionsAsync(RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        if (!_definition.Conditions.Any())
        {
            return true; // No conditions means always match
        }

        var results = new List<bool>();
        var currentLogicalOperator = "AND";

        foreach (var condition in _definition.Conditions)
        {
            var conditionResult = await EvaluateConditionAsync(condition, context, cancellationToken);
            
            if (condition.Negate)
            {
                conditionResult = !conditionResult;
            }

            if (results.Any())
            {
                // Apply logical operator from previous condition
                var previousResult = results.Last();
                var combinedResult = currentLogicalOperator.ToUpperInvariant() switch
                {
                    "AND" => previousResult && conditionResult,
                    "OR" => previousResult || conditionResult,
                    _ => previousResult && conditionResult
                };
                
                results[results.Count - 1] = combinedResult;
            }
            else
            {
                results.Add(conditionResult);
            }

            currentLogicalOperator = condition.LogicalOperator;
        }

        return results.LastOrDefault();
    }

    private async Task<bool> EvaluateConditionAsync(RuleCondition condition, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        try
        {
            // Get the field value from the entity
            var fieldValue = await GetFieldValueAsync(condition.Field, context, cancellationToken);
            
            // Evaluate the condition using the expression evaluator
            return await _expressionEvaluator.EvaluateConditionAsync(fieldValue, condition.Operator, condition.Value, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error evaluating condition {Field} {Operator} {Value} in rule {RuleId}", 
                condition.Field, condition.Operator, condition.Value, RuleId);
            return false;
        }
    }

    private async Task<object?> GetFieldValueAsync(string field, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        var parts = field.Split('.');
        if (parts.Length < 2)
        {
            throw new ArgumentException($"Invalid field format: {field}");
        }

        var entityType = parts[0];
        var propertyName = parts[1];

        // Handle special computed properties
        if (propertyName.EndsWith("Count"))
        {
            return await GetCountPropertyAsync(entityType, propertyName, context, cancellationToken);
        }

        // Get property value using reflection
        var entity = context.Entity;
        var property = entity.GetType().GetProperty(propertyName);
        
        if (property == null)
        {
            throw new ArgumentException($"Property {propertyName} not found on {entityType}");
        }

        return property.GetValue(entity);
    }

    private async Task<int> GetCountPropertyAsync(string entityType, string propertyName, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        var entity = context.Entity;
        
        return propertyName switch
        {
            "PermissionCount" when entityType == "User" => await GetUserPermissionCountAsync(entity, context, cancellationToken),
            "ProjectCount" when entityType == "User" => await GetUserProjectCountAsync(entity, context, cancellationToken),
            "RoleCount" when entityType == "User" => await GetUserRoleCountAsync(entity, context, cancellationToken),
            _ => 0
        };
    }

    private async Task<int> GetUserPermissionCountAsync(object userEntity, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        if (userEntity is not Domain.Entities.User user)
            return 0;

        var permissions = await context.AuditContext.UnitOfWork.Permissions.FindAsync(
            p => p.SubjectId == user.AccUserId, cancellationToken);
        
        return permissions.Count();
    }

    private async Task<int> GetUserProjectCountAsync(object userEntity, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        if (userEntity is not Domain.Entities.User user)
            return 0;

        var userRoles = await context.AuditContext.UnitOfWork.UserRoles.FindAsync(
            ur => ur.UserId == user.Id, cancellationToken);
        
        return userRoles.Select(ur => ur.ProjectId).Distinct().Count();
    }

    private async Task<int> GetUserRoleCountAsync(object userEntity, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        if (userEntity is not Domain.Entities.User user)
            return 0;

        var userRoles = await context.AuditContext.UnitOfWork.UserRoles.FindAsync(
            ur => ur.UserId == user.Id, cancellationToken);
        
        return userRoles.Count();
    }

    private async Task ExecuteActionsAsync(RuleEvaluationContext context, AuditRuleResult result, CancellationToken cancellationToken)
    {
        foreach (var action in _definition.Actions)
        {
            try
            {
                await ExecuteActionAsync(action, context, result, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing action {ActionType} in rule {RuleId}", action.Type, RuleId);
            }
        }
    }

    private async Task ExecuteActionAsync(RuleAction action, RuleEvaluationContext context, AuditRuleResult result, CancellationToken cancellationToken)
    {
        switch (action.Type.ToLowerInvariant())
        {
            case "create_finding":
                await CreateFindingActionAsync(action, context, result, cancellationToken);
                break;

            case "log_event":
                await LogEventActionAsync(action, context, cancellationToken);
                break;

            case "send_notification":
                await SendNotificationActionAsync(action, context, cancellationToken);
                break;

            default:
                _logger.LogWarning("Unknown action type: {ActionType} in rule {RuleId}", action.Type, RuleId);
                break;
        }
    }

    private async Task CreateFindingActionAsync(RuleAction action, RuleEvaluationContext context, AuditRuleResult result, CancellationToken cancellationToken)
    {
        var title = action.Parameters.GetValueOrDefault("title", "Custom rule violation")?.ToString() ?? "Custom rule violation";
        var description = action.Parameters.GetValueOrDefault("description", _definition.Description)?.ToString() ?? _definition.Description;
        var recommendation = action.Parameters.GetValueOrDefault("recommendation")?.ToString();

        var finding = CreateFinding(title, description, context.Entity, recommendation);
        
        // Set affected entity information
        if (context.Entity is Domain.Entities.BaseEntity baseEntity)
        {
            finding.AffectedEntity = context.Entity.GetType().Name;
            finding.AffectedEntityId = baseEntity.Id.ToString();
        }

        result.Findings.Add(finding);
    }

    private async Task LogEventActionAsync(RuleAction action, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        var message = action.Parameters.GetValueOrDefault("message", $"Custom rule {RuleId} triggered")?.ToString();
        var level = action.Parameters.GetValueOrDefault("level", "Information")?.ToString();

        switch (level?.ToLowerInvariant())
        {
            case "error":
                _logger.LogError("Custom rule event: {Message}", message);
                break;
            case "warning":
                _logger.LogWarning("Custom rule event: {Message}", message);
                break;
            default:
                _logger.LogInformation("Custom rule event: {Message}", message);
                break;
        }
    }

    private async Task SendNotificationActionAsync(RuleAction action, RuleEvaluationContext context, CancellationToken cancellationToken)
    {
        // Placeholder for notification implementation
        var recipient = action.Parameters.GetValueOrDefault("recipient")?.ToString();
        var subject = action.Parameters.GetValueOrDefault("subject")?.ToString();
        var body = action.Parameters.GetValueOrDefault("body")?.ToString();

        _logger.LogInformation("Notification would be sent to {Recipient}: {Subject}", recipient, subject);
        // TODO: Implement actual notification sending
    }

    private string GetEntityTypeFromField(string field)
    {
        var parts = field.Split('.');
        return parts.Length > 0 ? parts[0] : "Unknown";
    }

    public override async Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default)
    {
        var result = await base.ValidateAsync(cancellationToken);

        // Additional validation for custom rules
        if (!_definition.Conditions.Any())
        {
            result.Warnings.Add("Custom rule has no conditions defined");
        }

        if (!_definition.Actions.Any())
        {
            result.IsValid = false;
            result.Errors.Add("Custom rule must have at least one action");
        }

        return result;
    }
}

/// <summary>
/// Context for evaluating a rule against an entity
/// </summary>
public class RuleEvaluationContext
{
    public object Entity { get; set; } = null!;
    public AuditContext AuditContext { get; set; } = null!;
    public CustomRuleDefinition RuleDefinition { get; set; } = null!;
}
