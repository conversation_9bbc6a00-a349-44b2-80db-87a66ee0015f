using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for building and managing custom audit rules
/// </summary>
public interface ICustomRuleBuilder
{
    /// <summary>
    /// Create a new custom rule from a rule definition
    /// </summary>
    Task<IAuditRule> CreateRuleAsync(CustomRuleDefinition definition, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update an existing custom rule
    /// </summary>
    Task<IAuditRule> UpdateRuleAsync(string ruleId, CustomRuleDefinition definition, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate a rule definition
    /// </summary>
    Task<RuleValidationResult> ValidateRuleDefinitionAsync(CustomRuleDefinition definition, CancellationToken cancellationToken = default);

    /// <summary>
    /// Test a rule against sample data
    /// </summary>
    Task<RuleTestResult> TestRuleAsync(CustomRuleDefinition definition, RuleTestData testData, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get available rule templates
    /// </summary>
    Task<IEnumerable<RuleTemplate>> GetRuleTemplatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Create a rule from a template
    /// </summary>
    Task<CustomRuleDefinition> CreateFromTemplateAsync(string templateId, Dictionary<string, object> parameters, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of custom rule builder
/// </summary>
public class CustomRuleBuilder : ICustomRuleBuilder
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<CustomRuleBuilder> _logger;
    private readonly IRuleExpressionEvaluator _expressionEvaluator;

    public CustomRuleBuilder(
        IUnitOfWork unitOfWork, 
        ILogger<CustomRuleBuilder> logger,
        IRuleExpressionEvaluator expressionEvaluator)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
        _expressionEvaluator = expressionEvaluator;
    }

    public async Task<IAuditRule> CreateRuleAsync(CustomRuleDefinition definition, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Creating custom rule: {RuleName}", definition.Name);

        // Validate the definition
        var validationResult = await ValidateRuleDefinitionAsync(definition, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ArgumentException($"Invalid rule definition: {string.Join(", ", validationResult.Errors)}");
        }

        // Create the custom rule instance
        var customRule = new CustomAuditRule(definition, _expressionEvaluator, _logger);

        // Save to database
        var auditRule = new AuditRule
        {
            RuleId = definition.RuleId,
            Name = definition.Name,
            Description = definition.Description,
            Category = definition.Category,
            Severity = definition.Severity,
            IsEnabled = definition.IsEnabled,
            RuleType = AuditRuleType.Custom,
            Definition = JsonSerializer.Serialize(definition),
            CreatedAt = DateTime.UtcNow,
            CreatedBy = definition.CreatedBy
        };

        await _unitOfWork.AuditRules.AddAsync(auditRule, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        _logger.LogInformation("Successfully created custom rule: {RuleId}", definition.RuleId);
        return customRule;
    }

    public async Task<IAuditRule> UpdateRuleAsync(string ruleId, CustomRuleDefinition definition, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Updating custom rule: {RuleId}", ruleId);

        var existingRule = await _unitOfWork.AuditRules.FirstOrDefaultAsync(r => r.RuleId == ruleId, cancellationToken);
        if (existingRule == null)
        {
            throw new ArgumentException($"Rule {ruleId} not found");
        }

        if (existingRule.RuleType != AuditRuleType.Custom)
        {
            throw new InvalidOperationException($"Rule {ruleId} is not a custom rule");
        }

        // Validate the updated definition
        var validationResult = await ValidateRuleDefinitionAsync(definition, cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ArgumentException($"Invalid rule definition: {string.Join(", ", validationResult.Errors)}");
        }

        // Update the rule
        existingRule.Name = definition.Name;
        existingRule.Description = definition.Description;
        existingRule.Category = definition.Category;
        existingRule.Severity = definition.Severity;
        existingRule.IsEnabled = definition.IsEnabled;
        existingRule.Definition = JsonSerializer.Serialize(definition);
        existingRule.UpdatedAt = DateTime.UtcNow;
        existingRule.UpdatedBy = definition.UpdatedBy;

        await _unitOfWork.AuditRules.UpdateAsync(existingRule, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        var customRule = new CustomAuditRule(definition, _expressionEvaluator, _logger);

        _logger.LogInformation("Successfully updated custom rule: {RuleId}", ruleId);
        return customRule;
    }

    public async Task<RuleValidationResult> ValidateRuleDefinitionAsync(CustomRuleDefinition definition, CancellationToken cancellationToken = default)
    {
        var result = new RuleValidationResult
        {
            RuleId = definition.RuleId,
            IsValid = true
        };

        // Basic validation
        if (string.IsNullOrEmpty(definition.RuleId))
        {
            result.IsValid = false;
            result.Errors.Add("Rule ID is required");
        }

        if (string.IsNullOrEmpty(definition.Name))
        {
            result.IsValid = false;
            result.Errors.Add("Rule name is required");
        }

        if (string.IsNullOrEmpty(definition.Description))
        {
            result.IsValid = false;
            result.Errors.Add("Rule description is required");
        }

        // Validate conditions
        if (definition.Conditions == null || !definition.Conditions.Any())
        {
            result.IsValid = false;
            result.Errors.Add("At least one condition is required");
        }
        else
        {
            foreach (var condition in definition.Conditions)
            {
                var conditionValidation = ValidateCondition(condition);
                if (!conditionValidation.IsValid)
                {
                    result.IsValid = false;
                    result.Errors.AddRange(conditionValidation.Errors);
                }
            }
        }

        // Validate actions
        if (definition.Actions == null || !definition.Actions.Any())
        {
            result.IsValid = false;
            result.Errors.Add("At least one action is required");
        }
        else
        {
            foreach (var action in definition.Actions)
            {
                var actionValidation = ValidateAction(action);
                if (!actionValidation.IsValid)
                {
                    result.IsValid = false;
                    result.Errors.AddRange(actionValidation.Errors);
                }
            }
        }

        // Check for duplicate rule ID
        var existingRule = await _unitOfWork.AuditRules.FirstOrDefaultAsync(r => r.RuleId == definition.RuleId, cancellationToken);
        if (existingRule != null)
        {
            result.Warnings.Add($"Rule ID {definition.RuleId} already exists");
        }

        return result;
    }

    public async Task<RuleTestResult> TestRuleAsync(CustomRuleDefinition definition, RuleTestData testData, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Testing custom rule: {RuleName}", definition.Name);

        var result = new RuleTestResult
        {
            RuleId = definition.RuleId,
            Success = true,
            TestedAt = DateTime.UtcNow
        };

        try
        {
            // Create a temporary rule instance
            var customRule = new CustomAuditRule(definition, _expressionEvaluator, _logger);

            // Create test context
            var testContext = new AuditContext
            {
                AuditRunId = Guid.NewGuid(),
                AccountId = testData.AccountId,
                ProjectIds = testData.ProjectIds,
                UnitOfWork = _unitOfWork,
                Data = testData.TestData
            };

            // Execute the rule
            var ruleResult = await customRule.ExecuteAsync(testContext, cancellationToken);

            result.Success = ruleResult.Success;
            result.ErrorMessage = ruleResult.ErrorMessage;
            result.FindingsGenerated = ruleResult.Findings.Count;
            result.ExecutionTime = ruleResult.ExecutionTime;
            result.TestFindings = ruleResult.Findings;

            _logger.LogDebug("Rule test completed. Success: {Success}, Findings: {FindingCount}", 
                result.Success, result.FindingsGenerated);
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error testing custom rule: {RuleName}", definition.Name);
        }

        return result;
    }

    public async Task<IEnumerable<RuleTemplate>> GetRuleTemplatesAsync(CancellationToken cancellationToken = default)
    {
        // Return predefined rule templates
        return new List<RuleTemplate>
        {
            new RuleTemplate
            {
                Id = "user-inactive-template",
                Name = "Inactive User Detection",
                Description = "Template for detecting inactive users",
                Category = AuditRuleCategory.Security,
                Parameters = new List<RuleTemplateParameter>
                {
                    new() { Name = "InactivityDays", Type = "int", DefaultValue = "90", Description = "Number of days to consider user inactive" },
                    new() { Name = "ExcludeServiceAccounts", Type = "bool", DefaultValue = "true", Description = "Whether to exclude service accounts" }
                },
                Template = GetInactiveUserTemplate()
            },
            new RuleTemplate
            {
                Id = "excessive-permissions-template",
                Name = "Excessive Permissions Detection",
                Description = "Template for detecting users with too many permissions",
                Category = AuditRuleCategory.Security,
                Parameters = new List<RuleTemplateParameter>
                {
                    new() { Name = "MaxPermissions", Type = "int", DefaultValue = "20", Description = "Maximum number of permissions per user" },
                    new() { Name = "MaxProjects", Type = "int", DefaultValue = "10", Description = "Maximum number of projects per user" }
                },
                Template = GetExcessivePermissionsTemplate()
            },
            new RuleTemplate
            {
                Id = "role-segregation-template",
                Name = "Role Segregation Violation",
                Description = "Template for detecting segregation of duties violations",
                Category = AuditRuleCategory.Compliance,
                Parameters = new List<RuleTemplateParameter>
                {
                    new() { Name = "ConflictingRoles", Type = "string[]", DefaultValue = "Admin,Auditor", Description = "Comma-separated list of conflicting roles" }
                },
                Template = GetRoleSegregationTemplate()
            }
        };
    }

    public async Task<CustomRuleDefinition> CreateFromTemplateAsync(string templateId, Dictionary<string, object> parameters, CancellationToken cancellationToken = default)
    {
        var templates = await GetRuleTemplatesAsync(cancellationToken);
        var template = templates.FirstOrDefault(t => t.Id == templateId);
        
        if (template == null)
        {
            throw new ArgumentException($"Template {templateId} not found");
        }

        // Apply parameters to template
        var definition = JsonSerializer.Deserialize<CustomRuleDefinition>(template.Template);
        if (definition == null)
        {
            throw new InvalidOperationException($"Invalid template format for {templateId}");
        }

        // Replace parameter placeholders
        foreach (var parameter in parameters)
        {
            ReplaceParameterInDefinition(definition, parameter.Key, parameter.Value);
        }

        // Generate unique rule ID
        definition.RuleId = $"CUSTOM-{templateId.ToUpper()}-{DateTime.UtcNow:yyyyMMddHHmmss}";
        definition.CreatedAt = DateTime.UtcNow;

        return definition;
    }

    private ValidationResult ValidateCondition(RuleCondition condition)
    {
        var result = new ValidationResult { IsValid = true };

        if (string.IsNullOrEmpty(condition.Field))
        {
            result.IsValid = false;
            result.Errors.Add("Condition field is required");
        }

        if (string.IsNullOrEmpty(condition.Operator))
        {
            result.IsValid = false;
            result.Errors.Add("Condition operator is required");
        }

        // Validate operator
        var validOperators = new[] { "equals", "not_equals", "greater_than", "less_than", "contains", "not_contains", "in", "not_in" };
        if (!validOperators.Contains(condition.Operator.ToLowerInvariant()))
        {
            result.IsValid = false;
            result.Errors.Add($"Invalid operator: {condition.Operator}");
        }

        return result;
    }

    private ValidationResult ValidateAction(RuleAction action)
    {
        var result = new ValidationResult { IsValid = true };

        if (string.IsNullOrEmpty(action.Type))
        {
            result.IsValid = false;
            result.Errors.Add("Action type is required");
        }

        // Validate action type
        var validActionTypes = new[] { "create_finding", "log_event", "send_notification", "execute_script" };
        if (!validActionTypes.Contains(action.Type.ToLowerInvariant()))
        {
            result.IsValid = false;
            result.Errors.Add($"Invalid action type: {action.Type}");
        }

        return result;
    }

    private void ReplaceParameterInDefinition(CustomRuleDefinition definition, string parameterName, object parameterValue)
    {
        var placeholder = $"{{{parameterName}}}";
        var valueString = parameterValue.ToString();

        // Replace in conditions
        foreach (var condition in definition.Conditions)
        {
            if (condition.Value?.ToString()?.Contains(placeholder) == true)
            {
                condition.Value = condition.Value.ToString()!.Replace(placeholder, valueString);
            }
        }

        // Replace in actions
        foreach (var action in definition.Actions)
        {
            foreach (var param in action.Parameters)
            {
                if (param.Value?.ToString()?.Contains(placeholder) == true)
                {
                    param.Value = param.Value.ToString()!.Replace(placeholder, valueString);
                }
            }
        }
    }

    private string GetInactiveUserTemplate()
    {
        var template = new CustomRuleDefinition
        {
            Name = "Inactive User Detection",
            Description = "Detects users who have been inactive for more than {InactivityDays} days",
            Category = AuditRuleCategory.Security,
            Severity = AuditSeverity.Medium,
            Conditions = new List<RuleCondition>
            {
                new() { Field = "User.LastSeenAt", Operator = "less_than", Value = "NOW() - {InactivityDays} DAYS" },
                new() { Field = "User.Status", Operator = "equals", Value = "Active" }
            },
            Actions = new List<RuleAction>
            {
                new() 
                { 
                    Type = "create_finding", 
                    Parameters = new Dictionary<string, object>
                    {
                        ["title"] = "Inactive user detected",
                        ["description"] = "User has been inactive for more than {InactivityDays} days",
                        ["severity"] = "Medium"
                    }
                }
            }
        };

        return JsonSerializer.Serialize(template);
    }

    private string GetExcessivePermissionsTemplate()
    {
        var template = new CustomRuleDefinition
        {
            Name = "Excessive Permissions Detection",
            Description = "Detects users with more than {MaxPermissions} permissions or access to more than {MaxProjects} projects",
            Category = AuditRuleCategory.Security,
            Severity = AuditSeverity.High,
            Conditions = new List<RuleCondition>
            {
                new() { Field = "User.PermissionCount", Operator = "greater_than", Value = "{MaxPermissions}" },
                new() { Field = "User.ProjectCount", Operator = "greater_than", Value = "{MaxProjects}", LogicalOperator = "OR" }
            },
            Actions = new List<RuleAction>
            {
                new() 
                { 
                    Type = "create_finding", 
                    Parameters = new Dictionary<string, object>
                    {
                        ["title"] = "Excessive permissions detected",
                        ["description"] = "User has excessive permissions or project access",
                        ["severity"] = "High"
                    }
                }
            }
        };

        return JsonSerializer.Serialize(template);
    }

    private string GetRoleSegregationTemplate()
    {
        var template = new CustomRuleDefinition
        {
            Name = "Role Segregation Violation",
            Description = "Detects users with conflicting roles that violate segregation of duties",
            Category = AuditRuleCategory.Compliance,
            Severity = AuditSeverity.High,
            Conditions = new List<RuleCondition>
            {
                new() { Field = "User.Roles", Operator = "contains_any", Value = "{ConflictingRoles}" }
            },
            Actions = new List<RuleAction>
            {
                new() 
                { 
                    Type = "create_finding", 
                    Parameters = new Dictionary<string, object>
                    {
                        ["title"] = "Role segregation violation",
                        ["description"] = "User has conflicting roles that violate segregation of duties",
                        ["severity"] = "High"
                    }
                }
            }
        };

        return JsonSerializer.Serialize(template);
    }

    private class ValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Errors { get; set; } = new();
    }
}

/// <summary>
/// Definition for a custom audit rule
/// </summary>
public class CustomRuleDefinition
{
    public string RuleId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AuditRuleCategory Category { get; set; }
    public AuditSeverity Severity { get; set; }
    public bool IsEnabled { get; set; } = true;
    public List<RuleCondition> Conditions { get; set; } = new();
    public List<RuleAction> Actions { get; set; } = new();
    public string? CreatedBy { get; set; }
    public string? UpdatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Condition for a custom rule
/// </summary>
public class RuleCondition
{
    public string Field { get; set; } = string.Empty;
    public string Operator { get; set; } = string.Empty;
    public object? Value { get; set; }
    public string LogicalOperator { get; set; } = "AND"; // AND, OR
    public bool Negate { get; set; } = false;
}

/// <summary>
/// Action to take when rule conditions are met
/// </summary>
public class RuleAction
{
    public string Type { get; set; } = string.Empty;
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// Template for creating custom rules
/// </summary>
public class RuleTemplate
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AuditRuleCategory Category { get; set; }
    public List<RuleTemplateParameter> Parameters { get; set; } = new();
    public string Template { get; set; } = string.Empty;
}

/// <summary>
/// Parameter for a rule template
/// </summary>
public class RuleTemplateParameter
{
    public string Name { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string DefaultValue { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Required { get; set; } = true;
}

/// <summary>
/// Result of testing a custom rule
/// </summary>
public class RuleTestResult
{
    public string RuleId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int FindingsGenerated { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public DateTime TestedAt { get; set; }
    public List<AuditFindingData> TestFindings { get; set; } = new();
}

/// <summary>
/// Test data for rule testing
/// </summary>
public class RuleTestData
{
    public string AccountId { get; set; } = string.Empty;
    public List<string> ProjectIds { get; set; } = new();
    public Dictionary<string, object> TestData { get; set; } = new();
}
