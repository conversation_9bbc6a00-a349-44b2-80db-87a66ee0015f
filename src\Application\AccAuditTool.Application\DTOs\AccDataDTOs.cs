namespace AccAuditTool.Application.Interfaces;

/// <summary>
/// ACC Account DTO
/// </summary>
public class AccAccountDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Region { get; set; }
    public string? SubscriptionType { get; set; }
}

/// <summary>
/// ACC Project DTO
/// </summary>
public class AccProjectDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string AccountId { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string? ProjectType { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}

/// <summary>
/// ACC User DTO
/// </summary>
public class AccUserDto
{
    public string Id { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? FirstName { get; set; }
    public string? LastName { get; set; }
    public string? CompanyId { get; set; }
    public string? CompanyName { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? LastSeenAt { get; set; }
    public List<AccUserRoleDto> Roles { get; set; } = new();
}

/// <summary>
/// ACC User Role DTO
/// </summary>
public class AccUserRoleDto
{
    public string RoleId { get; set; } = string.Empty;
    public string RoleName { get; set; } = string.Empty;
    public string? ServiceKey { get; set; }
    public DateTime AssignedAt { get; set; }
}

/// <summary>
/// ACC Company DTO
/// </summary>
public class AccCompanyDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Trade { get; set; }
    public string? Address { get; set; }
    public string? Phone { get; set; }
}

/// <summary>
/// ACC Role DTO
/// </summary>
public class AccRoleDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string? ServiceKey { get; set; }
    public List<string> Permissions { get; set; } = new();
}

/// <summary>
/// ACC Resource DTO
/// </summary>
public class AccResourceDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Path { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public string? ParentId { get; set; }
    public DateTime? LastModifiedAt { get; set; }
    public string? LastModifiedBy { get; set; }
}

/// <summary>
/// ACC Permission DTO
/// </summary>
public class AccPermissionDto
{
    public string SubjectType { get; set; } = string.Empty;
    public string SubjectId { get; set; } = string.Empty;
    public string ResourceId { get; set; } = string.Empty;
    public string ResourceType { get; set; } = string.Empty;
    public List<string> Actions { get; set; } = new();
    public string? InheritedFrom { get; set; }
}
