@using AccAuditTool.Application.Services

<div class="compliance-overview">
    @if (Frameworks.Any())
    {
        <div class="compliance-list">
            @foreach (var framework in Frameworks.Take(MaxFrameworks))
            {
                <div class="compliance-item">
                    <div class="compliance-header">
                        <div class="framework-info">
                            <h6 class="framework-name">@framework.Name</h6>
                            <small class="framework-version text-muted"><EMAIL></small>
                        </div>
                        <div class="compliance-score">
                            <span class="score-value text-@GetComplianceColor(GetFrameworkCompliance(framework.Id))">
                                @GetFrameworkCompliance(framework.Id).ToString("F1")%
                            </span>
                        </div>
                    </div>
                    
                    <div class="compliance-progress">
                        <div class="progress">
                            <div class="progress-bar bg-@GetComplianceColor(GetFrameworkCompliance(framework.Id))" 
                                 role="progressbar" 
                                 style="width: @GetFrameworkCompliance(framework.Id)%" 
                                 aria-valuenow="@GetFrameworkCompliance(framework.Id)" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                        </div>
                    </div>

                    <div class="compliance-details">
                        <div class="detail-item">
                            <span class="detail-label">Controls:</span>
                            <span class="detail-value">@framework.Controls.Count</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Status:</span>
                            <span class="detail-value">
                                <span class="badge bg-@GetComplianceColor(GetFrameworkCompliance(framework.Id))">
                                    @GetComplianceStatus(GetFrameworkCompliance(framework.Id))
                                </span>
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">Last Check:</span>
                            <span class="detail-value text-muted">@GetLastCheckTime(framework.Id)</span>
                        </div>
                    </div>

                    @if (ShowActions)
                    {
                        <div class="compliance-actions">
                            <button class="btn btn-outline-primary btn-sm" @onclick="() => ViewFrameworkDetails(framework.Id)">
                                <i class="fas fa-eye"></i>
                                View Details
                            </button>
                            <button class="btn btn-outline-secondary btn-sm" @onclick="() => RunComplianceCheck(framework.Id)">
                                <i class="fas fa-sync"></i>
                                Check Now
                            </button>
                        </div>
                    }
                </div>
            }
        </div>

        @if (Frameworks.Count > MaxFrameworks)
        {
            <div class="compliance-footer">
                <button class="btn btn-link btn-sm" @onclick="ShowAllFrameworks">
                    <i class="fas fa-ellipsis-h"></i>
                    View all @Frameworks.Count frameworks
                </button>
            </div>
        }

        <div class="overall-compliance">
            <div class="overall-header">
                <h6>Overall Compliance</h6>
                <span class="overall-score text-@GetComplianceColor(OverallCompliancePercentage)">
                    @OverallCompliancePercentage.ToString("F1")%
                </span>
            </div>
            <div class="progress">
                <div class="progress-bar bg-@GetComplianceColor(OverallCompliancePercentage)" 
                     role="progressbar" 
                     style="width: @OverallCompliancePercentage%" 
                     aria-valuenow="@OverallCompliancePercentage" 
                     aria-valuemin="0" 
                     aria-valuemax="100">
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="no-frameworks-message">
            <i class="fas fa-clipboard-check text-muted"></i>
            <p class="text-muted">No compliance frameworks configured</p>
            @if (ShowActions)
            {
                <button class="btn btn-primary btn-sm" @onclick="ConfigureFrameworks">
                    <i class="fas fa-plus"></i>
                    Configure Frameworks
                </button>
            }
        </div>
    }
</div>

@code {
    [Parameter] public List<ComplianceFramework> Frameworks { get; set; } = new();
    [Parameter] public int MaxFrameworks { get; set; } = 3;
    [Parameter] public bool ShowActions { get; set; } = true;
    [Parameter] public EventCallback<string> OnViewFramework { get; set; }
    [Parameter] public EventCallback<string> OnRunCheck { get; set; }
    [Parameter] public EventCallback OnShowAll { get; set; }
    [Parameter] public EventCallback OnConfigure { get; set; }

    // Sample compliance data - in real implementation, this would come from the service
    private Dictionary<string, double> FrameworkCompliance = new()
    {
        ["ISO27001"] = 87.5,
        ["SOC2"] = 92.3,
        ["NIST-CSF"] = 78.9
    };

    private double OverallCompliancePercentage => 
        FrameworkCompliance.Values.Any() ? FrameworkCompliance.Values.Average() : 0;

    private double GetFrameworkCompliance(string frameworkId)
    {
        return FrameworkCompliance.GetValueOrDefault(frameworkId, 0);
    }

    private string GetComplianceColor(double percentage)
    {
        return percentage switch
        {
            >= 90 => "success",
            >= 75 => "warning",
            >= 50 => "info",
            _ => "danger"
        };
    }

    private string GetComplianceStatus(double percentage)
    {
        return percentage switch
        {
            >= 95 => "Excellent",
            >= 85 => "Good",
            >= 70 => "Fair",
            >= 50 => "Poor",
            _ => "Critical"
        };
    }

    private string GetLastCheckTime(string frameworkId)
    {
        // Sample data - in real implementation, this would come from the database
        var lastCheck = DateTime.UtcNow.AddHours(-Random.Shared.Next(1, 72));
        var timeSpan = DateTime.UtcNow - lastCheck;
        
        return timeSpan switch
        {
            { TotalHours: < 1 } => "< 1h ago",
            { TotalHours: < 24 } => $"{(int)timeSpan.TotalHours}h ago",
            { TotalDays: < 7 } => $"{(int)timeSpan.TotalDays}d ago",
            _ => lastCheck.ToString("MMM dd")
        };
    }

    private async Task ViewFrameworkDetails(string frameworkId)
    {
        if (OnViewFramework.HasDelegate)
        {
            await OnViewFramework.InvokeAsync(frameworkId);
        }
    }

    private async Task RunComplianceCheck(string frameworkId)
    {
        if (OnRunCheck.HasDelegate)
        {
            await OnRunCheck.InvokeAsync(frameworkId);
        }
    }

    private async Task ShowAllFrameworks()
    {
        if (OnShowAll.HasDelegate)
        {
            await OnShowAll.InvokeAsync();
        }
    }

    private async Task ConfigureFrameworks()
    {
        if (OnConfigure.HasDelegate)
        {
            await OnConfigure.InvokeAsync();
        }
    }
}

<style>
    .compliance-overview {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .compliance-list {
        flex: 1;
        overflow-y: auto;
        max-height: 350px;
    }

    .compliance-item {
        padding: 16px 0;
        border-bottom: 1px solid #f1f3f4;
    }

    .compliance-item:last-child {
        border-bottom: none;
    }

    .compliance-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 12px;
    }

    .framework-info {
        flex: 1;
    }

    .framework-name {
        margin: 0 0 2px 0;
        font-weight: 600;
        font-size: 0.9rem;
        color: #495057;
    }

    .framework-version {
        font-size: 0.75rem;
    }

    .compliance-score {
        text-align: right;
    }

    .score-value {
        font-size: 1.25rem;
        font-weight: 700;
    }

    .compliance-progress {
        margin-bottom: 12px;
    }

    .progress {
        height: 6px;
        background-color: #e9ecef;
    }

    .compliance-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 0.8rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .detail-label {
        color: #6c757d;
        font-weight: 500;
    }

    .detail-value {
        font-weight: 600;
        color: #495057;
    }

    .compliance-actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
    }

    .compliance-actions .btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }

    .compliance-footer {
        border-top: 1px solid #f1f3f4;
        padding-top: 12px;
        text-align: center;
        margin-top: 12px;
    }

    .compliance-footer .btn-link {
        color: #6c757d;
        text-decoration: none;
        font-size: 0.875rem;
    }

    .compliance-footer .btn-link:hover {
        color: #495057;
        text-decoration: underline;
    }

    .overall-compliance {
        border-top: 2px solid #f1f3f4;
        padding-top: 16px;
        margin-top: 16px;
    }

    .overall-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .overall-header h6 {
        margin: 0;
        font-weight: 600;
        color: #495057;
    }

    .overall-score {
        font-size: 1.1rem;
        font-weight: 700;
    }

    .no-frameworks-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        gap: 12px;
    }

    .no-frameworks-message i {
        font-size: 2rem;
    }

    .no-frameworks-message p {
        margin: 0;
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 2px 6px;
    }

    /* Custom scrollbar */
    .compliance-list::-webkit-scrollbar {
        width: 4px;
    }

    .compliance-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .compliance-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .compliance-list::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>
