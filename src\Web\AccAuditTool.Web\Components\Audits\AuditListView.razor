@using AccAuditTool.Domain.Entities

<div class="audit-list-view">
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(AuditRun.Name))">
                            Name
                            @if (SortColumn == nameof(AuditRun.Name))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(AuditRun.Status))">
                            Status
                            @if (SortColumn == nameof(AuditRun.Status))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(AuditRun.StartedAt))">
                            Started
                            @if (SortColumn == nameof(AuditRun.StartedAt))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">Duration</th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(AuditRun.TotalFindings))">
                            Findings
                            @if (SortColumn == nameof(AuditRun.TotalFindings))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">Type</th>
                    <th scope="col" class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var audit in SortedAudits)
                {
                    <tr class="audit-row" data-audit-id="@audit.Id">
                        <td>
                            <div class="audit-name-cell">
                                <div class="audit-name">@audit.Name</div>
                                @if (!string.IsNullOrEmpty(audit.Description))
                                {
                                    <div class="audit-description">@TruncateText(audit.Description, 60)</div>
                                }
                                <div class="audit-meta">
                                    <small class="text-muted">
                                        <i class="fas fa-building"></i>
                                        @audit.AccountId
                                        @if (audit.ProjectIds.Any())
                                        {
                                            <span class="ms-2">
                                                <i class="fas fa-folder"></i>
                                                @audit.ProjectIds.Count project(s)
                                            </span>
                                        }
                                    </small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-@GetStatusColor(audit.Status) audit-status-badge">
                                <i class="@GetStatusIcon(audit.Status)"></i>
                                @audit.Status
                            </span>
                            @if (audit.Status == AuditRunStatus.Running)
                            {
                                <div class="progress mt-1" style="height: 4px;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" 
                                         style="width: @GetProgressPercentage(audit)%">
                                    </div>
                                </div>
                            }
                        </td>
                        <td>
                            <div class="date-cell">
                                <div class="date-primary">@audit.StartedAt.ToString("MMM dd, yyyy")</div>
                                <div class="date-secondary">@audit.StartedAt.ToString("HH:mm")</div>
                            </div>
                        </td>
                        <td>
                            <div class="duration-cell">
                                @if (audit.CompletedAt.HasValue)
                                {
                                    <span class="duration-completed">@GetDuration(audit.StartedAt, audit.CompletedAt.Value)</span>
                                }
                                else if (audit.Status == AuditRunStatus.Running)
                                {
                                    <span class="duration-running">@GetDuration(audit.StartedAt, DateTime.UtcNow)</span>
                                    <small class="text-muted d-block">running</small>
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </div>
                        </td>
                        <td>
                            @if (audit.TotalFindings > 0)
                            {
                                <div class="findings-cell">
                                    <span class="findings-count">@audit.TotalFindings</span>
                                    <small class="text-muted d-block">findings</small>
                                </div>
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </td>
                        <td>
                            <span class="badge bg-light text-dark audit-type-badge">
                                <i class="@GetTypeIcon(audit.AuditType)"></i>
                                @(audit.AuditType ?? "Manual")
                            </span>
                        </td>
                        <td class="text-end">
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" 
                                        @onclick="() => OnViewAudit.InvokeAsync(audit.Id)"
                                        data-bs-toggle="tooltip" 
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                @if (audit.Status == AuditRunStatus.Running)
                                {
                                    <button class="btn btn-outline-warning btn-sm" 
                                            @onclick="() => OnCancelAudit.InvokeAsync(audit.Id)"
                                            data-bs-toggle="tooltip" 
                                            title="Cancel Audit">
                                        <i class="fas fa-stop"></i>
                                    </button>
                                }
                                else if (audit.Status == AuditRunStatus.Failed || audit.Status == AuditRunStatus.Cancelled)
                                {
                                    <button class="btn btn-outline-success btn-sm" 
                                            @onclick="() => OnRestartAudit.InvokeAsync(audit.Id)"
                                            data-bs-toggle="tooltip" 
                                            title="Restart Audit">
                                        <i class="fas fa-redo"></i>
                                    </button>
                                }
                                
                                @if (audit.Status != AuditRunStatus.Running)
                                {
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            @onclick="() => OnEditAudit.InvokeAsync(audit.Id)"
                                            data-bs-toggle="tooltip" 
                                            title="Edit Audit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" 
                                            @onclick="() => OnDeleteAudit.InvokeAsync(audit.Id)"
                                            data-bs-toggle="tooltip" 
                                            title="Delete Audit">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@code {
    [Parameter] public List<AuditRun> Audits { get; set; } = new();
    [Parameter] public EventCallback<Guid> OnViewAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnEditAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnDeleteAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnCancelAudit { get; set; }
    [Parameter] public EventCallback<Guid> OnRestartAudit { get; set; }

    private string SortColumn = nameof(AuditRun.StartedAt);
    private string SortDirection = "desc";

    private List<AuditRun> SortedAudits => SortAudits(Audits);

    private List<AuditRun> SortAudits(List<AuditRun> audits)
    {
        return SortColumn switch
        {
            nameof(AuditRun.Name) => SortDirection == "asc" 
                ? audits.OrderBy(a => a.Name).ToList()
                : audits.OrderByDescending(a => a.Name).ToList(),
            nameof(AuditRun.Status) => SortDirection == "asc" 
                ? audits.OrderBy(a => a.Status).ToList()
                : audits.OrderByDescending(a => a.Status).ToList(),
            nameof(AuditRun.StartedAt) => SortDirection == "asc" 
                ? audits.OrderBy(a => a.StartedAt).ToList()
                : audits.OrderByDescending(a => a.StartedAt).ToList(),
            nameof(AuditRun.TotalFindings) => SortDirection == "asc" 
                ? audits.OrderBy(a => a.TotalFindings).ToList()
                : audits.OrderByDescending(a => a.TotalFindings).ToList(),
            _ => audits.OrderByDescending(a => a.StartedAt).ToList()
        };
    }

    private void SortBy(string column)
    {
        if (SortColumn == column)
        {
            SortDirection = SortDirection == "asc" ? "desc" : "asc";
        }
        else
        {
            SortColumn = column;
            SortDirection = "asc";
        }
        StateHasChanged();
    }

    private string GetStatusColor(AuditRunStatus status)
    {
        return status switch
        {
            AuditRunStatus.Pending => "secondary",
            AuditRunStatus.Running => "primary",
            AuditRunStatus.Completed => "success",
            AuditRunStatus.Failed => "danger",
            AuditRunStatus.Cancelled => "warning",
            _ => "secondary"
        };
    }

    private string GetStatusIcon(AuditRunStatus status)
    {
        return status switch
        {
            AuditRunStatus.Pending => "fas fa-clock",
            AuditRunStatus.Running => "fas fa-spinner fa-spin",
            AuditRunStatus.Completed => "fas fa-check-circle",
            AuditRunStatus.Failed => "fas fa-times-circle",
            AuditRunStatus.Cancelled => "fas fa-ban",
            _ => "fas fa-question-circle"
        };
    }

    private string GetTypeIcon(string? auditType)
    {
        return auditType?.ToLowerInvariant() switch
        {
            "scheduled" => "fas fa-calendar-alt",
            "triggered" => "fas fa-bolt",
            "manual" => "fas fa-hand-paper",
            _ => "fas fa-hand-paper"
        };
    }

    private string GetDuration(DateTime start, DateTime end)
    {
        var duration = end - start;
        
        if (duration.TotalDays >= 1)
        {
            return $"{(int)duration.TotalDays}d {duration.Hours}h";
        }
        else if (duration.TotalHours >= 1)
        {
            return $"{(int)duration.TotalHours}h {duration.Minutes}m";
        }
        else
        {
            return $"{(int)duration.TotalMinutes}m";
        }
    }

    private int GetProgressPercentage(AuditRun audit)
    {
        // Simple progress calculation - in real implementation, this would be more sophisticated
        if (audit.Status != AuditRunStatus.Running)
            return 0;

        var elapsed = DateTime.UtcNow - audit.StartedAt;
        var estimatedTotal = TimeSpan.FromMinutes(30); // Estimated 30 minutes
        var percentage = (int)((elapsed.TotalMinutes / estimatedTotal.TotalMinutes) * 100);
        return Math.Min(95, Math.Max(5, percentage)); // Keep between 5% and 95%
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }
}

<style>
    .audit-list-view {
        padding: 0;
    }

    .table {
        margin: 0;
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    .table th .btn-link {
        color: #495057;
        text-decoration: none;
        font-weight: 600;
        white-space: nowrap;
    }

    .table th .btn-link:hover {
        color: #007bff;
    }

    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .audit-row:hover {
        background-color: #f8f9fa;
    }

    .audit-name-cell {
        min-width: 200px;
    }

    .audit-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .audit-description {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
        line-height: 1.3;
    }

    .audit-meta {
        font-size: 0.8rem;
    }

    .audit-meta i {
        width: 12px;
        text-align: center;
        margin-right: 0.25rem;
    }

    .audit-status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
        border-radius: 0.375rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .audit-type-badge {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        border: 1px solid #dee2e6;
    }

    .date-cell {
        min-width: 100px;
    }

    .date-primary {
        font-weight: 500;
        color: #495057;
    }

    .date-secondary {
        font-size: 0.8rem;
        color: #6c757d;
    }

    .duration-cell {
        min-width: 80px;
    }

    .duration-completed {
        color: #495057;
        font-weight: 500;
    }

    .duration-running {
        color: #007bff;
        font-weight: 500;
    }

    .findings-cell {
        text-align: center;
    }

    .findings-count {
        font-weight: 600;
        color: #495057;
        font-size: 1.1rem;
    }

    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .action-buttons .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }

    .progress {
        background-color: rgba(0, 123, 255, 0.1);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.875rem;
        }

        .table th,
        .table td {
            padding: 0.75rem 0.5rem;
        }

        .audit-name-cell {
            min-width: 150px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.125rem;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.375rem;
            font-size: 0.75rem;
        }
    }

    @media (max-width: 576px) {
        .table th:nth-child(4),
        .table td:nth-child(4),
        .table th:nth-child(6),
        .table td:nth-child(6) {
            display: none;
        }
    }
</style>
