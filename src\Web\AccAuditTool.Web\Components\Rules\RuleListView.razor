@using AccAuditTool.Domain.Interfaces

<div class="rule-list-view">
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th scope="col" style="width: 40px;">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   @onchange="ToggleSelectAll"
                                   checked="@IsAllSelected" />
                        </div>
                    </th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(IAuditRule.Name))">
                            Rule
                            @if (SortColumn == nameof(IAuditRule.Name))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(IAuditRule.Category))">
                            Category
                            @if (SortColumn == nameof(IAuditRule.Category))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">
                        <button class="btn btn-link p-0 text-start" @onclick="() => SortBy(nameof(IAuditRule.Severity))">
                            Severity
                            @if (SortColumn == nameof(IAuditRule.Severity))
                            {
                                <i class="fas fa-sort-@(SortDirection == "asc" ? "up" : "down") ms-1"></i>
                            }
                        </button>
                    </th>
                    <th scope="col">Status</th>
                    <th scope="col">Type</th>
                    <th scope="col">Dependencies</th>
                    <th scope="col" class="text-end">Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var rule in SortedRules)
                {
                    <tr class="rule-row" data-rule-id="@rule.RuleId">
                        <td>
                            <div class="form-check">
                                <input class="form-check-input" 
                                       type="checkbox" 
                                       checked="@SelectedRules.Contains(rule.RuleId)"
                                       @onchange="(e) => OnRuleSelected.InvokeAsync((rule.RuleId, (bool)e.Value!))" />
                            </div>
                        </td>
                        <td>
                            <div class="rule-name-cell">
                                <div class="rule-name">@rule.Name</div>
                                <div class="rule-id">@rule.RuleId</div>
                                @if (!string.IsNullOrEmpty(rule.Description))
                                {
                                    <div class="rule-description">@TruncateText(rule.Description, 80)</div>
                                }
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-@GetCategoryColor(rule.Category) category-badge">
                                <i class="@GetCategoryIcon(rule.Category)"></i>
                                @rule.Category
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-@GetSeverityColor(rule.Severity) severity-badge">
                                @rule.Severity
                            </span>
                        </td>
                        <td>
                            <div class="status-cell">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           checked="@rule.IsEnabled"
                                           @onchange="() => OnToggleRule.InvokeAsync(rule.RuleId)" />
                                    <label class="form-check-label">
                                        @(rule.IsEnabled ? "Enabled" : "Disabled")
                                    </label>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-light text-dark type-badge">
                                <i class="@GetTypeIcon(rule.RuleId)"></i>
                                @GetRuleType(rule.RuleId)
                            </span>
                        </td>
                        <td>
                            <div class="dependencies-cell">
                                @{
                                    var dependencies = rule.GetDependencies();
                                }
                                @if (dependencies.Any())
                                {
                                    <span class="dependencies-count" 
                                          data-bs-toggle="tooltip" 
                                          title="@string.Join(", ", dependencies)">
                                        <i class="fas fa-link"></i>
                                        @dependencies.Count()
                                    </span>
                                }
                                else
                                {
                                    <span class="text-muted">None</span>
                                }
                            </div>
                        </td>
                        <td class="text-end">
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" 
                                        @onclick="() => OnViewRule.InvokeAsync(rule.RuleId)"
                                        data-bs-toggle="tooltip" 
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                
                                <button class="btn btn-outline-info btn-sm" 
                                        @onclick="() => OnTestRule.InvokeAsync(rule.RuleId)"
                                        data-bs-toggle="tooltip" 
                                        title="Test Rule">
                                    <i class="fas fa-vial"></i>
                                </button>
                                
                                @if (IsCustomRule(rule.RuleId))
                                {
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            @onclick="() => OnEditRule.InvokeAsync(rule.RuleId)"
                                            data-bs-toggle="tooltip" 
                                            title="Edit Rule">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                }
                                
                                <button class="btn btn-outline-success btn-sm" 
                                        @onclick="() => OnDuplicateRule.InvokeAsync(rule.RuleId)"
                                        data-bs-toggle="tooltip" 
                                        title="Duplicate Rule">
                                    <i class="fas fa-copy"></i>
                                </button>
                                
                                @if (IsCustomRule(rule.RuleId))
                                {
                                    <button class="btn btn-outline-danger btn-sm" 
                                            @onclick="() => OnDeleteRule.InvokeAsync(rule.RuleId)"
                                            data-bs-toggle="tooltip" 
                                            title="Delete Rule">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                }
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

@code {
    [Parameter] public List<IAuditRule> Rules { get; set; } = new();
    [Parameter] public HashSet<string> SelectedRules { get; set; } = new();
    [Parameter] public EventCallback<(string RuleId, bool IsSelected)> OnRuleSelected { get; set; }
    [Parameter] public EventCallback<string> OnViewRule { get; set; }
    [Parameter] public EventCallback<string> OnEditRule { get; set; }
    [Parameter] public EventCallback<string> OnTestRule { get; set; }
    [Parameter] public EventCallback<string> OnToggleRule { get; set; }
    [Parameter] public EventCallback<string> OnDeleteRule { get; set; }
    [Parameter] public EventCallback<string> OnDuplicateRule { get; set; }

    private string SortColumn = nameof(IAuditRule.Name);
    private string SortDirection = "asc";

    private List<IAuditRule> SortedRules => SortRules(Rules);
    private bool IsAllSelected => Rules.Any() && Rules.All(r => SelectedRules.Contains(r.RuleId));

    private List<IAuditRule> SortRules(List<IAuditRule> rules)
    {
        return SortColumn switch
        {
            nameof(IAuditRule.Name) => SortDirection == "asc" 
                ? rules.OrderBy(r => r.Name).ToList()
                : rules.OrderByDescending(r => r.Name).ToList(),
            nameof(IAuditRule.Category) => SortDirection == "asc" 
                ? rules.OrderBy(r => r.Category).ToList()
                : rules.OrderByDescending(r => r.Category).ToList(),
            nameof(IAuditRule.Severity) => SortDirection == "asc" 
                ? rules.OrderBy(r => r.Severity).ToList()
                : rules.OrderByDescending(r => r.Severity).ToList(),
            _ => rules.OrderBy(r => r.Name).ToList()
        };
    }

    private void SortBy(string column)
    {
        if (SortColumn == column)
        {
            SortDirection = SortDirection == "asc" ? "desc" : "asc";
        }
        else
        {
            SortColumn = column;
            SortDirection = "asc";
        }
        StateHasChanged();
    }

    private async Task ToggleSelectAll(ChangeEventArgs e)
    {
        var isSelected = (bool)e.Value!;
        
        if (isSelected)
        {
            foreach (var rule in Rules)
            {
                SelectedRules.Add(rule.RuleId);
            }
        }
        else
        {
            SelectedRules.Clear();
        }
        
        StateHasChanged();
    }

    private string GetCategoryColor(AuditRuleCategory category)
    {
        return category switch
        {
            AuditRuleCategory.Security => "danger",
            AuditRuleCategory.Compliance => "warning",
            AuditRuleCategory.AccessControl => "info",
            AuditRuleCategory.DataIntegrity => "primary",
            AuditRuleCategory.Performance => "success",
            AuditRuleCategory.Configuration => "secondary",
            _ => "light"
        };
    }

    private string GetCategoryIcon(AuditRuleCategory category)
    {
        return category switch
        {
            AuditRuleCategory.Security => "fas fa-shield-alt",
            AuditRuleCategory.Compliance => "fas fa-clipboard-check",
            AuditRuleCategory.AccessControl => "fas fa-key",
            AuditRuleCategory.DataIntegrity => "fas fa-database",
            AuditRuleCategory.Performance => "fas fa-tachometer-alt",
            AuditRuleCategory.Configuration => "fas fa-cog",
            _ => "fas fa-cube"
        };
    }

    private string GetSeverityColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "danger",
            AuditSeverity.High => "warning",
            AuditSeverity.Medium => "info",
            AuditSeverity.Low => "secondary",
            AuditSeverity.Info => "light",
            _ => "secondary"
        };
    }

    private string GetTypeIcon(string ruleId)
    {
        return IsCustomRule(ruleId) ? "fas fa-user-cog" : "fas fa-cogs";
    }

    private string GetRuleType(string ruleId)
    {
        return IsCustomRule(ruleId) ? "Custom" : "Built-in";
    }

    private bool IsCustomRule(string ruleId)
    {
        return ruleId.StartsWith("CUSTOM-");
    }

    private string TruncateText(string text, int maxLength)
    {
        if (string.IsNullOrEmpty(text) || text.Length <= maxLength)
            return text;

        return text.Substring(0, maxLength) + "...";
    }
}

<style>
    .rule-list-view {
        padding: 0;
    }

    .table {
        margin: 0;
    }

    .table th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        color: #495057;
        padding: 1rem 0.75rem;
        vertical-align: middle;
    }

    .table th .btn-link {
        color: #495057;
        text-decoration: none;
        font-weight: 600;
        white-space: nowrap;
    }

    .table th .btn-link:hover {
        color: #007bff;
    }

    .table td {
        padding: 1rem 0.75rem;
        vertical-align: middle;
        border-bottom: 1px solid #f1f3f4;
    }

    .rule-row:hover {
        background-color: #f8f9fa;
    }

    .rule-name-cell {
        min-width: 250px;
    }

    .rule-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .rule-id {
        font-family: 'Courier New', monospace;
        font-size: 0.8rem;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 0.125rem 0.375rem;
        border-radius: 0.25rem;
        display: inline-block;
        margin-bottom: 0.25rem;
    }

    .rule-description {
        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.3;
    }

    .category-badge,
    .severity-badge {
        font-size: 0.75rem;
        padding: 0.3rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .type-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.4rem;
        border-radius: 0.2rem;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        border: 1px solid #dee2e6;
    }

    .status-cell .form-check {
        margin: 0;
    }

    .status-cell .form-check-label {
        font-size: 0.875rem;
        color: #495057;
        margin-left: 0.5rem;
    }

    .dependencies-cell {
        text-align: center;
    }

    .dependencies-count {
        color: #007bff;
        font-weight: 500;
        cursor: help;
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
    }

    .action-buttons {
        display: flex;
        gap: 0.25rem;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .action-buttons .btn {
        padding: 0.375rem 0.5rem;
        font-size: 0.8rem;
    }

    /* Form controls */
    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }

    .form-switch .form-check-input {
        width: 2em;
        margin-left: -2.5em;
    }

    .form-switch .form-check-input:checked {
        background-color: #198754;
        border-color: #198754;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.875rem;
        }

        .table th,
        .table td {
            padding: 0.75rem 0.5rem;
        }

        .rule-name-cell {
            min-width: 200px;
        }

        .action-buttons {
            flex-direction: column;
            gap: 0.125rem;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.375rem;
            font-size: 0.75rem;
        }
    }

    @media (max-width: 576px) {
        .table th:nth-child(6),
        .table td:nth-child(6),
        .table th:nth-child(7),
        .table td:nth-child(7) {
            display: none;
        }
    }
</style>
