using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AccAuditTool.Infrastructure.Services;

/// <summary>
/// Service for monitoring system health and performance
/// </summary>
public interface IHealthCheckService
{
    /// <summary>
    /// Perform comprehensive health check
    /// </summary>
    Task<SystemHealthStatus> CheckSystemHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check database connectivity
    /// </summary>
    Task<ComponentHealthStatus> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check APS API connectivity
    /// </summary>
    Task<ComponentHealthStatus> CheckApsApiHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Check rate limiting status
    /// </summary>
    Task<ComponentHealthStatus> CheckRateLimitHealthAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get performance metrics
    /// </summary>
    Task<PerformanceMetrics> GetPerformanceMetricsAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Overall system health status
/// </summary>
public class SystemHealthStatus
{
    public bool IsHealthy { get; set; }
    public DateTime CheckedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan ResponseTime { get; set; }
    public List<ComponentHealthStatus> Components { get; set; } = new();
    public PerformanceMetrics Performance { get; set; } = new();
    public string? OverallStatus => IsHealthy ? "Healthy" : "Unhealthy";
}

/// <summary>
/// Individual component health status
/// </summary>
public class ComponentHealthStatus
{
    public string Name { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public string Status { get; set; } = string.Empty;
    public TimeSpan ResponseTime { get; set; }
    public string? ErrorMessage { get; set; }
    public Dictionary<string, object> Details { get; set; } = new();
}

/// <summary>
/// System performance metrics
/// </summary>
public class PerformanceMetrics
{
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageMB { get; set; }
    public long AvailableMemoryMB { get; set; }
    public int ActiveConnections { get; set; }
    public int TotalRequests { get; set; }
    public int FailedRequests { get; set; }
    public double AverageResponseTimeMs { get; set; }
    public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Implementation of health check service
/// </summary>
public class HealthCheckService : IHealthCheckService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IApsApiClient _apsApiClient;
    private readonly IRateLimitService _rateLimitService;
    private readonly ILogger<HealthCheckService> _logger;

    public HealthCheckService(
        IUnitOfWork unitOfWork,
        IApsApiClient apsApiClient,
        IRateLimitService rateLimitService,
        ILogger<HealthCheckService> logger)
    {
        _unitOfWork = unitOfWork;
        _apsApiClient = apsApiClient;
        _rateLimitService = rateLimitService;
        _logger = logger;
    }

    public async Task<SystemHealthStatus> CheckSystemHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var healthStatus = new SystemHealthStatus();

        try
        {
            _logger.LogInformation("Starting system health check");

            // Check all components in parallel
            var tasks = new[]
            {
                CheckDatabaseHealthAsync(cancellationToken),
                CheckApsApiHealthAsync(cancellationToken),
                CheckRateLimitHealthAsync(cancellationToken)
            };

            var results = await Task.WhenAll(tasks);
            healthStatus.Components.AddRange(results);

            // Get performance metrics
            healthStatus.Performance = await GetPerformanceMetricsAsync(cancellationToken);

            // Determine overall health
            healthStatus.IsHealthy = healthStatus.Components.All(c => c.IsHealthy);
            healthStatus.ResponseTime = stopwatch.Elapsed;

            _logger.LogInformation("System health check completed. Status: {Status}, Response time: {ResponseTime}ms",
                healthStatus.OverallStatus, healthStatus.ResponseTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during system health check");
            healthStatus.IsHealthy = false;
            healthStatus.Components.Add(new ComponentHealthStatus
            {
                Name = "System",
                IsHealthy = false,
                Status = "Error",
                ErrorMessage = ex.Message,
                ResponseTime = stopwatch.Elapsed
            });
        }

        return healthStatus;
    }

    public async Task<ComponentHealthStatus> CheckDatabaseHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var status = new ComponentHealthStatus { Name = "Database" };

        try
        {
            // Simple connectivity test
            var accountCount = await _unitOfWork.Accounts.CountAsync(cancellationToken: cancellationToken);
            
            status.IsHealthy = true;
            status.Status = "Connected";
            status.ResponseTime = stopwatch.Elapsed;
            status.Details["AccountCount"] = accountCount;
            status.Details["ConnectionString"] = "***"; // Masked for security

            _logger.LogDebug("Database health check passed. Response time: {ResponseTime}ms", 
                status.ResponseTime.TotalMilliseconds);
        }
        catch (Exception ex)
        {
            status.IsHealthy = false;
            status.Status = "Error";
            status.ErrorMessage = ex.Message;
            status.ResponseTime = stopwatch.Elapsed;

            _logger.LogError(ex, "Database health check failed");
        }

        return status;
    }

    public async Task<ComponentHealthStatus> CheckApsApiHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var status = new ComponentHealthStatus { Name = "APS API" };

        try
        {
            // Get API client health
            var apiHealth = _apsApiClient.GetHealthStatus();
            
            status.IsHealthy = apiHealth.IsHealthy;
            status.Status = apiHealth.IsHealthy ? "Connected" : "Degraded";
            status.ResponseTime = stopwatch.Elapsed;
            status.Details["TotalRequests"] = apiHealth.TotalRequests;
            status.Details["SuccessRate"] = $"{apiHealth.SuccessRate:F1}%";
            status.Details["AverageResponseTime"] = $"{apiHealth.AverageResponseTime.TotalMilliseconds:F0}ms";
            status.Details["LastRequestAt"] = apiHealth.LastRequestAt;

            if (!apiHealth.IsHealthy)
            {
                status.ErrorMessage = $"API health degraded. Success rate: {apiHealth.SuccessRate:F1}%";
            }

            _logger.LogDebug("APS API health check completed. Status: {Status}, Success rate: {SuccessRate}%", 
                status.Status, apiHealth.SuccessRate);
        }
        catch (Exception ex)
        {
            status.IsHealthy = false;
            status.Status = "Error";
            status.ErrorMessage = ex.Message;
            status.ResponseTime = stopwatch.Elapsed;

            _logger.LogError(ex, "APS API health check failed");
        }

        return status;
    }

    public async Task<ComponentHealthStatus> CheckRateLimitHealthAsync(CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var status = new ComponentHealthStatus { Name = "Rate Limiting" };

        try
        {
            var rateLimitStatus = _rateLimitService.GetStatus();
            
            status.IsHealthy = rateLimitStatus.AvailableTokens > 0;
            status.Status = status.IsHealthy ? "Available" : "Exhausted";
            status.ResponseTime = stopwatch.Elapsed;
            status.Details["AvailableTokens"] = rateLimitStatus.AvailableTokens;
            status.Details["MaxTokens"] = rateLimitStatus.MaxTokens;
            status.Details["CurrentLimit"] = rateLimitStatus.CurrentLimit;
            status.Details["NextRefill"] = rateLimitStatus.NextRefill;

            if (!status.IsHealthy)
            {
                status.ErrorMessage = "Rate limit tokens exhausted";
            }

            _logger.LogDebug("Rate limit health check completed. Available tokens: {AvailableTokens}/{MaxTokens}", 
                rateLimitStatus.AvailableTokens, rateLimitStatus.MaxTokens);
        }
        catch (Exception ex)
        {
            status.IsHealthy = false;
            status.Status = "Error";
            status.ErrorMessage = ex.Message;
            status.ResponseTime = stopwatch.Elapsed;

            _logger.LogError(ex, "Rate limit health check failed");
        }

        return status;
    }

    public async Task<PerformanceMetrics> GetPerformanceMetricsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var process = Process.GetCurrentProcess();
            var metrics = new PerformanceMetrics();

            // Memory metrics
            metrics.MemoryUsageMB = process.WorkingSet64 / 1024 / 1024;
            
            // Get available memory (simplified)
            var gc = GC.GetTotalMemory(false);
            metrics.AvailableMemoryMB = (GC.GetTotalMemory(false) / 1024 / 1024);

            // Get API client metrics
            var apiHealth = _apsApiClient.GetHealthStatus();
            metrics.TotalRequests = apiHealth.TotalRequests;
            metrics.FailedRequests = apiHealth.FailedRequests;
            metrics.AverageResponseTimeMs = apiHealth.AverageResponseTime.TotalMilliseconds;

            _logger.LogTrace("Performance metrics collected. Memory: {MemoryMB}MB, Requests: {TotalRequests}",
                metrics.MemoryUsageMB, metrics.TotalRequests);

            return metrics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error collecting performance metrics");
            return new PerformanceMetrics();
        }
    }
}
