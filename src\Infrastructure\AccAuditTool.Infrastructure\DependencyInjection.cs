using AccAuditTool.Application.Interfaces;
using AccAuditTool.Domain.Interfaces;
using AccAuditTool.Infrastructure.Configuration;
using AccAuditTool.Infrastructure.Data;
using AccAuditTool.Infrastructure.Repositories;
using AccAuditTool.Infrastructure.Services;
using AccAuditTool.Infrastructure.Validation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace AccAuditTool.Infrastructure;

/// <summary>
/// Extension methods for configuring infrastructure services
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Add infrastructure services to the service collection
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
    {
        // Database configuration
        services.AddDbContext<AccAuditDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

        // Repository pattern
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

        // Application services
        services.AddScoped<IAuditService, AuditService>();
        services.AddScoped<IDataSyncService, DataSyncService>();
        services.AddScoped<IReportService, ReportService>();
        services.AddScoped<IConfigurationService, ConfigurationService>();

        // APS API configuration and services
        services.Configure<ApsApiOptions>(configuration.GetSection(ApsApiOptions.SectionName));
        services.AddApsApiServices(configuration);

        // Performance and resilience services
        services.AddPerformanceServices();

        // Validation services
        services.AddValidationServices();

        // Health monitoring
        services.AddHealthServices();

        return services;
    }

    /// <summary>
    /// Add APS API related services
    /// </summary>
    private static IServiceCollection AddApsApiServices(this IServiceCollection services, IConfiguration configuration)
    {
        // Authentication service
        services.AddScoped<IApsAuthenticationService, ApsAuthenticationService>();

        // Rate limiting service
        services.AddSingleton<IRateLimitService, RateLimitService>();

        // Error handling service
        services.AddScoped<IErrorHandlingService, ErrorHandlingService>();

        // HTTP client configuration for APS API
        services.AddHttpClient<IApsApiClient, ApsApiClient>((serviceProvider, client) =>
        {
            var apsOptions = configuration.GetSection(ApsApiOptions.SectionName).Get<ApsApiOptions>();
            client.BaseAddress = new Uri(apsOptions?.BaseUrl ?? "https://developer.api.autodesk.com");
            client.Timeout = TimeSpan.FromSeconds(apsOptions?.Timeout.RequestTimeoutSeconds ?? 30);
            
            // Add standard headers
            client.DefaultRequestHeaders.Add("User-Agent", "AccAuditTool/1.0");
            client.DefaultRequestHeaders.Add("Accept", "application/json");
        })
        .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler())
        .AddPolicyHandler(GetRetryPolicy())
        .AddPolicyHandler(GetCircuitBreakerPolicy())
        .AddPolicyHandler(GetTimeoutPolicy());

        // ACC data service
        services.AddScoped<IAccDataService, AccDataService>();

        return services;
    }

    /// <summary>
    /// Add performance optimization services
    /// </summary>
    private static IServiceCollection AddPerformanceServices(this IServiceCollection services)
    {
        // Memory caching
        services.AddMemoryCache();
        services.AddScoped<ICachingService, CachingService>();

        // Batch processing
        services.AddScoped<IBatchProcessingService, BatchProcessingService>();

        return services;
    }

    /// <summary>
    /// Add validation services
    /// </summary>
    private static IServiceCollection AddValidationServices(this IServiceCollection services)
    {
        services.AddScoped<IDataValidationService, DataValidationService>();
        return services;
    }

    /// <summary>
    /// Add health monitoring services
    /// </summary>
    private static IServiceCollection AddHealthServices(this IServiceCollection services)
    {
        services.AddScoped<IHealthCheckService, HealthCheckService>();
        
        // Add ASP.NET Core health checks
        services.AddHealthChecks()
            .AddDbContextCheck<AccAuditDbContext>("database")
            .AddCheck<ApsApiHealthCheck>("aps-api")
            .AddCheck<RateLimitHealthCheck>("rate-limit");

        return services;
    }

    /// <summary>
    /// Get retry policy for HTTP requests
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return Policy
            .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
            .Or<HttpRequestException>()
            .Or<TaskCanceledException>()
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    var logger = context.GetLogger();
                    logger?.LogWarning("Retry {RetryCount} for {OperationKey} in {Delay}ms",
                        retryCount, context.OperationKey, timespan.TotalMilliseconds);
                });
    }

    /// <summary>
    /// Get circuit breaker policy for HTTP requests
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return Policy
            .HandleResult<HttpResponseMessage>(r => !r.IsSuccessStatusCode)
            .Or<HttpRequestException>()
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 5,
                durationOfBreak: TimeSpan.FromSeconds(30),
                onBreak: (result, timespan) =>
                {
                    // Log circuit breaker opened
                },
                onReset: () =>
                {
                    // Log circuit breaker closed
                });
    }

    /// <summary>
    /// Get timeout policy for HTTP requests
    /// </summary>
    private static IAsyncPolicy<HttpResponseMessage> GetTimeoutPolicy()
    {
        return Policy.TimeoutAsync<HttpResponseMessage>(30);
    }
}

/// <summary>
/// Health check for APS API connectivity
/// </summary>
public class ApsApiHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IApsApiClient _apiClient;
    private readonly ILogger<ApsApiHealthCheck> _logger;

    public ApsApiHealthCheck(IApsApiClient apiClient, ILogger<ApsApiHealthCheck> logger)
    {
        _apiClient = apiClient;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var health = _apiClient.GetHealthStatus();
            
            if (health.IsHealthy)
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                    "APS API is healthy",
                    new Dictionary<string, object>
                    {
                        ["SuccessRate"] = health.SuccessRate,
                        ["TotalRequests"] = health.TotalRequests,
                        ["AverageResponseTime"] = health.AverageResponseTime.TotalMilliseconds
                    });
            }
            else
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded(
                    "APS API is degraded",
                    data: new Dictionary<string, object>
                    {
                        ["SuccessRate"] = health.SuccessRate,
                        ["FailedRequests"] = health.FailedRequests
                    });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "APS API health check failed");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "APS API health check failed", ex);
        }
    }
}

/// <summary>
/// Health check for rate limiting status
/// </summary>
public class RateLimitHealthCheck : Microsoft.Extensions.Diagnostics.HealthChecks.IHealthCheck
{
    private readonly IRateLimitService _rateLimitService;
    private readonly ILogger<RateLimitHealthCheck> _logger;

    public RateLimitHealthCheck(IRateLimitService rateLimitService, ILogger<RateLimitHealthCheck> logger)
    {
        _rateLimitService = rateLimitService;
        _logger = logger;
    }

    public async Task<Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult> CheckHealthAsync(
        Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var status = _rateLimitService.GetStatus();
            
            var data = new Dictionary<string, object>
            {
                ["AvailableTokens"] = status.AvailableTokens,
                ["MaxTokens"] = status.MaxTokens,
                ["CurrentLimit"] = status.CurrentLimit,
                ["NextRefill"] = status.NextRefill
            };

            if (status.AvailableTokens > status.MaxTokens * 0.2) // More than 20% available
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Healthy(
                    "Rate limiting is healthy", data);
            }
            else if (status.AvailableTokens > 0)
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Degraded(
                    "Rate limiting is degraded", data: data);
            }
            else
            {
                return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                    "Rate limit exhausted", data: data);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Rate limit health check failed");
            return Microsoft.Extensions.Diagnostics.HealthChecks.HealthCheckResult.Unhealthy(
                "Rate limit health check failed", ex);
        }
    }
}

/// <summary>
/// Extension methods for logging context
/// </summary>
internal static class ContextExtensions
{
    public static ILogger? GetLogger(this Polly.Context context)
    {
        if (context.TryGetValue("logger", out var logger))
        {
            return logger as ILogger;
        }
        return null;
    }
}
