@using AccAuditTool.Application.Services

<div class="metric-card card border-@Color">
    <div class="card-body">
        <div class="metric-header">
            <div class="metric-icon">
                <i class="@Icon text-@Color"></i>
            </div>
            <div class="metric-trend">
                @if (Trend != TrendDirection.Insufficient)
                {
                    <i class="fas @GetTrendIcon() text-@GetTrendColor()"></i>
                }
            </div>
        </div>
        <div class="metric-content">
            <h3 class="metric-value text-@Color">@Value</h3>
            <p class="metric-title">@Title</p>
            @if (!string.IsNullOrEmpty(Subtitle))
            {
                <small class="metric-subtitle text-muted">@Subtitle</small>
            }
        </div>
        @if (ShowProgress && ProgressValue.HasValue)
        {
            <div class="metric-progress">
                <div class="progress">
                    <div class="progress-bar bg-@Color" 
                         role="progressbar" 
                         style="width: @(ProgressValue.Value)%" 
                         aria-valuenow="@ProgressValue.Value" 
                         aria-valuemin="0" 
                         aria-valuemax="100">
                    </div>
                </div>
                <small class="progress-text text-muted">@ProgressText</small>
            </div>
        }
    </div>
    @if (IsClickable && OnClick.HasDelegate)
    {
        <div class="card-footer">
            <button class="btn btn-link btn-sm p-0" @onclick="OnClick">
                <i class="fas fa-arrow-right"></i>
                View Details
            </button>
        </div>
    }
</div>

@code {
    [Parameter] public string Title { get; set; } = string.Empty;
    [Parameter] public string Value { get; set; } = string.Empty;
    [Parameter] public string? Subtitle { get; set; }
    [Parameter] public string Icon { get; set; } = "fas fa-info-circle";
    [Parameter] public string Color { get; set; } = "primary";
    [Parameter] public TrendDirection Trend { get; set; } = TrendDirection.Insufficient;
    [Parameter] public bool ShowProgress { get; set; } = false;
    [Parameter] public double? ProgressValue { get; set; }
    [Parameter] public string? ProgressText { get; set; }
    [Parameter] public bool IsClickable { get; set; } = false;
    [Parameter] public EventCallback OnClick { get; set; }

    private string GetTrendIcon()
    {
        return Trend switch
        {
            TrendDirection.Increasing => "fa-arrow-up",
            TrendDirection.Decreasing => "fa-arrow-down",
            TrendDirection.Stable => "fa-minus",
            _ => "fa-question"
        };
    }

    private string GetTrendColor()
    {
        return Trend switch
        {
            TrendDirection.Increasing => Color == "danger" ? "danger" : "success",
            TrendDirection.Decreasing => Color == "danger" ? "success" : "danger",
            TrendDirection.Stable => "warning",
            _ => "muted"
        };
    }
}

<style>
    .metric-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        height: 100%;
    }

    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .metric-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(var(--bs-primary-rgb), 0.1);
    }

    .metric-icon i {
        font-size: 24px;
    }

    .metric-trend i {
        font-size: 16px;
    }

    .metric-content {
        text-align: left;
    }

    .metric-value {
        font-size: 2.5rem;
        font-weight: 700;
        line-height: 1;
        margin: 0 0 5px 0;
    }

    .metric-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .metric-subtitle {
        font-size: 0.75rem;
        display: block;
        margin-top: 5px;
    }

    .metric-progress {
        margin-top: 15px;
    }

    .progress {
        height: 6px;
        margin-bottom: 5px;
    }

    .progress-text {
        font-size: 0.75rem;
    }

    .card-footer {
        background-color: transparent;
        border-top: 1px solid rgba(0, 0, 0, 0.125);
        padding: 10px 20px;
    }

    .card-footer .btn-link {
        color: var(--bs-primary);
        text-decoration: none;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .card-footer .btn-link:hover {
        text-decoration: underline;
    }

    .card-footer .btn-link i {
        margin-left: 5px;
        font-size: 0.75rem;
    }

    /* Color variations for metric icons */
    .metric-card.border-primary .metric-icon {
        background-color: rgba(13, 110, 253, 0.1);
    }

    .metric-card.border-success .metric-icon {
        background-color: rgba(25, 135, 84, 0.1);
    }

    .metric-card.border-warning .metric-icon {
        background-color: rgba(255, 193, 7, 0.1);
    }

    .metric-card.border-danger .metric-icon {
        background-color: rgba(220, 53, 69, 0.1);
    }

    .metric-card.border-info .metric-icon {
        background-color: rgba(13, 202, 240, 0.1);
    }
</style>
