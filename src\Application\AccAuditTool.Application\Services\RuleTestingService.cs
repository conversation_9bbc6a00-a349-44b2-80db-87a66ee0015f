using AccAuditTool.Domain.Entities;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Service for testing and validating audit rules
/// </summary>
public interface IRuleTestingService
{
    /// <summary>
    /// Run unit tests for a specific rule
    /// </summary>
    Task<RuleTestSuite> RunRuleUnitTestsAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Run integration tests for a rule
    /// </summary>
    Task<RuleTestSuite> RunRuleIntegrationTestsAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Run performance benchmarks for a rule
    /// </summary>
    Task<RulePerformanceBenchmark> RunRulePerformanceBenchmarkAsync(string ruleId, PerformanceTestOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate rule against test scenarios
    /// </summary>
    Task<RuleValidationReport> ValidateRuleAsync(string ruleId, List<RuleTestScenario> scenarios, CancellationToken cancellationToken = default);

    /// <summary>
    /// Run comprehensive test suite for all rules
    /// </summary>
    Task<ComprehensiveTestReport> RunComprehensiveTestSuiteAsync(RuleTestOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Generate test data for rule testing
    /// </summary>
    Task<RuleTestData> GenerateTestDataAsync(string ruleId, TestDataGenerationOptions options, CancellationToken cancellationToken = default);

    /// <summary>
    /// Create test scenarios for a rule
    /// </summary>
    Task<List<RuleTestScenario>> CreateTestScenariosAsync(string ruleId, CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of rule testing service
/// </summary>
public class RuleTestingService : IRuleTestingService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IRuleRegistry _ruleRegistry;
    private readonly IRuleExecutionEngine _ruleExecutionEngine;
    private readonly ILogger<RuleTestingService> _logger;

    public RuleTestingService(
        IUnitOfWork unitOfWork,
        IRuleRegistry ruleRegistry,
        IRuleExecutionEngine ruleExecutionEngine,
        ILogger<RuleTestingService> logger)
    {
        _unitOfWork = unitOfWork;
        _ruleRegistry = ruleRegistry;
        _ruleExecutionEngine = ruleExecutionEngine;
        _logger = logger;
    }

    public async Task<RuleTestSuite> RunRuleUnitTestsAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Running unit tests for rule {RuleId}", ruleId);

        var testSuite = new RuleTestSuite
        {
            RuleId = ruleId,
            TestType = RuleTestType.Unit,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
            if (rule == null)
            {
                testSuite.Success = false;
                testSuite.ErrorMessage = $"Rule {ruleId} not found";
                return testSuite;
            }

            // Test 1: Rule validation
            var validationTest = await RunRuleValidationTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(validationTest);

            // Test 2: Basic execution test
            var executionTest = await RunBasicExecutionTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(executionTest);

            // Test 3: Parameter validation test
            var parameterTest = await RunParameterValidationTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(parameterTest);

            // Test 4: Edge case tests
            var edgeCaseTests = await RunEdgeCaseTestsAsync(rule, cancellationToken);
            testSuite.Tests.AddRange(edgeCaseTests);

            // Test 5: Dependency tests
            var dependencyTest = await RunDependencyTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(dependencyTest);

            testSuite.Success = testSuite.Tests.All(t => t.Success);
            testSuite.PassedTests = testSuite.Tests.Count(t => t.Success);
            testSuite.FailedTests = testSuite.Tests.Count(t => !t.Success);
        }
        catch (Exception ex)
        {
            testSuite.Success = false;
            testSuite.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error running unit tests for rule {RuleId}", ruleId);
        }
        finally
        {
            testSuite.CompletedAt = DateTime.UtcNow;
            testSuite.Duration = testSuite.CompletedAt.Value - testSuite.StartedAt;
        }

        _logger.LogInformation("Completed unit tests for rule {RuleId}. Success: {Success}, Passed: {Passed}, Failed: {Failed}",
            ruleId, testSuite.Success, testSuite.PassedTests, testSuite.FailedTests);

        return testSuite;
    }

    public async Task<RuleTestSuite> RunRuleIntegrationTestsAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Running integration tests for rule {RuleId}", ruleId);

        var testSuite = new RuleTestSuite
        {
            RuleId = ruleId,
            TestType = RuleTestType.Integration,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
            if (rule == null)
            {
                testSuite.Success = false;
                testSuite.ErrorMessage = $"Rule {ruleId} not found";
                return testSuite;
            }

            // Test 1: Database integration test
            var databaseTest = await RunDatabaseIntegrationTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(databaseTest);

            // Test 2: Rule execution engine integration
            var executionEngineTest = await RunExecutionEngineIntegrationTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(executionEngineTest);

            // Test 3: Finding persistence test
            var persistenceTest = await RunFindingPersistenceTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(persistenceTest);

            // Test 4: Cross-rule dependency test
            var crossRuleTest = await RunCrossRuleDependencyTestAsync(rule, cancellationToken);
            testSuite.Tests.Add(crossRuleTest);

            testSuite.Success = testSuite.Tests.All(t => t.Success);
            testSuite.PassedTests = testSuite.Tests.Count(t => t.Success);
            testSuite.FailedTests = testSuite.Tests.Count(t => !t.Success);
        }
        catch (Exception ex)
        {
            testSuite.Success = false;
            testSuite.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error running integration tests for rule {RuleId}", ruleId);
        }
        finally
        {
            testSuite.CompletedAt = DateTime.UtcNow;
            testSuite.Duration = testSuite.CompletedAt.Value - testSuite.StartedAt;
        }

        return testSuite;
    }

    public async Task<RulePerformanceBenchmark> RunRulePerformanceBenchmarkAsync(string ruleId, PerformanceTestOptions options, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Running performance benchmark for rule {RuleId}", ruleId);

        var benchmark = new RulePerformanceBenchmark
        {
            RuleId = ruleId,
            Options = options,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
            if (rule == null)
            {
                benchmark.Success = false;
                benchmark.ErrorMessage = $"Rule {ruleId} not found";
                return benchmark;
            }

            // Generate test data
            var testData = await GenerateTestDataAsync(ruleId, new TestDataGenerationOptions
            {
                UserCount = options.DataSize,
                ProjectCount = Math.Max(1, options.DataSize / 10),
                CompanyCount = Math.Max(1, options.DataSize / 20)
            }, cancellationToken);

            var context = new AuditContext
            {
                AuditRunId = Guid.NewGuid(),
                AccountId = "test-account",
                ProjectIds = testData.ProjectIds,
                UnitOfWork = _unitOfWork,
                Data = testData.TestData
            };

            // Warm-up run
            await rule.ExecuteAsync(context, cancellationToken);

            // Performance measurements
            var executionTimes = new List<TimeSpan>();
            var memoryUsages = new List<long>();

            for (int i = 0; i < options.Iterations; i++)
            {
                var stopwatch = Stopwatch.StartNew();
                var initialMemory = GC.GetTotalMemory(false);

                var result = await rule.ExecuteAsync(context, cancellationToken);

                stopwatch.Stop();
                var finalMemory = GC.GetTotalMemory(false);

                executionTimes.Add(stopwatch.Elapsed);
                memoryUsages.Add(finalMemory - initialMemory);

                benchmark.Results.Add(new PerformanceTestResult
                {
                    Iteration = i + 1,
                    ExecutionTime = stopwatch.Elapsed,
                    MemoryUsage = finalMemory - initialMemory,
                    FindingsGenerated = result.Findings.Count,
                    Success = result.Success
                });
            }

            // Calculate statistics
            benchmark.Statistics = new PerformanceStatistics
            {
                AverageExecutionTime = TimeSpan.FromTicks((long)executionTimes.Average(t => t.Ticks)),
                MinExecutionTime = executionTimes.Min(),
                MaxExecutionTime = executionTimes.Max(),
                AverageMemoryUsage = (long)memoryUsages.Average(),
                MinMemoryUsage = memoryUsages.Min(),
                MaxMemoryUsage = memoryUsages.Max(),
                TotalIterations = options.Iterations,
                SuccessfulIterations = benchmark.Results.Count(r => r.Success)
            };

            benchmark.Success = true;
        }
        catch (Exception ex)
        {
            benchmark.Success = false;
            benchmark.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error running performance benchmark for rule {RuleId}", ruleId);
        }
        finally
        {
            benchmark.CompletedAt = DateTime.UtcNow;
            benchmark.Duration = benchmark.CompletedAt.Value - benchmark.StartedAt;
        }

        return benchmark;
    }

    public async Task<RuleValidationReport> ValidateRuleAsync(string ruleId, List<RuleTestScenario> scenarios, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Validating rule {RuleId} against {ScenarioCount} scenarios", ruleId, scenarios.Count);

        var report = new RuleValidationReport
        {
            RuleId = ruleId,
            StartedAt = DateTime.UtcNow,
            TotalScenarios = scenarios.Count
        };

        try
        {
            var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
            if (rule == null)
            {
                report.Success = false;
                report.ErrorMessage = $"Rule {ruleId} not found";
                return report;
            }

            foreach (var scenario in scenarios)
            {
                var scenarioResult = await ValidateRuleScenarioAsync(rule, scenario, cancellationToken);
                report.ScenarioResults.Add(scenarioResult);

                if (scenarioResult.Success)
                {
                    report.PassedScenarios++;
                }
                else
                {
                    report.FailedScenarios++;
                }
            }

            report.Success = report.FailedScenarios == 0;
        }
        catch (Exception ex)
        {
            report.Success = false;
            report.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error validating rule {RuleId}", ruleId);
        }
        finally
        {
            report.CompletedAt = DateTime.UtcNow;
            report.Duration = report.CompletedAt.Value - report.StartedAt;
        }

        return report;
    }

    public async Task<ComprehensiveTestReport> RunComprehensiveTestSuiteAsync(RuleTestOptions options, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Running comprehensive test suite");

        var report = new ComprehensiveTestReport
        {
            Options = options,
            StartedAt = DateTime.UtcNow
        };

        try
        {
            var rules = await _ruleRegistry.GetAllRulesAsync(cancellationToken);
            var rulesToTest = rules.Where(r => r.IsEnabled).ToList();

            if (options.RuleIds?.Any() == true)
            {
                rulesToTest = rulesToTest.Where(r => options.RuleIds.Contains(r.RuleId)).ToList();
            }

            report.TotalRules = rulesToTest.Count;

            foreach (var rule in rulesToTest)
            {
                var ruleReport = new RuleTestReport
                {
                    RuleId = rule.RuleId,
                    RuleName = rule.Name
                };

                // Run unit tests
                if (options.IncludeUnitTests)
                {
                    ruleReport.UnitTestSuite = await RunRuleUnitTestsAsync(rule.RuleId, cancellationToken);
                }

                // Run integration tests
                if (options.IncludeIntegrationTests)
                {
                    ruleReport.IntegrationTestSuite = await RunRuleIntegrationTestsAsync(rule.RuleId, cancellationToken);
                }

                // Run performance benchmarks
                if (options.IncludePerformanceTests)
                {
                    ruleReport.PerformanceBenchmark = await RunRulePerformanceBenchmarkAsync(rule.RuleId, options.PerformanceOptions, cancellationToken);
                }

                ruleReport.OverallSuccess = 
                    (ruleReport.UnitTestSuite?.Success ?? true) &&
                    (ruleReport.IntegrationTestSuite?.Success ?? true) &&
                    (ruleReport.PerformanceBenchmark?.Success ?? true);

                report.RuleReports[rule.RuleId] = ruleReport;

                if (ruleReport.OverallSuccess)
                {
                    report.PassedRules++;
                }
                else
                {
                    report.FailedRules++;
                }
            }

            report.Success = report.FailedRules == 0;
        }
        catch (Exception ex)
        {
            report.Success = false;
            report.ErrorMessage = ex.Message;
            _logger.LogError(ex, "Error running comprehensive test suite");
        }
        finally
        {
            report.CompletedAt = DateTime.UtcNow;
            report.Duration = report.CompletedAt.Value - report.StartedAt;
        }

        return report;
    }

    public async Task<RuleTestData> GenerateTestDataAsync(string ruleId, TestDataGenerationOptions options, CancellationToken cancellationToken = default)
    {
        _logger.LogDebug("Generating test data for rule {RuleId}", ruleId);

        var testData = new RuleTestData
        {
            AccountId = "test-account-" + Guid.NewGuid().ToString("N")[..8],
            ProjectIds = Enumerable.Range(1, options.ProjectCount)
                .Select(_ => Guid.NewGuid().ToString())
                .ToList()
        };

        // Generate test users
        var users = new List<object>();
        for (int i = 0; i < options.UserCount; i++)
        {
            users.Add(new
            {
                Id = Guid.NewGuid(),
                Email = $"testuser{i}@example.com",
                Name = $"Test User {i}",
                Status = i % 10 == 0 ? "Inactive" : "Active", // 10% inactive users
                LastSeenAt = i % 5 == 0 ? (DateTime?)null : DateTime.UtcNow.AddDays(-Random.Shared.Next(1, 365)),
                CreatedAt = DateTime.UtcNow.AddDays(-Random.Shared.Next(30, 730))
            });
        }

        // Generate test companies
        var companies = new List<object>();
        for (int i = 0; i < options.CompanyCount; i++)
        {
            companies.Add(new
            {
                Id = Guid.NewGuid(),
                Name = $"Test Company {i}",
                Status = "Active"
            });
        }

        // Generate test roles and permissions
        var roles = new List<object>();
        var permissions = new List<object>();
        
        for (int i = 0; i < options.UserCount / 2; i++)
        {
            roles.Add(new
            {
                Id = Guid.NewGuid(),
                Name = $"Test Role {i}",
                ProjectId = testData.ProjectIds[Random.Shared.Next(testData.ProjectIds.Count)]
            });
        }

        testData.TestData["Users"] = users;
        testData.TestData["Companies"] = companies;
        testData.TestData["Roles"] = roles;
        testData.TestData["Permissions"] = permissions;

        return testData;
    }

    public async Task<List<RuleTestScenario>> CreateTestScenariosAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        var scenarios = new List<RuleTestScenario>();

        var rule = await _ruleRegistry.GetRuleAsync(ruleId, cancellationToken);
        if (rule == null)
        {
            return scenarios;
        }

        // Create scenarios based on rule type and category
        switch (rule.Category)
        {
            case AuditRuleCategory.Security:
                scenarios.AddRange(CreateSecurityRuleScenarios(ruleId));
                break;
            case AuditRuleCategory.Compliance:
                scenarios.AddRange(CreateComplianceRuleScenarios(ruleId));
                break;
            default:
                scenarios.AddRange(CreateGenericRuleScenarios(ruleId));
                break;
        }

        return scenarios;
    }

    private List<RuleTestScenario> CreateSecurityRuleScenarios(string ruleId)
    {
        return new List<RuleTestScenario>
        {
            new RuleTestScenario
            {
                Id = $"{ruleId}-security-positive",
                Name = "Security violation detected",
                Description = "Should detect security violations",
                ExpectedFindingCount = 1,
                ExpectedSeverity = AuditSeverity.High
            },
            new RuleTestScenario
            {
                Id = $"{ruleId}-security-negative",
                Name = "No security violations",
                Description = "Should not detect violations when none exist",
                ExpectedFindingCount = 0
            }
        };
    }

    private List<RuleTestScenario> CreateComplianceRuleScenarios(string ruleId)
    {
        return new List<RuleTestScenario>
        {
            new RuleTestScenario
            {
                Id = $"{ruleId}-compliance-violation",
                Name = "Compliance violation detected",
                Description = "Should detect compliance violations",
                ExpectedFindingCount = 1,
                ExpectedSeverity = AuditSeverity.Medium
            }
        };
    }

    private List<RuleTestScenario> CreateGenericRuleScenarios(string ruleId)
    {
        return new List<RuleTestScenario>
        {
            new RuleTestScenario
            {
                Id = $"{ruleId}-basic",
                Name = "Basic rule execution",
                Description = "Should execute without errors",
                ExpectedFindingCount = 0
            }
        };
    }

    // Helper methods for individual tests
    private async Task<RuleTest> RunRuleValidationTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        var test = new RuleTest
        {
            Name = "Rule Validation Test",
            Description = "Validates that the rule configuration is valid"
        };

        try
        {
            var validationResult = await rule.ValidateAsync(cancellationToken);
            test.Success = validationResult.IsValid;
            test.ErrorMessage = validationResult.IsValid ? null : string.Join(", ", validationResult.Errors);
        }
        catch (Exception ex)
        {
            test.Success = false;
            test.ErrorMessage = ex.Message;
        }

        return test;
    }

    private async Task<RuleTest> RunBasicExecutionTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        var test = new RuleTest
        {
            Name = "Basic Execution Test",
            Description = "Tests basic rule execution with minimal data"
        };

        try
        {
            var context = new AuditContext
            {
                AuditRunId = Guid.NewGuid(),
                AccountId = "test-account",
                ProjectIds = new List<string> { "test-project" },
                UnitOfWork = _unitOfWork
            };

            var result = await rule.ExecuteAsync(context, cancellationToken);
            test.Success = result.Success;
            test.ErrorMessage = result.ErrorMessage;
        }
        catch (Exception ex)
        {
            test.Success = false;
            test.ErrorMessage = ex.Message;
        }

        return test;
    }

    private async Task<RuleTest> RunParameterValidationTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        var test = new RuleTest
        {
            Name = "Parameter Validation Test",
            Description = "Tests rule parameter validation"
        };

        try
        {
            // Test with invalid parameters if rule has any
            test.Success = true; // Placeholder - would implement actual parameter testing
        }
        catch (Exception ex)
        {
            test.Success = false;
            test.ErrorMessage = ex.Message;
        }

        return test;
    }

    private async Task<List<RuleTest>> RunEdgeCaseTestsAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        var tests = new List<RuleTest>();

        // Null data test
        tests.Add(new RuleTest
        {
            Name = "Null Data Test",
            Description = "Tests rule behavior with null/empty data",
            Success = true // Placeholder
        });

        // Large data test
        tests.Add(new RuleTest
        {
            Name = "Large Data Test",
            Description = "Tests rule behavior with large datasets",
            Success = true // Placeholder
        });

        return tests;
    }

    private async Task<RuleTest> RunDependencyTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        var test = new RuleTest
        {
            Name = "Dependency Test",
            Description = "Tests rule dependencies"
        };

        try
        {
            var dependencies = rule.GetDependencies();
            test.Success = true; // Placeholder - would test actual dependencies
        }
        catch (Exception ex)
        {
            test.Success = false;
            test.ErrorMessage = ex.Message;
        }

        return test;
    }

    private async Task<RuleTest> RunDatabaseIntegrationTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        return new RuleTest
        {
            Name = "Database Integration Test",
            Description = "Tests rule integration with database",
            Success = true // Placeholder
        };
    }

    private async Task<RuleTest> RunExecutionEngineIntegrationTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        return new RuleTest
        {
            Name = "Execution Engine Integration Test",
            Description = "Tests rule integration with execution engine",
            Success = true // Placeholder
        };
    }

    private async Task<RuleTest> RunFindingPersistenceTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        return new RuleTest
        {
            Name = "Finding Persistence Test",
            Description = "Tests that rule findings are properly persisted",
            Success = true // Placeholder
        };
    }

    private async Task<RuleTest> RunCrossRuleDependencyTestAsync(IAuditRule rule, CancellationToken cancellationToken)
    {
        return new RuleTest
        {
            Name = "Cross-Rule Dependency Test",
            Description = "Tests dependencies between rules",
            Success = true // Placeholder
        };
    }

    private async Task<RuleTestScenarioResult> ValidateRuleScenarioAsync(IAuditRule rule, RuleTestScenario scenario, CancellationToken cancellationToken)
    {
        var result = new RuleTestScenarioResult
        {
            ScenarioId = scenario.Id,
            ScenarioName = scenario.Name
        };

        try
        {
            // Execute rule with scenario data
            var context = new AuditContext
            {
                AuditRunId = Guid.NewGuid(),
                AccountId = "test-account",
                ProjectIds = new List<string> { "test-project" },
                UnitOfWork = _unitOfWork,
                Data = scenario.TestData
            };

            var ruleResult = await rule.ExecuteAsync(context, cancellationToken);
            
            result.Success = ruleResult.Success &&
                           ruleResult.Findings.Count == scenario.ExpectedFindingCount &&
                           (scenario.ExpectedSeverity == null || 
                            ruleResult.Findings.All(f => f.Severity == scenario.ExpectedSeverity));

            result.ActualFindingCount = ruleResult.Findings.Count;
            result.ErrorMessage = ruleResult.ErrorMessage;
        }
        catch (Exception ex)
        {
            result.Success = false;
            result.ErrorMessage = ex.Message;
        }

        return result;
    }
}

/// <summary>
/// Test suite for a rule
/// </summary>
public class RuleTestSuite
{
    public string RuleId { get; set; } = string.Empty;
    public RuleTestType TestType { get; set; }
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int PassedTests { get; set; }
    public int FailedTests { get; set; }
    public List<RuleTest> Tests { get; set; } = new();
}

/// <summary>
/// Individual rule test
/// </summary>
public class RuleTest
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public TimeSpan? Duration { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Rule test types
/// </summary>
public enum RuleTestType
{
    Unit,
    Integration,
    Performance,
    Validation
}

/// <summary>
/// Performance benchmark for a rule
/// </summary>
public class RulePerformanceBenchmark
{
    public string RuleId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public PerformanceTestOptions Options { get; set; } = new();
    public PerformanceStatistics Statistics { get; set; } = new();
    public List<PerformanceTestResult> Results { get; set; } = new();
}

/// <summary>
/// Performance test options
/// </summary>
public class PerformanceTestOptions
{
    public int Iterations { get; set; } = 10;
    public int DataSize { get; set; } = 100;
    public TimeSpan Timeout { get; set; } = TimeSpan.FromMinutes(5);
}

/// <summary>
/// Performance test result
/// </summary>
public class PerformanceTestResult
{
    public int Iteration { get; set; }
    public TimeSpan ExecutionTime { get; set; }
    public long MemoryUsage { get; set; }
    public int FindingsGenerated { get; set; }
    public bool Success { get; set; }
}

/// <summary>
/// Performance statistics
/// </summary>
public class PerformanceStatistics
{
    public TimeSpan AverageExecutionTime { get; set; }
    public TimeSpan MinExecutionTime { get; set; }
    public TimeSpan MaxExecutionTime { get; set; }
    public long AverageMemoryUsage { get; set; }
    public long MinMemoryUsage { get; set; }
    public long MaxMemoryUsage { get; set; }
    public int TotalIterations { get; set; }
    public int SuccessfulIterations { get; set; }
}

/// <summary>
/// Rule validation report
/// </summary>
public class RuleValidationReport
{
    public string RuleId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int TotalScenarios { get; set; }
    public int PassedScenarios { get; set; }
    public int FailedScenarios { get; set; }
    public List<RuleTestScenarioResult> ScenarioResults { get; set; } = new();
}

/// <summary>
/// Test scenario for rule validation
/// </summary>
public class RuleTestScenario
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public int ExpectedFindingCount { get; set; }
    public AuditSeverity? ExpectedSeverity { get; set; }
    public Dictionary<string, object> TestData { get; set; } = new();
}

/// <summary>
/// Result of a test scenario
/// </summary>
public class RuleTestScenarioResult
{
    public string ScenarioId { get; set; } = string.Empty;
    public string ScenarioName { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public int ActualFindingCount { get; set; }
    public TimeSpan? Duration { get; set; }
}

/// <summary>
/// Comprehensive test report
/// </summary>
public class ComprehensiveTestReport
{
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime StartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public TimeSpan? Duration { get; set; }
    public int TotalRules { get; set; }
    public int PassedRules { get; set; }
    public int FailedRules { get; set; }
    public RuleTestOptions Options { get; set; } = new();
    public Dictionary<string, RuleTestReport> RuleReports { get; set; } = new();
}

/// <summary>
/// Test report for a single rule
/// </summary>
public class RuleTestReport
{
    public string RuleId { get; set; } = string.Empty;
    public string RuleName { get; set; } = string.Empty;
    public bool OverallSuccess { get; set; }
    public RuleTestSuite? UnitTestSuite { get; set; }
    public RuleTestSuite? IntegrationTestSuite { get; set; }
    public RulePerformanceBenchmark? PerformanceBenchmark { get; set; }
}

/// <summary>
/// Options for rule testing
/// </summary>
public class RuleTestOptions
{
    public List<string>? RuleIds { get; set; }
    public bool IncludeUnitTests { get; set; } = true;
    public bool IncludeIntegrationTests { get; set; } = true;
    public bool IncludePerformanceTests { get; set; } = false;
    public PerformanceTestOptions PerformanceOptions { get; set; } = new();
    public bool StopOnFirstFailure { get; set; } = false;
}

/// <summary>
/// Options for test data generation
/// </summary>
public class TestDataGenerationOptions
{
    public int UserCount { get; set; } = 100;
    public int ProjectCount { get; set; } = 10;
    public int CompanyCount { get; set; } = 5;
    public int RoleCount { get; set; } = 20;
    public int PermissionCount { get; set; } = 50;
    public bool IncludeInactiveUsers { get; set; } = true;
    public bool IncludeTestData { get; set; } = true;
}
