{"Version": 1, "Hash": "K8UMJBRN8z1dVUUl/1fwBqqbtiywbAULezUMyOqMvGA=", "Source": "AccAuditTool.Web", "BasePath": "_content/AccAuditTool.Web", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "AccAuditTool.Web\\wwwroot", "Source": "AccAuditTool.Web", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "blazorise.bootstrap5.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "i7bnanqjdz", "Integrity": "fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "FileLength": 87508, "LastWriteTime": "2024-01-29T08:40:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "blazorise.bootstrap5.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bma77fm84a", "Integrity": "fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "FileLength": 70296, "LastWriteTime": "2024-01-29T08:40:56+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "modal.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qmfpgx199h", "Integrity": "nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "FileLength": 3939, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "SourceId": "Blazorise.Bootstrap5", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise.Bootstrap5", "RelativePath": "tooltip.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhsggsqt5r", "Integrity": "0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "FileLength": 421, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "blazorise.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "w2rkbj284c", "Integrity": "GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "FileLength": 67982, "LastWriteTime": "2024-01-24T12:35:00+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "blazorise.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "czkd00wxo8", "Integrity": "eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "FileLength": 60000, "LastWriteTime": "2024-01-24T12:35:00+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "breakpoint.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "id0t3r0t65", "Integrity": "JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "FileLength": 2379, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "button.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "82e040z584", "Integrity": "MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "FileLength": 949, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "closable.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fhfndkf66a", "Integrity": "Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "FileLength": 5188, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "colorPicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ud6cjn8zgl", "Integrity": "rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "FileLength": 7047, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "datePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06sjv0489f", "Integrity": "WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "FileLength": 12869, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "dragDrop.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t0yrw3o63r", "Integrity": "Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "FileLength": 2440, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "dropdown.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6px1262uhf", "Integrity": "2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "FileLength": 1372, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "fileEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kj461f6oua", "Integrity": "gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "FileLength": 5702, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "filePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iqtkjq5rwl", "Integrity": "doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "FileLength": 2247, "LastWriteTime": "2024-01-29T10:41:16+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "floatingUi.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qlnisu95dt", "Integrity": "AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "FileLength": 1681, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "inputMask.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ldvsmunupm", "Integrity": "2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "FileLength": 2604, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "io.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jpt4u823i0", "Integrity": "mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "FileLength": 4775, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "memoEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pdvbj4pezq", "Integrity": "Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "FileLength": 3595, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "numericPicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1ygi1dfwlx", "Integrity": "t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "FileLength": 7352, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "observer.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s1nsmqqjya", "Integrity": "UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "FileLength": 3682, "LastWriteTime": "2023-12-05T11:19:44+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "table.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "oky876bcag", "Integrity": "CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "FileLength": 7739, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "textEdit.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ph79pqpyod", "Integrity": "0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "FileLength": 1722, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "theme.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7mew5qey9y", "Integrity": "/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "FileLength": 1487, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "timePicker.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g5xqvxvlnv", "Integrity": "VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "FileLength": 6161, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "tooltip.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hqt248o0ba", "Integrity": "CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "FileLength": 54020, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "utilities.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnoi3d7w4a", "Integrity": "VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "FileLength": 9501, "LastWriteTime": "2024-01-29T10:41:14+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/DateTimeMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9sjnuohl93", "Integrity": "6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "FileLength": 651, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/NoValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qgmopykj85", "Integrity": "RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "FileLength": 113, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/NumericMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rdf588x9o3", "Integrity": "4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "FileLength": 6408, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "validators/RegExMaskValidator.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4rmwhz9udx", "Integrity": "0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "FileLength": 633, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/autoNumeric.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cs38ynhyrk", "Integrity": "LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "FileLength": 219906, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/Behave.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k77rf775v5", "Integrity": "QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "FileLength": 9317, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/flatpickr.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "edp1606bnc", "Integrity": "TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "FileLength": 62541, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/floating-ui-core.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2wlo7snjy4", "Integrity": "0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "FileLength": 15103, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/floating-ui.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5mbjrq4df3", "Integrity": "cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "FileLength": 10628, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/inputmask.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kogj7cf2d4", "Integrity": "zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "FileLength": 140250, "LastWriteTime": "2024-01-24T12:33:34+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/jsencrypt.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pybrzh7rdh", "Integrity": "UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "FileLength": 55434, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/Pickr.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qovnesw5rb", "Integrity": "1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "FileLength": 27690, "LastWriteTime": "2023-03-07T13:52:50+00:00"}, {"Identity": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "SourceId": "Blazorise", "SourceType": "Package", "ContentRoot": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\", "BasePath": "_content/Blazorise", "RelativePath": "vendors/sha512.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "luevd9t85z", "Integrity": "E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "FileLength": 17899, "LastWriteTime": "2023-08-23T08:02:48+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AccAuditTool.Web.styles.css", "SourceId": "AccAuditTool.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "AccAuditTool.Web#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "x6vvz0kgeb", "Integrity": "qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AccAuditTool.Web.styles.css", "FileLength": 2853, "LastWriteTime": "2025-07-08T21:12:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AccAuditTool.Web.bundle.scp.css", "SourceId": "AccAuditTool.Web", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "AccAuditTool.Web#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "x6vvz0kgeb", "Integrity": "qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AccAuditTool.Web.bundle.scp.css", "FileLength": 2853, "LastWriteTime": "2025-07-08T21:12:18+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/FONT-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "48tmkg660f", "Integrity": "jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\FONT-LICENSE", "FileLength": 4103, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/css/open-iconic-bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cmapd0fi15", "Integrity": "BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "FileLength": 9395, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0uw8dim9nl", "Integrity": "OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "FileLength": 28196, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.otf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wk8x8xm0ah", "Integrity": "sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "FileLength": 20996, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sjnzgf7e1h", "Integrity": "+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "FileLength": 55332, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ll5grcv8wv", "Integrity": "p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "FileLength": 28028, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/font/fonts/open-iconic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h4d0pazwgy", "Integrity": "cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "FileLength": 14984, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/ICON-LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4dwjve0o0b", "Integrity": "aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\ICON-LICENSE", "FileLength": 1093, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\README.md", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/open-iconic/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8h4oiah9s0", "Integrity": "rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\open-iconic\\README.md", "FileLength": 3658, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\site.css", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1fpfb6x0uo", "Integrity": "bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 2810, "LastWriteTime": "2025-07-08T20:50:21+00:00"}, {"Identity": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\favicon.ico", "SourceId": "AccAuditTool.Web", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\", "BasePath": "_content/AccAuditTool.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-08T20:50:21+00:00"}], "Endpoints": [{"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87508"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fv6EZTSDe/dbsGHcGi/OsXGCJ29DS9cgTkAChWURfVs="}]}, {"Route": "_content/Blazorise.Bootstrap5/blazorise.bootstrap5.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\blazorise.bootstrap5.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70296"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 08:40:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fWtOHgoT9ACm656TfnJzwWesenDJ0Gi+rRo/LwZkdao="}]}, {"Route": "_content/Blazorise.Bootstrap5/modal.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\modal.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3939"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nmBocwq71lzZvK8PgW4CmY1Lg0l13vYllZpAszJ5dXI="}]}, {"Route": "_content/Blazorise.Bootstrap5/tooltip.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise.bootstrap5\\1.4.2\\staticwebassets\\tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "421"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0SsaWZFsYhKUyxkY7AORXYg3LsjD5deznxguGQFz1eI="}]}, {"Route": "_content/Blazorise/blazorise.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67982"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GP1G2nhvZ243mLfCXJL8hYi57H8P/HA2fmZrusF70nw="}]}, {"Route": "_content/Blazorise/blazorise.min.css", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\blazorise.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60000"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:35:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eOt59ZYXwX8B9y8l2Se8MEctrFLQUV1RW2bRpVUGEsM="}]}, {"Route": "_content/Blazorise/breakpoint.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\breakpoint.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2379"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JhaI1xXnywN8viG6dIVM6CsVG8f8gNdVIn7XpGEIWh4="}]}, {"Route": "_content/Blazorise/button.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\button.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "949"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MDvFXabuWT407wRcLuQXQdhjsxrQrmKwCmHqBF/2Pv0="}]}, {"Route": "_content/Blazorise/closable.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\closable.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5188"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ixv4O0t21NpbZR6d0ORC1WkQHljTvIS5MBs3lRDN3No="}]}, {"Route": "_content/Blazorise/colorPicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\colorPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7047"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rmUsWEAm0eyXhjFgSQhdy2nHjMvih4MEP6uIc/0M6c0="}]}, {"Route": "_content/Blazorise/datePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\datePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12869"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WNSbnhG3EQd+zgM7AopwQQ9dXZhSepALf9nmYFqRvw0="}]}, {"Route": "_content/Blazorise/dragDrop.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dragDrop.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2440"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Aw+6VFl0UEW2TCK7RkpMkJy6TsblnZduHawm04gwQ1Q="}]}, {"Route": "_content/Blazorise/dropdown.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\dropdown.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1372"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2hobq2IkjtN24UIlFUGCINxX+u6Fgxj/+ezcnblloJo="}]}, {"Route": "_content/Blazorise/fileEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\fileEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5702"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gPlzBplZsScSTrPTARk9meqO3ngwcug+icK06J7yGZE="}]}, {"Route": "_content/Blazorise/filePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\filePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2247"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-doH1FHSo3w/yVZRMV5GNOPiSbN9YpX73mIHvumKyzCU="}]}, {"Route": "_content/Blazorise/floatingUi.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\floatingUi.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1681"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AhQWnKESfGi41a+YG6ZXMcP6I9LvRj/XbBvK3v35tgg="}]}, {"Route": "_content/Blazorise/inputMask.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\inputMask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2604"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2Ys+oINrQx9tepEH1xCtYv+rwiKkA3eYR7RZo7hJ+Os="}]}, {"Route": "_content/Blazorise/io.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\io.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mbKL6XwKZyyrts0POi77EYKAsiuPcX8ZEDw5bwtQtFY="}]}, {"Route": "_content/Blazorise/memoEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\memoEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3595"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Y/lK1dVrt/awy16dDPYfp7y+ovL/RxKAC6rQPqsC31Y="}]}, {"Route": "_content/Blazorise/numericPicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\numericPicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7352"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t/Rbw/26RsfEUW/2BkmSxoTWa53XUgU6bfXrzEsKgSY="}]}, {"Route": "_content/Blazorise/observer.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\observer.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3682"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Dec 2023 11:19:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UL7yfMvnqDrtwAkFBxgGYL+5hCO9VDCJ94lmA5Ry130="}]}, {"Route": "_content/Blazorise/table.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\table.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7739"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CFBEPQJo0UgveddHA+XQ6Yxey/g3l2xDCRUhN7s2yhE="}]}, {"Route": "_content/Blazorise/textEdit.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\textEdit.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1722"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0wC7htxIWOHGD19Yc1tPqX0kn7k1ILicd64Bz7cJu2A="}]}, {"Route": "_content/Blazorise/theme.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\theme.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1487"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/z7kNAcnzllfg/HxEPpf0fnn2IpUACYWcVwVa2oqnsA="}]}, {"Route": "_content/Blazorise/timePicker.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\timePicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6161"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VP39HLpfjrx9sw8Yf/A3FwS1g29R5R8NALQbsYv8CDg="}]}, {"Route": "_content/Blazorise/tooltip.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\tooltip.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54020"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CSdK5FabrdtmiH8JySFriuSwszOxX0U6IpoJVlCLpW8="}]}, {"Route": "_content/Blazorise/utilities.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\utilities.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9501"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8=\""}, {"Name": "Last-Modified", "Value": "Mon, 29 Jan 2024 10:41:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VfSBGhC9/G9Qh7ARVB4Mm9X+hoRfC4vg/yYVXMtVjU8="}]}, {"Route": "_content/Blazorise/validators/DateTimeMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\DateTimeMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "651"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-6VFRpMupxoUE443D30s91C5bFZsQ58DxuiNwbCVxLJk="}]}, {"Route": "_content/Blazorise/validators/NoValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NoValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "113"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RvdrNjHB3Z79Jf1QB/v3rjA4ib41PS+fjetwFdNiJuw="}]}, {"Route": "_content/Blazorise/validators/NumericMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\NumericMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6408"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ar0+fGtxySKLf8iZAlv7UMfP/0rO43/fn6aJjFYscE="}]}, {"Route": "_content/Blazorise/validators/RegExMaskValidator.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\validators\\RegExMaskValidator.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "633"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0JfcdT/AH07dbEAVpuaISjcVIZZmz6JaK0kpd2kRHFM="}]}, {"Route": "_content/Blazorise/vendors/autoNumeric.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\autoNumeric.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "219906"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-LHPx5iVPtgtmcZNDoi0TKQcGpZ/ThGTXvzkGzajnDEo="}]}, {"Route": "_content/Blazorise/vendors/Behave.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Behave.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9317"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QPixC/RhNy0Sx4ntFHFH0iGj3tNiFkhkh/FDWbau6LE="}]}, {"Route": "_content/Blazorise/vendors/flatpickr.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\flatpickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62541"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TuPGTy1RSyKjfSGGz7VXOVYYoW3nhlFnCiVxIxtci88="}]}, {"Route": "_content/Blazorise/vendors/floating-ui-core.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui-core.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15103"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0guaW7kt/WFpp8o6esUyNY5+Wm0/Jk0sgZGfiLlpIV0="}]}, {"Route": "_content/Blazorise/vendors/floating-ui.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\floating-ui.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10628"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cxuZSSJUtLW1W9nVAnm5EiMlDJ34kSIUSNACDrLG6OI="}]}, {"Route": "_content/Blazorise/vendors/inputmask.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\inputmask.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "140250"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs=\""}, {"Name": "Last-Modified", "Value": "Wed, 24 Jan 2024 12:33:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zJ0DpGMxzKalEgH9RIQKYvyut+k6zQYZq2MOwl0ovPs="}]}, {"Route": "_content/Blazorise/vendors/jsencrypt.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\jsencrypt.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55434"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UYwEFy4Dt94x8agDo+K5rAAynsteCUPA/G2UQCHEvyM="}]}, {"Route": "_content/Blazorise/vendors/Pickr.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\Pickr.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "27690"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 07 Mar 2023 13:52:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-1gHexzaXVdeRaNA/1rkPr0sk9Wcyys2XlLgv22akhVM="}]}, {"Route": "_content/Blazorise/vendors/sha512.js", "AssetFile": "C:\\Users\\<USER>\\.nuget\\packages\\blazorise\\1.4.2\\staticwebassets\\vendors\\sha512.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "17899"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Aug 2023 08:02:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+LUjqfR7dS8pbN72SD0gJxBccFzMa7ZTfkInVjDPqU="}]}, {"Route": "AccAuditTool.Web.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AccAuditTool.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 21:12:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o="}]}, {"Route": "AccAuditTool.Web.styles.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AccAuditTool.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 21:12:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o="}]}, {"Route": "AccAuditTool.Web.x6vvz0kgeb.bundle.scp.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\AccAuditTool.Web.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 21:12:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6vvz0kgeb"}, {"Name": "label", "Value": "AccAuditTool.Web.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o="}]}, {"Route": "AccAuditTool.Web.x6vvz0kgeb.styles.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\obj\\Debug\\net8.0\\scopedcss\\bundle\\AccAuditTool.Web.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2853"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 21:12:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x6vvz0kgeb"}, {"Name": "label", "Value": "AccAuditTool.Web.styles.css"}, {"Name": "integrity", "Value": "sha256-qfICzeyxsII6++5NOje4Vd6nIx3F8DRRweWEf3kOH6o="}]}, {"Route": "css/bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "css/bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "css/bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/bootstrap/bootstrap.min.css.map", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "css/open-iconic/FONT-LICENSE", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/FONT-LICENSE.48tmkg660f", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\FONT-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4103"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "48tmkg660f"}, {"Name": "label", "Value": "css/open-iconic/FONT-LICENSE"}, {"Name": "integrity", "Value": "sha256-jA4J4h/k76zVxbFKEaWwFKJccmO0voOQ1DbUW+5YNlI="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.cmapd0fi15.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cmapd0fi15"}, {"Name": "label", "Value": "css/open-iconic/font/css/open-iconic-bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/css/open-iconic-bootstrap.min.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\css\\open-iconic-bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9395"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-BJ/G+e+y7bQdrYkS2RBTyNfBHpA9IuGaPmf9htub5MQ="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.0uw8dim9nl.eot", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0uw8dim9nl"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.eot"}, {"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.eot", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28196"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OK3poGPgzKI2NzNgP07XMbJa3Dz6USoUh/chSkSvQpc="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.h4d0pazwgy.woff", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h4d0pazwgy"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.woff"}, {"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ll5grcv8wv.ttf", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ll5grcv8wv"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.ttf"}, {"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.otf", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.sjnzgf7e1h.svg", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sjnzgf7e1h"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.svg"}, {"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.svg", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "55332"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+P1oQ5jPzOVJGC52E1oxGXIXxxCyMlqy6A9cNxGYzVk="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.ttf", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28028"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p+RP8CV3vRK1YbIkNzq3rPo1jyETPnR07ULb+HVYL8w="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.wk8x8xm0ah.otf", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.otf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20996"}, {"Name": "Content-Type", "Value": "font/otf"}, {"Name": "ETag", "Value": "\"sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wk8x8xm0ah"}, {"Name": "label", "Value": "css/open-iconic/font/fonts/open-iconic.otf"}, {"Name": "integrity", "Value": "sha256-sDUtuZAEzWZyv/U1xl/9D3ehyU69JE+FvAcu5HQ+/a0="}]}, {"Route": "css/open-iconic/font/fonts/open-iconic.woff", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\font\\fonts\\open-iconic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14984"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cZPqVlRJfSNW0KaQ4+UPOXZ/v/QzXlejRDwUNdZIofI="}]}, {"Route": "css/open-iconic/ICON-LICENSE", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/ICON-LICENSE.4dwjve0o0b", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\ICON-LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1093"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4dwjve0o0b"}, {"Name": "label", "Value": "css/open-iconic/ICON-LICENSE"}, {"Name": "integrity", "Value": "sha256-aF5g/izareSj02F3MPSoTGNbcMBl9nmZKDe04zjU/ss="}]}, {"Route": "css/open-iconic/README.8h4oiah9s0.md", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8h4oiah9s0"}, {"Name": "label", "Value": "css/open-iconic/README.md"}, {"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/open-iconic/README.md", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\open-iconic\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3658"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rDb1fXbrDo8/dpt6Gi3UAobONVQv/lE2bB7lGwRQ0jM="}]}, {"Route": "css/site.1fpfb6x0uo.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1fpfb6x0uo"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "css/site.css", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2810"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bQF09ZZOsk0T2q2MpSrJMbqdu0Ks9Ea03LFI7wJyLeU="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "C:\\Users\\<USER>\\Downloads\\ACC User Management\\src\\Web\\AccAuditTool.Web\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 20:50:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}]}