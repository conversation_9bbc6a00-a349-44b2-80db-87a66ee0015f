using AccAuditTool.Infrastructure.Configuration;
using AccAuditTool.Infrastructure.Services;
using FluentAssertions;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Moq.Protected;
using System.Net;
using System.Text.Json;

namespace AccAuditTool.Infrastructure.Tests.Services;

public class DataCollectionEngineTests : IDisposable
{
    private readonly Mock<ILogger<ApsApiClient>> _mockLogger;
    private readonly Mock<IApsAuthenticationService> _mockAuthService;
    private readonly Mock<IRateLimitService> _mockRateLimitService;
    private readonly Mock<HttpMessageHandler> _mockHttpHandler;
    private readonly HttpClient _httpClient;
    private readonly ApsApiOptions _options;
    private readonly ApsApiClient _apiClient;

    public DataCollectionEngineTests()
    {
        _mockLogger = new Mock<ILogger<ApsApiClient>>();
        _mockAuthService = new Mock<IApsAuthenticationService>();
        _mockRateLimitService = new Mock<IRateLimitService>();
        _mockHttpHandler = new Mock<HttpMessageHandler>();
        
        _httpClient = new HttpClient(_mockHttpHandler.Object)
        {
            BaseAddress = new Uri("https://developer.api.autodesk.com")
        };

        _options = new ApsApiOptions
        {
            BaseUrl = "https://developer.api.autodesk.com",
            ClientId = "test-client-id",
            ClientSecret = "test-client-secret",
            RateLimit = new RateLimitOptions
            {
                RequestsPerMinute = 100,
                BurstCapacity = 20
            },
            Retry = new RetryOptions
            {
                MaxRetryAttempts = 3,
                BaseDelayMs = 1000
            }
        };

        var optionsWrapper = Options.Create(_options);
        _apiClient = new ApsApiClient(_httpClient, _mockAuthService.Object, _mockRateLimitService.Object, optionsWrapper, _mockLogger.Object);
    }

    [Fact]
    public async Task GetAsync_WhenSuccessfulResponse_ShouldReturnDeserializedObject()
    {
        // Arrange
        var expectedData = new { id = "test-id", name = "Test Account" };
        var jsonResponse = JsonSerializer.Serialize(expectedData);

        _mockAuthService.Setup(x => x.GetAccessTokenAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-access-token");

        _mockRateLimitService.Setup(x => x.WaitForPermissionAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _mockRateLimitService.Setup(x => x.GetStatus())
            .Returns(new RateLimitStatus { AvailableTokens = 10, MaxTokens = 20 });

        _mockHttpHandler.Protected()
            .Setup<Task<HttpResponseMessage>>("SendAsync", 
                ItExpr.IsAny<HttpRequestMessage>(), 
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(jsonResponse)
            });

        // Act
        var result = await _apiClient.GetAsync<dynamic>("/test/endpoint");

        // Assert
        result.Should().NotBeNull();
        _mockAuthService.Verify(x => x.GetAccessTokenAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockRateLimitService.Verify(x => x.WaitForPermissionAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GetAsync_WhenRateLimited_ShouldWaitAndRetry()
    {
        // Arrange
        _mockAuthService.Setup(x => x.GetAccessTokenAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync("test-access-token");

        _mockRateLimitService.Setup(x => x.WaitForPermissionAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.Delay(100)); // Simulate rate limit wait

        _mockRateLimitService.Setup(x => x.GetStatus())
            .Returns(new RateLimitStatus { AvailableTokens = 0, MaxTokens = 20 });

        var rateLimitResponse = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.TooManyRequests,
            Content = new StringContent("Rate limit exceeded")
        };
        rateLimitResponse.Headers.Add("Retry-After", "60");

        var successResponse = new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent("{\"id\":\"test\"}")
        };

        _mockHttpHandler.Protected()
            .SetupSequence<Task<HttpResponseMessage>>("SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>())
            .ReturnsAsync(rateLimitResponse)
            .ReturnsAsync(successResponse);

        // Act & Assert
        var result = await _apiClient.GetAsync<dynamic>("/test/endpoint");
        result.Should().NotBeNull();
        
        _mockRateLimitService.Verify(x => x.UpdateLimitsFromResponse(It.IsAny<HttpResponseMessage>()), Times.AtLeast(1));
    }

    [Fact]
    public async Task GetAsync_WhenAuthenticationFails_ShouldThrowException()
    {
        // Arrange
        _mockAuthService.Setup(x => x.GetAccessTokenAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Authentication failed"));

        _mockRateLimitService.Setup(x => x.WaitForPermissionAsync(It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _apiClient.GetAsync<dynamic>("/test/endpoint"));
    }

    [Fact]
    public void GetHealthStatus_ShouldReturnCurrentStatus()
    {
        // Arrange
        _mockRateLimitService.Setup(x => x.GetStatus())
            .Returns(new RateLimitStatus 
            { 
                AvailableTokens = 15, 
                MaxTokens = 20,
                CurrentLimit = 100
            });

        // Act
        var health = _apiClient.GetHealthStatus();

        // Assert
        health.Should().NotBeNull();
        health.RateLimitStatus.AvailableTokens.Should().Be(15);
        health.RateLimitStatus.MaxTokens.Should().Be(20);
    }

    public void Dispose()
    {
        _httpClient?.Dispose();
    }
}

public class BatchProcessingServiceTests
{
    private readonly Mock<ILogger<BatchProcessingService>> _mockLogger;
    private readonly BatchProcessingService _batchService;

    public BatchProcessingServiceTests()
    {
        _mockLogger = new Mock<ILogger<BatchProcessingService>>();
        _batchService = new BatchProcessingService(_mockLogger.Object);
    }

    [Fact]
    public async Task ProcessBatchAsync_WithSuccessfulItems_ShouldReturnAllResults()
    {
        // Arrange
        var items = Enumerable.Range(1, 10).ToList();
        var processor = new Func<int, CancellationToken, Task<string>>(async (item, ct) =>
        {
            await Task.Delay(10, ct);
            return $"Processed-{item}";
        });

        var options = new BatchProcessingOptions
        {
            BatchSize = 3,
            MaxDegreeOfParallelism = 2
        };

        // Act
        var result = await _batchService.ProcessBatchAsync(items, processor, options);

        // Assert
        result.Should().NotBeNull();
        result.TotalItems.Should().Be(10);
        result.SuccessfulItems.Should().Be(10);
        result.FailedItems.Should().Be(0);
        result.SuccessRate.Should().Be(100);
        result.SuccessfulResults.Should().HaveCount(10);
        result.SuccessfulResults.Should().AllSatisfy(r => r.Should().StartWith("Processed-"));
    }

    [Fact]
    public async Task ProcessBatchAsync_WithSomeFailures_ShouldContinueProcessing()
    {
        // Arrange
        var items = Enumerable.Range(1, 10).ToList();
        var processor = new Func<int, CancellationToken, Task<string>>(async (item, ct) =>
        {
            await Task.Delay(10, ct);
            if (item % 3 == 0) // Fail every 3rd item
                throw new InvalidOperationException($"Failed item {item}");
            return $"Processed-{item}";
        });

        var options = new BatchProcessingOptions
        {
            BatchSize = 5,
            ContinueOnError = true
        };

        // Act
        var result = await _batchService.ProcessBatchAsync(items, processor, options);

        // Assert
        result.Should().NotBeNull();
        result.TotalItems.Should().Be(10);
        result.SuccessfulItems.Should().Be(7); // 10 - 3 failures (3, 6, 9)
        result.FailedItems.Should().Be(3);
        result.SuccessRate.Should().Be(70);
        result.Errors.Should().HaveCount(3);
        result.Errors.Should().AllSatisfy(e => e.Exception.Should().BeOfType<InvalidOperationException>());
    }

    [Fact]
    public async Task ProcessBatchWithRetryAsync_ShouldRetryFailedItems()
    {
        // Arrange
        var items = new[] { 1, 2, 3, 4, 5 };
        var attemptCounts = new Dictionary<int, int>();
        
        var processor = new Func<int, CancellationToken, Task<string>>(async (item, ct) =>
        {
            attemptCounts[item] = attemptCounts.GetValueOrDefault(item, 0) + 1;
            await Task.Delay(10, ct);
            
            // Fail on first attempt for items 2 and 4
            if ((item == 2 || item == 4) && attemptCounts[item] == 1)
                throw new InvalidOperationException($"First attempt failed for item {item}");
                
            return $"Processed-{item}";
        });

        var options = new BatchProcessingOptions
        {
            MaxRetryAttempts = 2,
            RetryDelay = TimeSpan.FromMilliseconds(50)
        };

        // Act
        var result = await _batchService.ProcessBatchWithRetryAsync(items, processor, options);

        // Assert
        result.Should().NotBeNull();
        result.SuccessfulItems.Should().Be(5);
        result.FailedItems.Should().Be(0);
        attemptCounts[1].Should().Be(1); // No retry needed
        attemptCounts[2].Should().Be(2); // One retry
        attemptCounts[3].Should().Be(1); // No retry needed
        attemptCounts[4].Should().Be(2); // One retry
        attemptCounts[5].Should().Be(1); // No retry needed
    }

    [Fact]
    public async Task ProcessBatchAsync_WithProgress_ShouldReportProgress()
    {
        // Arrange
        var items = Enumerable.Range(1, 20).ToList();
        var progressReports = new List<BatchProgress>();
        var progress = new Progress<BatchProgress>(p => progressReports.Add(p));

        var processor = new Func<int, CancellationToken, Task<string>>(async (item, ct) =>
        {
            await Task.Delay(50, ct);
            return $"Processed-{item}";
        });

        var options = new BatchProcessingOptions
        {
            BatchSize = 5,
            DelayBetweenBatches = TimeSpan.FromMilliseconds(10)
        };

        // Act
        var result = await _batchService.ProcessBatchAsync(items, processor, progress, options);

        // Assert
        result.Should().NotBeNull();
        result.SuccessfulItems.Should().Be(20);
        progressReports.Should().NotBeEmpty();
        progressReports.Last().ProgressPercentage.Should().Be(100);
        progressReports.Should().AllSatisfy(p => p.TotalItems.Should().Be(20));
    }
}
