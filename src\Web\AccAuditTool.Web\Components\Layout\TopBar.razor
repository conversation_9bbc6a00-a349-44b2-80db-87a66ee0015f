@inject IJSRuntime JSRuntime

<div class="top-bar">
    <div class="top-bar-left">
        <button class="btn btn-link mobile-sidebar-toggle d-md-none" type="button" @onclick="ToggleMobileSidebar">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="breadcrumb-container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    @foreach (var crumb in Breadcrumbs)
                    {
                        @if (crumb.IsActive)
                        {
                            <li class="breadcrumb-item active" aria-current="page">@crumb.Text</li>
                        }
                        else
                        {
                            <li class="breadcrumb-item">
                                <a href="@crumb.Url">@crumb.Text</a>
                            </li>
                        }
                    }
                </ol>
            </nav>
        </div>
    </div>

    <div class="top-bar-center">
        <div class="search-container">
            <div class="input-group">
                <span class="input-group-text">
                    <i class="fas fa-search"></i>
                </span>
                <input type="text" 
                       class="form-control" 
                       placeholder="Search audits, findings, rules..." 
                       @bind="SearchQuery"
                       @onkeypress="HandleSearchKeyPress" />
                @if (!string.IsNullOrEmpty(SearchQuery))
                {
                    <button class="btn btn-outline-secondary" type="button" @onclick="ClearSearch">
                        <i class="fas fa-times"></i>
                    </button>
                }
            </div>
        </div>
    </div>

    <div class="top-bar-right">
        <!-- Quick Actions -->
        <div class="quick-actions">
            <button class="btn btn-outline-primary btn-sm" 
                    @onclick="StartQuickAudit"
                    data-bs-toggle="tooltip" 
                    data-bs-placement="bottom" 
                    title="Start Quick Audit">
                <i class="fas fa-play"></i>
                <span class="d-none d-lg-inline">Quick Audit</span>
            </button>
        </div>

        <!-- Notifications -->
        <div class="notifications dropdown">
            <button class="btn btn-link notification-toggle" 
                    type="button" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                <i class="fas fa-bell"></i>
                @if (UnreadNotificationCount > 0)
                {
                    <span class="notification-badge">@UnreadNotificationCount</span>
                }
            </button>
            <div class="dropdown-menu dropdown-menu-end notification-dropdown">
                <div class="dropdown-header">
                    <h6>Notifications</h6>
                    @if (UnreadNotificationCount > 0)
                    {
                        <button class="btn btn-link btn-sm" @onclick="MarkAllAsRead">
                            Mark all as read
                        </button>
                    }
                </div>
                <div class="notification-list">
                    @if (Notifications.Any())
                    {
                        @foreach (var notification in Notifications.Take(5))
                        {
                            <div class="notification-item @(notification.IsRead ? "" : "unread")">
                                <div class="notification-icon">
                                    <i class="@GetNotificationIcon(notification.Type) text-@GetNotificationColor(notification.Type)"></i>
                                </div>
                                <div class="notification-content">
                                    <div class="notification-title">@notification.Title</div>
                                    <div class="notification-message">@notification.Message</div>
                                    <div class="notification-time">@GetRelativeTime(notification.CreatedAt)</div>
                                </div>
                                @if (!notification.IsRead)
                                {
                                    <button class="btn btn-link btn-sm mark-read-btn" @onclick="() => MarkAsRead(notification.Id)">
                                        <i class="fas fa-check"></i>
                                    </button>
                                }
                            </div>
                        }
                    }
                    else
                    {
                        <div class="no-notifications">
                            <i class="fas fa-bell-slash text-muted"></i>
                            <p class="text-muted">No notifications</p>
                        </div>
                    }
                </div>
                @if (Notifications.Count > 5)
                {
                    <div class="dropdown-footer">
                        <a href="/notifications" class="btn btn-link">View all notifications</a>
                    </div>
                }
            </div>
        </div>

        <!-- User Menu -->
        <div class="user-menu dropdown">
            <button class="btn btn-link user-toggle" 
                    type="button" 
                    data-bs-toggle="dropdown" 
                    aria-expanded="false">
                <div class="user-avatar">
                    <img src="@UserAvatarUrl" alt="@CurrentUser.Name" class="avatar-img" />
                </div>
                <span class="user-name d-none d-md-inline">@CurrentUser.Name</span>
                <i class="fas fa-chevron-down"></i>
            </button>
            <div class="dropdown-menu dropdown-menu-end user-dropdown">
                <div class="dropdown-header">
                    <div class="user-info">
                        <div class="user-name">@CurrentUser.Name</div>
                        <div class="user-email">@CurrentUser.Email</div>
                        <div class="user-role">@CurrentUser.Role</div>
                    </div>
                </div>
                <div class="dropdown-divider"></div>
                <a class="dropdown-item" href="/profile">
                    <i class="fas fa-user"></i>
                    Profile
                </a>
                <a class="dropdown-item" href="/settings">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
                <a class="dropdown-item" href="/help">
                    <i class="fas fa-question-circle"></i>
                    Help & Support
                </a>
                <div class="dropdown-divider"></div>
                <button class="dropdown-item" @onclick="Logout">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    private string SearchQuery = string.Empty;
    private int UnreadNotificationCount = 3;
    
    private List<BreadcrumbItem> Breadcrumbs = new()
    {
        new BreadcrumbItem { Text = "Home", Url = "/", IsActive = false },
        new BreadcrumbItem { Text = "Dashboard", Url = "/dashboard", IsActive = true }
    };

    private List<NotificationItem> Notifications = new()
    {
        new NotificationItem 
        { 
            Id = Guid.NewGuid(), 
            Type = "audit_completed", 
            Title = "Audit Completed", 
            Message = "Monthly security audit has been completed successfully", 
            CreatedAt = DateTime.UtcNow.AddMinutes(-30),
            IsRead = false
        },
        new NotificationItem 
        { 
            Id = Guid.NewGuid(), 
            Type = "critical_finding", 
            Title = "Critical Finding", 
            Message = "New critical security finding detected in Project Alpha", 
            CreatedAt = DateTime.UtcNow.AddHours(-2),
            IsRead = false
        },
        new NotificationItem 
        { 
            Id = Guid.NewGuid(), 
            Type = "rule_updated", 
            Title = "Rule Updated", 
            Message = "Security rule SEC-001 has been updated", 
            CreatedAt = DateTime.UtcNow.AddHours(-4),
            IsRead = true
        }
    };

    private UserInfo CurrentUser = new()
    {
        Name = "John Doe",
        Email = "<EMAIL>",
        Role = "Audit Manager"
    };

    private string UserAvatarUrl => $"https://ui-avatars.com/api/?name={Uri.EscapeDataString(CurrentUser.Name)}&background=007bff&color=fff&size=32";

    private async Task ToggleMobileSidebar()
    {
        await JSRuntime.InvokeVoidAsync("toggleMobileSidebar");
    }

    private async Task HandleSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await PerformSearch();
        }
    }

    private async Task PerformSearch()
    {
        if (!string.IsNullOrWhiteSpace(SearchQuery))
        {
            // Navigate to search results page
            // Navigation.NavigateTo($"/search?q={Uri.EscapeDataString(SearchQuery)}");
        }
    }

    private async Task ClearSearch()
    {
        SearchQuery = string.Empty;
    }

    private async Task StartQuickAudit()
    {
        // Navigate to quick audit page or show modal
        // Navigation.NavigateTo("/audits/quick");
    }

    private async Task MarkAsRead(Guid notificationId)
    {
        var notification = Notifications.FirstOrDefault(n => n.Id == notificationId);
        if (notification != null)
        {
            notification.IsRead = true;
            UnreadNotificationCount = Notifications.Count(n => !n.IsRead);
            StateHasChanged();
        }
    }

    private async Task MarkAllAsRead()
    {
        foreach (var notification in Notifications)
        {
            notification.IsRead = true;
        }
        UnreadNotificationCount = 0;
        StateHasChanged();
    }

    private async Task Logout()
    {
        // Implement logout logic
        // await AuthService.LogoutAsync();
        // Navigation.NavigateTo("/login");
    }

    private string GetNotificationIcon(string type)
    {
        return type switch
        {
            "audit_completed" => "fas fa-check-circle",
            "audit_failed" => "fas fa-times-circle",
            "critical_finding" => "fas fa-exclamation-triangle",
            "rule_updated" => "fas fa-cog",
            "user_login" => "fas fa-sign-in-alt",
            "system_alert" => "fas fa-bell",
            _ => "fas fa-info-circle"
        };
    }

    private string GetNotificationColor(string type)
    {
        return type switch
        {
            "audit_completed" => "success",
            "audit_failed" => "danger",
            "critical_finding" => "danger",
            "rule_updated" => "warning",
            "user_login" => "info",
            "system_alert" => "primary",
            _ => "secondary"
        };
    }

    private string GetRelativeTime(DateTime dateTime)
    {
        var timeSpan = DateTime.UtcNow - dateTime;
        
        return timeSpan switch
        {
            { TotalMinutes: < 1 } => "Just now",
            { TotalMinutes: < 60 } => $"{(int)timeSpan.TotalMinutes}m ago",
            { TotalHours: < 24 } => $"{(int)timeSpan.TotalHours}h ago",
            { TotalDays: < 7 } => $"{(int)timeSpan.TotalDays}d ago",
            _ => dateTime.ToString("MMM dd")
        };
    }

    public class BreadcrumbItem
    {
        public string Text { get; set; } = string.Empty;
        public string Url { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    public class NotificationItem
    {
        public Guid Id { get; set; }
        public string Type { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public bool IsRead { get; set; }
    }

    public class UserInfo
    {
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
    }
}

<script>
    window.toggleMobileSidebar = function () {
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.classList.toggle('show');
        }
    };
</script>

<style>
    .top-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        gap: 1rem;
    }

    .top-bar-left {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: 1;
        min-width: 0;
    }

    .top-bar-center {
        flex: 0 0 auto;
        max-width: 400px;
        width: 100%;
    }

    .top-bar-right {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-shrink: 0;
    }

    .mobile-sidebar-toggle {
        color: #6c757d;
        border: none;
        background: none;
        padding: 0.5rem;
    }

    .breadcrumb-container {
        min-width: 0;
    }

    .breadcrumb {
        margin: 0;
        background: none;
        padding: 0;
        font-size: 0.875rem;
    }

    .breadcrumb-item a {
        color: #6c757d;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #495057;
        text-decoration: underline;
    }

    .search-container {
        width: 100%;
    }

    .search-container .input-group-text {
        background-color: #f8f9fa;
        border-color: #ced4da;
        color: #6c757d;
    }

    .search-container .form-control {
        border-left: none;
    }

    .search-container .form-control:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .quick-actions .btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .notification-toggle {
        position: relative;
        color: #6c757d;
        border: none;
        background: none;
        padding: 0.5rem;
        border-radius: 0.375rem;
    }

    .notification-toggle:hover {
        color: #495057;
        background-color: #f8f9fa;
    }

    .notification-badge {
        position: absolute;
        top: 0.25rem;
        right: 0.25rem;
        background-color: #dc3545;
        color: white;
        border-radius: 50%;
        width: 1.25rem;
        height: 1.25rem;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
    }

    .notification-dropdown {
        width: 350px;
        max-height: 400px;
        overflow-y: auto;
    }

    .notification-dropdown .dropdown-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .notification-dropdown .dropdown-header h6 {
        margin: 0;
        font-weight: 600;
    }

    .notification-list {
        max-height: 300px;
        overflow-y: auto;
    }

    .notification-item {
        display: flex;
        align-items: flex-start;
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f8f9fa;
        gap: 0.75rem;
        transition: background-color 0.2s ease;
    }

    .notification-item:hover {
        background-color: #f8f9fa;
    }

    .notification-item.unread {
        background-color: #f0f8ff;
        border-left: 3px solid #0d6efd;
    }

    .notification-icon {
        flex-shrink: 0;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        background-color: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .notification-content {
        flex: 1;
        min-width: 0;
    }

    .notification-title {
        font-weight: 600;
        font-size: 0.875rem;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .notification-message {
        font-size: 0.8rem;
        color: #6c757d;
        line-height: 1.4;
        margin-bottom: 0.25rem;
    }

    .notification-time {
        font-size: 0.75rem;
        color: #adb5bd;
    }

    .mark-read-btn {
        flex-shrink: 0;
        color: #6c757d;
        padding: 0.25rem;
    }

    .no-notifications {
        text-align: center;
        padding: 2rem 1rem;
    }

    .no-notifications i {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .user-toggle {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #495057;
        border: none;
        background: none;
        padding: 0.5rem;
        border-radius: 0.375rem;
    }

    .user-toggle:hover {
        background-color: #f8f9fa;
    }

    .user-avatar {
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
    }

    .avatar-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .user-name {
        font-weight: 500;
        font-size: 0.875rem;
    }

    .user-dropdown {
        width: 250px;
    }

    .user-dropdown .dropdown-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .user-dropdown .user-info .user-name {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.25rem;
    }

    .user-dropdown .user-info .user-email {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .user-dropdown .user-info .user-role {
        font-size: 0.75rem;
        color: #adb5bd;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .user-dropdown .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
    }

    .user-dropdown .dropdown-item i {
        width: 1rem;
        text-align: center;
        color: #6c757d;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .top-bar {
            gap: 0.5rem;
        }

        .top-bar-center {
            max-width: 200px;
        }

        .breadcrumb {
            font-size: 0.8rem;
        }

        .notification-dropdown {
            width: 300px;
        }

        .user-dropdown {
            width: 200px;
        }
    }

    @media (max-width: 576px) {
        .top-bar-center {
            display: none;
        }

        .quick-actions .btn span {
            display: none !important;
        }
    }
</style>
