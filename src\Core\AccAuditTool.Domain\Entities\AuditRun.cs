namespace AccAuditTool.Domain.Entities;

/// <summary>
/// Represents an audit execution run
/// </summary>
public class AuditRun : BaseEntity
{
    /// <summary>
    /// Project this audit run was performed on
    /// </summary>
    public Guid? ProjectId { get; set; }
    public Project? Project { get; set; }

    /// <summary>
    /// Account this audit run was performed on (if account-level audit)
    /// </summary>
    public Guid? AccountId { get; set; }
    public Account? Account { get; set; }

    /// <summary>
    /// Audit configuration used for this run
    /// </summary>
    public Guid AuditConfigurationId { get; set; }
    public AuditConfiguration AuditConfiguration { get; set; } = null!;

    /// <summary>
    /// Audit run status
    /// </summary>
    public AuditRunStatus Status { get; set; } = AuditRunStatus.Running;

    /// <summary>
    /// Audit run start time
    /// </summary>
    public DateTime StartedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Audit run completion time
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// User who initiated the audit run
    /// </summary>
    public string? InitiatedBy { get; set; }

    /// <summary>
    /// Audit run type (Manual, Scheduled, Triggered)
    /// </summary>
    public AuditRunType Type { get; set; } = AuditRunType.Manual;

    /// <summary>
    /// Total number of users analyzed
    /// </summary>
    public int UsersAnalyzed { get; set; }

    /// <summary>
    /// Total number of permissions analyzed
    /// </summary>
    public int PermissionsAnalyzed { get; set; }

    /// <summary>
    /// Total number of findings generated
    /// </summary>
    public int FindingsCount { get; set; }

    /// <summary>
    /// Overall risk score for this audit run
    /// </summary>
    public int OverallRiskScore { get; set; }

    /// <summary>
    /// Error message if audit run failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Audit findings generated by this run
    /// </summary>
    public ICollection<AuditFinding> AuditFindings { get; set; } = new List<AuditFinding>();
}

/// <summary>
/// Audit run status enumeration
/// </summary>
public enum AuditRunStatus
{
    Running,
    Completed,
    Failed,
    Cancelled
}

/// <summary>
/// Audit run type enumeration
/// </summary>
public enum AuditRunType
{
    Manual,
    Scheduled,
    Triggered
}
