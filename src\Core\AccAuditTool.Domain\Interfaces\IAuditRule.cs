using AccAuditTool.Domain.Entities;

namespace AccAuditTool.Domain.Interfaces;

/// <summary>
/// Interface for audit rules that can be executed against ACC data
/// </summary>
public interface IAuditRule
{
    /// <summary>
    /// Unique identifier for the rule
    /// </summary>
    string RuleId { get; }

    /// <summary>
    /// Human-readable name of the rule
    /// </summary>
    string Name { get; }

    /// <summary>
    /// Detailed description of what the rule checks
    /// </summary>
    string Description { get; }

    /// <summary>
    /// Category of the audit rule (Security, Compliance, Performance, etc.)
    /// </summary>
    AuditRuleCategory Category { get; }

    /// <summary>
    /// Severity level of findings from this rule
    /// </summary>
    AuditSeverity Severity { get; }

    /// <summary>
    /// Whether the rule is currently enabled
    /// </summary>
    bool IsEnabled { get; }

    /// <summary>
    /// Configuration parameters for the rule
    /// </summary>
    Dictionary<string, object> Parameters { get; }

    /// <summary>
    /// Execute the audit rule against the provided context
    /// </summary>
    Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate the rule configuration
    /// </summary>
    Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get the dependencies this rule has on other rules or data
    /// </summary>
    IEnumerable<string> GetDependencies();
}

/// <summary>
/// Base class for audit rules with common functionality
/// </summary>
public abstract class BaseAuditRule : IAuditRule
{
    protected BaseAuditRule(string ruleId, string name, string description, AuditRuleCategory category, AuditSeverity severity)
    {
        RuleId = ruleId;
        Name = name;
        Description = description;
        Category = category;
        Severity = severity;
        IsEnabled = true;
        Parameters = new Dictionary<string, object>();
    }

    public string RuleId { get; }
    public string Name { get; }
    public string Description { get; }
    public AuditRuleCategory Category { get; }
    public AuditSeverity Severity { get; }
    public bool IsEnabled { get; protected set; }
    public Dictionary<string, object> Parameters { get; }

    public abstract Task<AuditRuleResult> ExecuteAsync(AuditContext context, CancellationToken cancellationToken = default);

    public virtual Task<RuleValidationResult> ValidateAsync(CancellationToken cancellationToken = default)
    {
        var result = new RuleValidationResult
        {
            IsValid = true,
            RuleId = RuleId
        };

        // Basic validation - can be overridden by derived classes
        if (string.IsNullOrEmpty(RuleId))
        {
            result.IsValid = false;
            result.Errors.Add("Rule ID cannot be empty");
        }

        if (string.IsNullOrEmpty(Name))
        {
            result.IsValid = false;
            result.Errors.Add("Rule name cannot be empty");
        }

        return Task.FromResult(result);
    }

    public virtual IEnumerable<string> GetDependencies()
    {
        return Enumerable.Empty<string>();
    }

    /// <summary>
    /// Helper method to create audit findings
    /// </summary>
    protected AuditFindingData CreateFinding(string title, string description, object? evidence = null, string? recommendation = null)
    {
        return new AuditFindingData
        {
            RuleId = RuleId,
            Title = title,
            Description = description,
            Severity = Severity,
            Evidence = evidence != null ? System.Text.Json.JsonSerializer.Serialize(evidence) : null,
            Recommendation = recommendation,
            DetectedAt = DateTime.UtcNow
        };
    }

    /// <summary>
    /// Helper method to get parameter value with type conversion
    /// </summary>
    protected T GetParameter<T>(string key, T defaultValue = default!)
    {
        if (Parameters.TryGetValue(key, out var value))
        {
            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return defaultValue;
            }
        }
        return defaultValue;
    }
}

/// <summary>
/// Result of executing an audit rule
/// </summary>
public class AuditRuleResult
{
    public string RuleId { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
    public List<AuditFindingData> Findings { get; set; } = new();
    public TimeSpan ExecutionTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Result of validating a rule configuration
/// </summary>
public class RuleValidationResult
{
    public string RuleId { get; set; } = string.Empty;
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
}

/// <summary>
/// Context provided to audit rules during execution
/// </summary>
public class AuditContext
{
    public Guid AuditRunId { get; set; }
    public string AccountId { get; set; } = string.Empty;
    public List<string> ProjectIds { get; set; } = new();
    public DateTime AuditDate { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Parameters { get; set; } = new();
    
    // Data repositories for rule execution
    public IUnitOfWork UnitOfWork { get; set; } = null!;
    
    // Additional context data
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// Data structure for audit findings
/// </summary>
public class AuditFindingData
{
    public string RuleId { get; set; } = string.Empty;
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public AuditSeverity Severity { get; set; }
    public string? Evidence { get; set; }
    public string? Recommendation { get; set; }
    public DateTime DetectedAt { get; set; }
    public string? AffectedEntity { get; set; }
    public string? AffectedEntityId { get; set; }
}

/// <summary>
/// Categories for audit rules
/// </summary>
public enum AuditRuleCategory
{
    Security,
    Compliance,
    Performance,
    DataQuality,
    AccessControl,
    UserManagement,
    ProjectManagement,
    Custom
}

/// <summary>
/// Severity levels for audit findings
/// </summary>
public enum AuditSeverity
{
    Info = 0,
    Low = 1,
    Medium = 2,
    High = 3,
    Critical = 4
}

/// <summary>
/// Categories for audit findings
/// </summary>
public enum AuditFindingCategory
{
    Security,
    Compliance,
    AccessControl,
    DataIntegrity,
    Performance,
    Configuration,
    UserManagement,
    ProjectManagement
}
