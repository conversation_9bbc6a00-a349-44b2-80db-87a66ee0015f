using AccAuditTool.Application.Rules;
using AccAuditTool.Application.Rules.Security;
using AccAuditTool.Application.Rules.Compliance;
using AccAuditTool.Domain.Interfaces;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace AccAuditTool.Application.Services;

/// <summary>
/// Registry for managing audit rules
/// </summary>
public interface IRuleRegistry
{
    /// <summary>
    /// Register a rule in the registry
    /// </summary>
    Task RegisterRuleAsync(IAuditRule rule, CancellationToken cancellationToken = default);

    /// <summary>
    /// Unregister a rule from the registry
    /// </summary>
    Task UnregisterRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get a rule by ID
    /// </summary>
    Task<IAuditRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get all registered rules
    /// </summary>
    Task<IEnumerable<IAuditRule>> GetAllRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Get rules by category
    /// </summary>
    Task<IEnumerable<IAuditRule>> GetRulesByCategoryAsync(AuditRuleCategory category, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get enabled rules only
    /// </summary>
    Task<IEnumerable<IAuditRule>> GetEnabledRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Enable or disable a rule
    /// </summary>
    Task SetRuleEnabledAsync(string ruleId, bool enabled, CancellationToken cancellationToken = default);

    /// <summary>
    /// Reload rules from database
    /// </summary>
    Task ReloadRulesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Validate all registered rules
    /// </summary>
    Task<Dictionary<string, RuleValidationResult>> ValidateAllRulesAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Implementation of rule registry
/// </summary>
public class RuleRegistry : IRuleRegistry
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICustomRuleBuilder _customRuleBuilder;
    private readonly IRuleExpressionEvaluator _expressionEvaluator;
    private readonly ILogger<RuleRegistry> _logger;
    private readonly ConcurrentDictionary<string, IAuditRule> _rules;
    private readonly SemaphoreSlim _reloadSemaphore;

    public RuleRegistry(
        IUnitOfWork unitOfWork,
        ICustomRuleBuilder customRuleBuilder,
        IRuleExpressionEvaluator expressionEvaluator,
        ILogger<RuleRegistry> logger)
    {
        _unitOfWork = unitOfWork;
        _customRuleBuilder = customRuleBuilder;
        _expressionEvaluator = expressionEvaluator;
        _logger = logger;
        _rules = new ConcurrentDictionary<string, IAuditRule>();
        _reloadSemaphore = new SemaphoreSlim(1, 1);
    }

    public async Task RegisterRuleAsync(IAuditRule rule, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Registering rule {RuleId}: {RuleName}", rule.RuleId, rule.Name);

        // Validate the rule
        var validationResult = await rule.ValidateAsync(cancellationToken);
        if (!validationResult.IsValid)
        {
            throw new ArgumentException($"Rule validation failed: {string.Join(", ", validationResult.Errors)}");
        }

        _rules.AddOrUpdate(rule.RuleId, rule, (key, oldValue) => rule);

        _logger.LogInformation("Successfully registered rule {RuleId}", rule.RuleId);
    }

    public async Task UnregisterRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Unregistering rule {RuleId}", ruleId);

        _rules.TryRemove(ruleId, out _);

        _logger.LogInformation("Successfully unregistered rule {RuleId}", ruleId);
    }

    public async Task<IAuditRule?> GetRuleAsync(string ruleId, CancellationToken cancellationToken = default)
    {
        _rules.TryGetValue(ruleId, out var rule);
        return rule;
    }

    public async Task<IEnumerable<IAuditRule>> GetAllRulesAsync(CancellationToken cancellationToken = default)
    {
        return _rules.Values.ToList();
    }

    public async Task<IEnumerable<IAuditRule>> GetRulesByCategoryAsync(AuditRuleCategory category, CancellationToken cancellationToken = default)
    {
        return _rules.Values.Where(r => r.Category == category).ToList();
    }

    public async Task<IEnumerable<IAuditRule>> GetEnabledRulesAsync(CancellationToken cancellationToken = default)
    {
        return _rules.Values.Where(r => r.IsEnabled).ToList();
    }

    public async Task SetRuleEnabledAsync(string ruleId, bool enabled, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Setting rule {RuleId} enabled status to {Enabled}", ruleId, enabled);

        if (_rules.TryGetValue(ruleId, out var rule))
        {
            // For built-in rules, we need to update the enabled status
            if (rule is BaseAuditRule baseRule)
            {
                // This would require making IsEnabled settable in BaseAuditRule
                _logger.LogWarning("Cannot change enabled status of built-in rule {RuleId} at runtime", ruleId);
            }
        }

        // Update in database
        var dbRule = await _unitOfWork.AuditRules.FirstOrDefaultAsync(r => r.RuleId == ruleId, cancellationToken);
        if (dbRule != null)
        {
            dbRule.IsEnabled = enabled;
            dbRule.UpdatedAt = DateTime.UtcNow;
            await _unitOfWork.AuditRules.UpdateAsync(dbRule, cancellationToken);
            await _unitOfWork.SaveChangesAsync(cancellationToken);
        }

        _logger.LogInformation("Successfully updated rule {RuleId} enabled status", ruleId);
    }

    public async Task ReloadRulesAsync(CancellationToken cancellationToken = default)
    {
        await _reloadSemaphore.WaitAsync(cancellationToken);
        try
        {
            _logger.LogInformation("Reloading rules from database");

            _rules.Clear();

            // Load built-in rules
            await LoadBuiltInRulesAsync(cancellationToken);

            // Load custom rules from database
            await LoadCustomRulesAsync(cancellationToken);

            _logger.LogInformation("Successfully reloaded {RuleCount} rules", _rules.Count);
        }
        finally
        {
            _reloadSemaphore.Release();
        }
    }

    public async Task<Dictionary<string, RuleValidationResult>> ValidateAllRulesAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Validating all registered rules");

        var results = new Dictionary<string, RuleValidationResult>();

        foreach (var rule in _rules.Values)
        {
            try
            {
                var validationResult = await rule.ValidateAsync(cancellationToken);
                results[rule.RuleId] = validationResult;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating rule {RuleId}", rule.RuleId);
                results[rule.RuleId] = new RuleValidationResult
                {
                    RuleId = rule.RuleId,
                    IsValid = false,
                    Errors = { $"Validation error: {ex.Message}" }
                };
            }
        }

        var invalidRules = results.Values.Count(r => !r.IsValid);
        _logger.LogInformation("Rule validation completed. {ValidRules} valid, {InvalidRules} invalid", 
            results.Count - invalidRules, invalidRules);

        return results;
    }

    private async Task LoadBuiltInRulesAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Loading built-in rules");

        // Create instances of built-in rules
        var builtInRules = new List<IAuditRule>();

        // Security rules
        try
        {
            var excessivePermissionsRule = new ExcessivePermissionsRule(
                _logger.CreateLogger<ExcessivePermissionsRule>());
            builtInRules.Add(excessivePermissionsRule);

            var inactiveUsersRule = new InactiveUsersRule(
                _logger.CreateLogger<InactiveUsersRule>());
            builtInRules.Add(inactiveUsersRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating security rules");
        }

        // Compliance rules
        try
        {
            var roleSegregationRule = new RoleSegregationRule(
                _logger.CreateLogger<RoleSegregationRule>());
            builtInRules.Add(roleSegregationRule);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating compliance rules");
        }

        // Register all built-in rules
        foreach (var rule in builtInRules)
        {
            try
            {
                await RegisterRuleAsync(rule, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error registering built-in rule {RuleId}", rule.RuleId);
            }
        }

        _logger.LogDebug("Loaded {BuiltInRuleCount} built-in rules", builtInRules.Count);
    }

    private async Task LoadCustomRulesAsync(CancellationToken cancellationToken)
    {
        _logger.LogDebug("Loading custom rules from database");

        try
        {
            var customRuleEntities = await _unitOfWork.AuditRules.FindAsync(
                r => r.RuleType == AuditRuleType.Custom, cancellationToken);

            foreach (var ruleEntity in customRuleEntities)
            {
                try
                {
                    if (string.IsNullOrEmpty(ruleEntity.Definition))
                    {
                        _logger.LogWarning("Custom rule {RuleId} has no definition", ruleEntity.RuleId);
                        continue;
                    }

                    var definition = System.Text.Json.JsonSerializer.Deserialize<CustomRuleDefinition>(ruleEntity.Definition);
                    if (definition == null)
                    {
                        _logger.LogWarning("Failed to deserialize custom rule {RuleId}", ruleEntity.RuleId);
                        continue;
                    }

                    // Update definition with current database values
                    definition.IsEnabled = ruleEntity.IsEnabled;
                    definition.UpdatedAt = ruleEntity.UpdatedAt;

                    var customRule = new CustomAuditRule(definition, _expressionEvaluator, _logger);
                    await RegisterRuleAsync(customRule, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading custom rule {RuleId}", ruleEntity.RuleId);
                }
            }

            _logger.LogDebug("Loaded {CustomRuleCount} custom rules", customRuleEntities.Count());
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading custom rules from database");
        }
    }

    /// <summary>
    /// Initialize the registry with default rules
    /// </summary>
    public async Task InitializeAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Initializing rule registry");

        await ReloadRulesAsync(cancellationToken);

        // Validate all loaded rules
        var validationResults = await ValidateAllRulesAsync(cancellationToken);
        var invalidRules = validationResults.Where(kvp => !kvp.Value.IsValid).ToList();

        if (invalidRules.Any())
        {
            _logger.LogWarning("Found {InvalidRuleCount} invalid rules during initialization", invalidRules.Count);
            foreach (var invalidRule in invalidRules)
            {
                _logger.LogWarning("Invalid rule {RuleId}: {Errors}", 
                    invalidRule.Key, string.Join(", ", invalidRule.Value.Errors));
            }
        }

        _logger.LogInformation("Rule registry initialized with {RuleCount} rules ({ValidCount} valid, {InvalidCount} invalid)",
            _rules.Count, _rules.Count - invalidRules.Count, invalidRules.Count);
    }
}
