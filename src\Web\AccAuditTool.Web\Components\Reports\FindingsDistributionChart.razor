@using AccAuditTool.Domain.Entities
@inject IJSRuntime JSRuntime

<div class="findings-distribution-chart">
    <canvas id="findingsDistChart-@ChartId" width="300" height="300"></canvas>
    
    @if (Data.Any())
    {
        <div class="distribution-legend">
            @foreach (var item in Data.OrderByDescending(d => (int)d.Key))
            {
                <div class="legend-item">
                    <span class="legend-color" style="background-color: @GetSeverityColor(item.Key)"></span>
                    <span class="legend-label">@item.Key</span>
                    <span class="legend-value">@item.Value</span>
                    <span class="legend-percentage">(@(GetPercentage(item.Value))%)</span>
                </div>
            }
        </div>
        
        <div class="distribution-summary">
            <div class="summary-item">
                <span class="summary-label">Total Findings:</span>
                <span class="summary-value">@GetTotalFindings()</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">High Risk:</span>
                <span class="summary-value text-danger">@GetHighRiskCount()</span>
            </div>
        </div>
    }
    else
    {
        <div class="no-data-message">
            <i class="fas fa-chart-pie text-muted"></i>
            <p class="text-muted">No findings data available</p>
        </div>
    }
</div>

@code {
    [Parameter] public Dictionary<AuditSeverity, int> Data { get; set; } = new();
    
    private string ChartId = Guid.NewGuid().ToString("N")[..8];
    private bool ChartInitialized = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender || !ChartInitialized)
        {
            await InitializeChart();
            ChartInitialized = true;
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (ChartInitialized)
        {
            await UpdateChart();
        }
    }

    private async Task InitializeChart()
    {
        if (!Data.Any())
        {
            return;
        }

        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("initializeFindingsDistChart", $"findingsDistChart-{ChartId}", chartData);
    }

    private async Task UpdateChart()
    {
        if (!Data.Any())
        {
            return;
        }

        var chartData = PrepareChartData();
        
        await JSRuntime.InvokeVoidAsync("updateFindingsDistChart", $"findingsDistChart-{ChartId}", chartData);
    }

    private object PrepareChartData()
    {
        var sortedData = Data.OrderByDescending(d => (int)d.Key).ToList();
        
        return new
        {
            labels = sortedData.Select(d => d.Key.ToString()).ToArray(),
            datasets = new[]
            {
                new
                {
                    data = sortedData.Select(d => d.Value).ToArray(),
                    backgroundColor = sortedData.Select(d => GetSeverityColor(d.Key)).ToArray(),
                    borderColor = sortedData.Select(d => GetSeverityBorderColor(d.Key)).ToArray(),
                    borderWidth = 2,
                    hoverOffset = 6
                }
            }
        };
    }

    private string GetSeverityColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#dc3545",
            AuditSeverity.High => "#fd7e14",
            AuditSeverity.Medium => "#ffc107",
            AuditSeverity.Low => "#20c997",
            AuditSeverity.Info => "#0dcaf0",
            _ => "#6c757d"
        };
    }

    private string GetSeverityBorderColor(AuditSeverity severity)
    {
        return severity switch
        {
            AuditSeverity.Critical => "#b02a37",
            AuditSeverity.High => "#e8681a",
            AuditSeverity.Medium => "#e6ac00",
            AuditSeverity.Low => "#1aa179",
            AuditSeverity.Info => "#0bb5d6",
            _ => "#5a6268"
        };
    }

    private int GetTotalFindings()
    {
        return Data.Values.Sum();
    }

    private int GetHighRiskCount()
    {
        return Data.GetValueOrDefault(AuditSeverity.Critical, 0) + 
               Data.GetValueOrDefault(AuditSeverity.High, 0);
    }

    private double GetPercentage(int value)
    {
        var total = GetTotalFindings();
        return total > 0 ? Math.Round((double)value / total * 100, 1) : 0;
    }
}

<script>
    window.initializeFindingsDistChart = function (canvasId, data) {
        const ctx = document.getElementById(canvasId).getContext('2d');
        
        const chart = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false // We'll use custom legend
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    animateScale: true
                }
            }
        });

        // Store chart instance for updates
        window[`chart_${canvasId}`] = chart;
    };

    window.updateFindingsDistChart = function (canvasId, data) {
        const chart = window[`chart_${canvasId}`];
        if (chart) {
            chart.data = data;
            chart.update();
        }
    };
</script>

<style>
    .findings-distribution-chart {
        position: relative;
        height: 300px;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .findings-distribution-chart canvas {
        max-height: 180px;
        margin-bottom: 1rem;
    }

    .distribution-legend {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.8rem;
    }

    .legend-color {
        width: 12px;
        height: 12px;
        border-radius: 2px;
        flex-shrink: 0;
    }

    .legend-label {
        flex: 1;
        font-weight: 500;
        color: #495057;
    }

    .legend-value {
        font-weight: 600;
        color: #495057;
        min-width: 30px;
        text-align: right;
    }

    .legend-percentage {
        font-size: 0.75rem;
        color: #6c757d;
        min-width: 40px;
        text-align: right;
    }

    .distribution-summary {
        border-top: 1px solid #f1f3f4;
        padding-top: 0.75rem;
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.8rem;
    }

    .summary-label {
        color: #6c757d;
        font-weight: 500;
    }

    .summary-value {
        font-weight: 600;
        color: #495057;
    }

    .no-data-message {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        gap: 0.75rem;
    }

    .no-data-message i {
        font-size: 2.5rem;
    }

    .no-data-message p {
        margin: 0;
        font-size: 0.875rem;
    }
</style>
